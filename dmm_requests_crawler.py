#!/usr/bin/env python3
"""
DMM Requests爬虫 - 完全避免浏览器问题
"""
import requests
import sqlite3
import time
import re
from datetime import datetime
from typing import Dict, List, Optional
from bs4 import BeautifulSoup
import json

class DMMRequestsCrawler:
    """DMM Requests爬虫 - 不使用浏览器"""
    
    def __init__(self, db_file: str = "dmm_requests_database.db"):
        self.db_file = db_file
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        # 初始化数据库
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        print("📊 初始化数据库...")
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dmm_works (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cid TEXT UNIQUE NOT NULL,
                    title TEXT,
                    detail_url TEXT,
                    page_number INTEGER,
                    crawl_time TEXT,
                    UNIQUE(cid)
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ 数据库初始化完成")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
    
    def handle_age_verification(self):
        """处理年龄验证"""
        print("🔐 处理年龄验证...")
        
        try:
            # 访问年龄验证页面
            age_check_url = "https://www.dmm.co.jp/age_check/=/declared=yes/?rurl=https%3A%2F%2Fvideo.dmm.co.jp%2F"
            response = self.session.get(age_check_url, timeout=15)
            
            if response.status_code == 200:
                print("✅ 年龄验证完成")
                return True
            else:
                print(f"⚠️ 年龄验证响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 年龄验证失败: {e}")
            return False
    
    def extract_cid_from_url(self, url: str) -> Optional[str]:
        """从URL中提取CID"""
        try:
            cid_match = re.search(r'cid=([^&/]+)', url)
            if cid_match:
                return cid_match.group(1)
            return None
        except:
            return None
    
    def try_api_endpoints(self, page_number: int) -> List[Dict]:
        """尝试不同的API接口"""

        # 可能的API接口列表
        api_endpoints = [
            # Next.js API路由
            f"https://video.dmm.co.jp/api/av/list?page={page_number}",
            f"https://video.dmm.co.jp/_next/data/*/av/list.json?page={page_number}",

            # 传统API接口
            f"https://api.dmm.co.jp/affiliate/v3/ItemList?api_id=test&affiliate_id=test&site=FANZA&service=digital&floor=videoa&hits=120&offset={(page_number-1)*120}&output=json",

            # 内部API
            f"https://www.dmm.co.jp/api/v1/videoa/list?page={page_number}",
            f"https://video.dmm.co.jp/api/v1/list?page={page_number}",

            # GraphQL可能性
            "https://video.dmm.co.jp/graphql",
        ]

        for api_url in api_endpoints:
            try:
                print(f"      🔍 尝试API: {api_url}")

                if "graphql" in api_url:
                    # GraphQL查询
                    query = {
                        "query": f"""
                        query {{
                            videoList(page: {page_number}, limit: 120) {{
                                items {{
                                    cid
                                    title
                                    url
                                }}
                            }}
                        }}
                        """
                    }
                    response = self.session.post(api_url, json=query, timeout=15)
                else:
                    response = self.session.get(api_url, timeout=15)

                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"         ✅ API响应成功: {len(str(data))} 字符")

                        # 保存API响应用于分析
                        with open(f'debug_api_response_{page_number}.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)

                        # 尝试解析数据
                        works_data = self.parse_api_response(data, page_number)
                        if works_data:
                            print(f"         🎯 成功解析到 {len(works_data)} 个作品")
                            return works_data

                    except json.JSONDecodeError:
                        print(f"         ❌ 非JSON响应")
                        continue
                else:
                    print(f"         ❌ API失败: {response.status_code}")

            except Exception as e:
                print(f"         ❌ API异常: {str(e)[:50]}...")
                continue

        return []

    def parse_api_response(self, data: dict, page_number: int) -> List[Dict]:
        """解析API响应数据"""
        works_data = []

        try:
            # 尝试不同的数据结构
            possible_paths = [
                ['data', 'items'],
                ['items'],
                ['result', 'items'],
                ['data', 'videoList', 'items'],
                ['videos'],
                ['products'],
            ]

            items = None
            for path in possible_paths:
                temp_data = data
                try:
                    for key in path:
                        temp_data = temp_data[key]
                    if isinstance(temp_data, list) and len(temp_data) > 0:
                        items = temp_data
                        print(f"         📊 找到数据路径: {' -> '.join(path)}")
                        break
                except (KeyError, TypeError):
                    continue

            if items:
                for item in items:
                    try:
                        # 尝试提取CID
                        cid = None
                        for cid_key in ['cid', 'content_id', 'id', 'product_id']:
                            if cid_key in item:
                                cid = item[cid_key]
                                break

                        if cid:
                            work_info = {
                                'cid': str(cid),
                                'page_number': page_number,
                                'crawl_time': datetime.now().isoformat()
                            }

                            # 尝试提取其他信息
                            if 'title' in item:
                                work_info['title'] = item['title']
                            if 'url' in item:
                                work_info['detail_url'] = item['url']
                            elif 'link' in item:
                                work_info['detail_url'] = item['link']
                            else:
                                work_info['detail_url'] = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/"

                            works_data.append(work_info)

                    except Exception as e:
                        continue

        except Exception as e:
            print(f"         ❌ 解析异常: {e}")

        return works_data

    def crawl_single_page(self, page_number: int) -> List[Dict]:
        """爬取单页数据"""
        print(f"   🔍 爬取第 {page_number} 页")

        # 方法1: 尝试API接口
        print(f"      🚀 尝试API接口...")
        works_data = self.try_api_endpoints(page_number)

        if works_data:
            return works_data

        # 方法2: 尝试解析HTML中的JavaScript数据
        print(f"      🔍 尝试解析页面JavaScript数据...")
        works_data = self.extract_from_html_js(page_number)

        if works_data:
            return works_data

        # 方法3: 传统HTML解析（已知会失败，但保留作为备用）
        print(f"      📄 尝试传统HTML解析...")
        return self.crawl_html_page(page_number)

    def extract_from_html_js(self, page_number: int) -> List[Dict]:
        """从HTML页面的JavaScript中提取数据"""
        url = f"https://video.dmm.co.jp/av/list/?page={page_number}"

        try:
            response = self.session.get(url, timeout=30)

            if response.status_code == 200:
                html_content = response.text

                # 查找JavaScript中的数据
                # Next.js通常在__NEXT_DATA__中存储数据

                # 查找Next.js流式数据 - 修复正则表达式
                # 使用更精确的匹配，处理转义字符
                next_f_pattern = r'self\.__next_f\.push\(\[1,"((?:[^"\\]|\\.)*)"\]\)'
                next_f_matches = re.findall(next_f_pattern, html_content)
                if next_f_matches:
                    print(f"      ✅ 找到Next.js流式数据 ({len(next_f_matches)} 个片段)")

                    # 尝试解析流式数据
                    works_data = self.parse_next_f_data(next_f_matches, page_number)
                    if works_data:
                        return works_data

                # 查找传统的__NEXT_DATA__
                next_data_match = re.search(r'__NEXT_DATA__\s*=\s*({.*?});', html_content, re.DOTALL)
                if next_data_match:
                    try:
                        next_data = json.loads(next_data_match.group(1))
                        print(f"      ✅ 找到__NEXT_DATA__")

                        # 保存数据用于分析
                        with open(f'debug_next_data_{page_number}.json', 'w', encoding='utf-8') as f:
                            json.dump(next_data, f, ensure_ascii=False, indent=2)

                        # 尝试解析数据
                        works_data = self.parse_next_data(next_data, page_number)
                        if works_data:
                            return works_data

                    except json.JSONDecodeError:
                        print(f"      ❌ __NEXT_DATA__解析失败")

                # 查找其他可能的数据结构
                js_patterns = [
                    r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                    r'window\.initialData\s*=\s*({.*?});',
                    r'var\s+videoData\s*=\s*({.*?});',
                    r'const\s+items\s*=\s*(\[.*?\]);',
                ]

                for pattern in js_patterns:
                    match = re.search(pattern, html_content, re.DOTALL)
                    if match:
                        try:
                            data = json.loads(match.group(1))
                            print(f"      ✅ 找到JavaScript数据")
                            works_data = self.parse_api_response(data, page_number)
                            if works_data:
                                return works_data
                        except json.JSONDecodeError:
                            continue

        except Exception as e:
            print(f"      ❌ JavaScript解析异常: {e}")

        return []

    def parse_next_data(self, next_data: dict, page_number: int) -> List[Dict]:
        """解析Next.js的__NEXT_DATA__"""
        works_data = []

        try:
            # Next.js数据通常在props.pageProps中
            page_props = next_data.get('props', {}).get('pageProps', {})

            # 尝试不同的数据路径
            possible_keys = ['items', 'videos', 'products', 'data', 'list', 'results']

            for key in possible_keys:
                if key in page_props:
                    items = page_props[key]
                    if isinstance(items, list) and len(items) > 0:
                        print(f"         📊 在pageProps.{key}中找到 {len(items)} 个项目")

                        for item in items:
                            if isinstance(item, dict):
                                cid = item.get('cid') or item.get('id') or item.get('content_id')
                                if cid:
                                    work_info = {
                                        'cid': str(cid),
                                        'page_number': page_number,
                                        'crawl_time': datetime.now().isoformat(),
                                        'detail_url': f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/"
                                    }

                                    if 'title' in item:
                                        work_info['title'] = item['title']

                                    works_data.append(work_info)

                        if works_data:
                            return works_data

        except Exception as e:
            print(f"         ❌ Next.js数据解析异常: {e}")

        return works_data

    def parse_next_f_data(self, next_f_matches: List[str], page_number: int) -> List[Dict]:
        """解析Next.js流式数据"""
        works_data = []

        try:
            # 保存原始流式数据用于分析
            with open(f'debug_next_f_data_{page_number}.txt', 'w', encoding='utf-8') as f:
                for i, match in enumerate(next_f_matches):
                    f.write(f"=== 片段 {i+1} ===\n")
                    f.write(match + "\n\n")

            print(f"         📄 流式数据已保存到 debug_next_f_data_{page_number}.txt")

            # 尝试从流式数据中提取作品信息
            for match in next_f_matches:
                try:
                    # 解码转义字符
                    decoded_data = match.replace('\\n', '\n').replace('\\"', '"').replace('\\\\', '\\')

                    # 查找可能的CID模式 - 增强版
                    cid_patterns = [
                        # JSON格式的CID
                        r'"cid":"([^"]+)"',
                        r'"content_id":"([^"]+)"',
                        r'"id":"([^"]+)"',
                        r'"product_id":"([^"]+)"',

                        # URL中的CID
                        r'cid=([a-zA-Z0-9]+)',
                        r'/detail/=/cid=([^/&"]+)/',
                        r'/cid=([^/&"]+)/',

                        # 可能的作品ID格式
                        r'([a-zA-Z]+\d{5,})',  # 字母+5位以上数字
                        r'(\d+[a-zA-Z]+\d{3,})', # 数字+字母+3位以上数字

                        # 特定的DMM格式
                        r'"([a-zA-Z]{2,8}\d{3,6})"',  # 2-8个字母+3-6个数字
                        r'"(\d+[a-zA-Z]{2,8}\d{3,6})"',  # 数字前缀+字母+数字
                    ]

                    found_cids = set()
                    for pattern in cid_patterns:
                        cids = re.findall(pattern, decoded_data)
                        for cid in cids:
                            # 更严格的CID验证
                            if (len(cid) >= 5 and len(cid) <= 20 and  # 长度合理
                                cid not in found_cids and  # 去重
                                not cid.isdigit() and  # 不是纯数字
                                re.match(r'^[a-zA-Z0-9]+$', cid)):  # 只包含字母数字
                                found_cids.add(cid)

                    # 为找到的CID创建作品信息
                    for cid in found_cids:
                        work_info = {
                            'cid': cid,
                            'detail_url': f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/",
                            'page_number': page_number,
                            'crawl_time': datetime.now().isoformat()
                        }
                        works_data.append(work_info)

                except Exception as e:
                    continue

            # 去重
            seen_cids = set()
            unique_works = []
            for work in works_data:
                if work['cid'] not in seen_cids:
                    seen_cids.add(work['cid'])
                    unique_works.append(work)

            if unique_works:
                print(f"         🎯 从流式数据中提取到 {len(unique_works)} 个唯一作品")
                return unique_works

        except Exception as e:
            print(f"         ❌ 流式数据解析异常: {e}")

        return []

    def crawl_html_page(self, page_number: int) -> List[Dict]:
        """传统HTML解析方法（备用）"""
        url = f"https://video.dmm.co.jp/av/list/?page={page_number}"

        try:
            response = self.session.get(url, timeout=30)

            if response.status_code == 200:
                # 保存HTML用于调试
                with open(f'debug_requests_page_{page_number}.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"      📄 页面已保存到 debug_requests_page_{page_number}.html")

                # 解析HTML
                soup = BeautifulSoup(response.content, 'html.parser')

                # 查找包含CID的链接
                cid_links = soup.find_all('a', href=lambda x: x and 'cid=' in x)

                if cid_links:
                    works_data = []
                    seen_cids = set()

                    for link in cid_links:
                        href = link.get('href')
                        if href:
                            if href.startswith('/'):
                                href = 'https://www.dmm.co.jp' + href

                            cid = self.extract_cid_from_url(href)
                            if cid and cid not in seen_cids:
                                seen_cids.add(cid)

                                work_info = {
                                    'cid': cid,
                                    'detail_url': href,
                                    'page_number': page_number,
                                    'crawl_time': datetime.now().isoformat()
                                }
                                works_data.append(work_info)

                    return works_data

        except Exception as e:
            print(f"      ❌ HTML解析异常: {e}")

        return []
    
    def save_works_to_db(self, works_data: List[Dict]):
        """保存作品数据到数据库"""
        if not works_data:
            return
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            for work in works_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO dmm_works 
                    (cid, title, detail_url, page_number, crawl_time)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    work.get('cid'),
                    work.get('title'),
                    work.get('detail_url'),
                    work.get('page_number'),
                    work.get('crawl_time')
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def crawl_multiple_pages(self, start_page: int = 1, end_page: int = 10):
        """爬取多页数据"""
        print("📡 DMM Requests多页爬虫")
        print("=" * 50)

        print(f"📋 爬取配置:")
        print(f"   页面范围: {start_page} - {end_page}")
        print(f"   预计作品数: {(end_page - start_page + 1) * 120}")
        print(f"   请求间隔: 5秒")

        # 处理年龄验证
        if not self.handle_age_verification():
            print("⚠️ 年龄验证失败，继续尝试...")

        total_works = 0
        success_pages = 0
        failed_pages = 0

        for page_num in range(start_page, end_page + 1):
            print(f"\n📄 处理第 {page_num} 页 ({page_num - start_page + 1}/{end_page - start_page + 1})")

            # 爬取单页
            works_data = self.crawl_single_page(page_num)

            if works_data:
                # 保存数据
                self.save_works_to_db(works_data)
                success_pages += 1
                total_works += len(works_data)

                print(f"   ✅ 第 {page_num} 页完成，保存 {len(works_data)} 个作品")
                print(f"   📊 累计进度: {success_pages}页成功, {failed_pages}页失败, 总计{total_works}个作品")

            else:
                failed_pages += 1
                print(f"   ❌ 第 {page_num} 页失败")

            # 页面间隔（最后一页不需要等待）
            if page_num < end_page:
                print(f"   😴 等待5秒后继续下一页...")
                time.sleep(5)

        # 最终统计
        print(f"\n🎉 爬取完成!")
        print(f"📊 最终统计:")
        print(f"   成功页面: {success_pages}/{end_page - start_page + 1}")
        print(f"   失败页面: {failed_pages}/{end_page - start_page + 1}")
        print(f"   总作品数: {total_works}")
        print(f"   数据库文件: {self.db_file}")

    def crawl_first_page(self):
        """只爬取第一页进行测试"""
        self.crawl_multiple_pages(1, 1)

def main():
    """主函数"""
    print("📡 DMM Requests爬虫 (无浏览器版本)")
    print("=" * 50)

    print("📋 说明:")
    print("   - 使用requests库，不需要浏览器")
    print("   - 尝试多种方法：API接口、JavaScript数据、HTML解析")
    print("   - 避免浏览器依赖问题")

    print("\n📋 爬取选项:")
    print("   1. 测试单页 (第1页)")
    print("   2. 爬取前10页")
    print("   3. 爬取前50页")
    print("   4. 自定义页面范围")

    # 用户选择
    choice = input("\n请选择爬取方式 (1-4): ").strip()

    crawler = DMMRequestsCrawler()

    if choice == '1':
        print("🧪 开始测试第一页...")
        crawler.crawl_first_page()

    elif choice == '2':
        print("🚀 开始爬取前10页...")
        crawler.crawl_multiple_pages(1, 10)

    elif choice == '3':
        print("🚀 开始爬取前50页...")
        print("⚠️  这将需要约5分钟完成")
        confirm = input("确认继续？(y/N): ").strip().lower()
        if confirm == 'y':
            crawler.crawl_multiple_pages(1, 50)
        else:
            print("爬取已取消")

    elif choice == '4':
        try:
            start_page = int(input("起始页码: ").strip())
            end_page = int(input("结束页码: ").strip())

            if start_page < 1 or end_page < start_page or end_page > 417:
                print("❌ 页码范围无效 (1-417)")
                return

            estimated_time = (end_page - start_page + 1) * 0.1  # 每页约6秒
            print(f"🚀 开始爬取第{start_page}-{end_page}页...")
            print(f"⏰ 预计需要约{estimated_time:.1f}分钟")

            confirm = input("确认继续？(y/N): ").strip().lower()
            if confirm == 'y':
                crawler.crawl_multiple_pages(start_page, end_page)
            else:
                print("爬取已取消")

        except ValueError:
            print("❌ 请输入有效的数字")

    else:
        print("❌ 无效选择")
        return

if __name__ == "__main__":
    main()
