#!/usr/bin/env python3
"""
测试修正后的厂商模式匹配（基于实际数据库）
"""
import sys
import json
sys.path.append('.')

def load_actual_mappings():
    """加载实际的厂商映射数据"""
    try:
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data.get('mappings', {})
    except Exception as e:
        print(f"❌ 加载映射数据失败: {e}")
        return {}

def test_studio_patterns():
    """测试厂商模式匹配"""
    print("🔍 测试修正后的厂商模式匹配（基于实际数据库）")
    print("=" * 60)
    
    # 加载实际映射数据
    actual_mappings = load_actual_mappings()
    
    if not actual_mappings:
        print("❌ 无法加载实际映射数据")
        return False
    
    try:
        from modules.dmm_search_crawler import DMMSearchCrawler
        
        crawler = DMMSearchCrawler()
        
        # 基于实际数据库的测试用例
        test_cases = []
        
        # ID厂商（多重映射）
        if 'ID' in actual_mappings:
            id_mapping = actual_mappings['ID']
            print(f"📋 ID厂商实际映射:")
            print(f"   主映射: {id_mapping['primary']}")
            print(f"   备选映射: {id_mapping['alternatives']}")
            
            # 生成测试CID
            for prefix in [id_mapping['primary']] + id_mapping['alternatives']:
                test_cid = f"{prefix}00021"
                test_cases.append((test_cid, "ID", 21, f"ID厂商-{prefix}前缀"))
        
        # SSIS厂商（单一映射）
        if 'SSIS' in actual_mappings:
            ssis_mapping = actual_mappings['SSIS']
            print(f"\n📋 SSIS厂商实际映射:")
            print(f"   主映射: {ssis_mapping['primary']}")
            print(f"   类型: {ssis_mapping['type']}")
            
            test_cid = f"{ssis_mapping['primary']}00001"
            test_cases.append((test_cid, "SSIS", 1, "SSIS厂商-单一映射"))
        
        # NEO厂商（单一映射）
        if 'NEO' in actual_mappings:
            neo_mapping = actual_mappings['NEO']
            print(f"\n📋 NEO厂商实际映射:")
            print(f"   主映射: {neo_mapping['primary']}")
            print(f"   类型: {neo_mapping['type']}")
            
            test_cid = f"{neo_mapping['primary']}00834"
            test_cases.append((test_cid, "NEO", 834, "NEO厂商-单一映射"))
        
        # MILK厂商（多重映射）
        if 'MILK' in actual_mappings:
            milk_mapping = actual_mappings['MILK']
            print(f"\n📋 MILK厂商实际映射:")
            print(f"   主映射: {milk_mapping['primary']}")
            print(f"   备选映射: {milk_mapping['alternatives']}")
            
            for prefix in [milk_mapping['primary']] + milk_mapping['alternatives']:
                test_cid = f"{prefix}00251"
                test_cases.append((test_cid, "MILK", 251, f"MILK厂商-{prefix}前缀"))
        
        # HODV厂商（多重映射）
        if 'HODV' in actual_mappings:
            hodv_mapping = actual_mappings['HODV']
            print(f"\n📋 HODV厂商实际映射:")
            print(f"   主映射: {hodv_mapping['primary']}")
            print(f"   备选映射: {hodv_mapping['alternatives']}")
            
            for prefix in [hodv_mapping['primary']] + hodv_mapping['alternatives']:
                test_cid = f"{prefix}21987"
                test_cases.append((test_cid, "HODV", 21987, f"HODV厂商-{prefix}前缀"))
        
        print(f"\n🔍 开始测试 {len(test_cases)} 个基于实际数据的测试用例:")
        
        passed_tests = 0
        failed_tests = 0
        
        for cid, studio, number, description in test_cases:
            print(f"\n   📝 {description}:")
            print(f"      CID: {cid}, 厂商: {studio}, 数字: {number}")
            
            try:
                is_relevant = crawler._is_highly_relevant_cid(cid, studio, number)
                
                if is_relevant:
                    prefix = crawler._extract_prefix_from_cid(cid, studio, number)
                    confidence = crawler._calculate_confidence(cid, studio, number)
                    print(f"      ✅ 通过 -> 前缀: {prefix}, 置信度: {confidence:.2f}")
                    passed_tests += 1
                else:
                    print(f"      ❌ 被过滤（这不应该发生，因为是基于实际数据库的）")
                    failed_tests += 1
                    
            except Exception as e:
                print(f"      ❌ 异常: {e}")
                failed_tests += 1
        
        print(f"\n📊 测试结果:")
        print(f"   通过: {passed_tests}/{len(test_cases)} ({passed_tests/len(test_cases)*100:.1f}%)")
        print(f"   失败: {failed_tests}")
        
        if failed_tests == 0:
            print(f"\n🎉 完美！所有基于实际数据库的测试都通过了")
            return True
        else:
            print(f"\n⚠️ 有 {failed_tests} 个测试失败，需要进一步调整")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wrong_examples():
    """测试我之前错误示例的CID"""
    print("\n🚫 测试我之前错误示例的CID（应该被正确过滤）:")
    
    try:
        from modules.dmm_search_crawler import DMMSearchCrawler
        
        crawler = DMMSearchCrawler()
        
        # 我之前错误的示例
        wrong_examples = [
            ("h_1234ssis00001", "SSIS", 1, "错误的SSIS h_前缀格式"),
            ("h_1240neo00834", "NEO", 834, "错误的NEO h_前缀格式"),
        ]
        
        for cid, studio, number, description in wrong_examples:
            print(f"\n   📝 {description}:")
            print(f"      CID: {cid}, 厂商: {studio}, 数字: {number}")
            
            try:
                is_relevant = crawler._is_highly_relevant_cid(cid, studio, number)
                
                if is_relevant:
                    print(f"      ⚠️ 意外通过（可能需要调整模式）")
                else:
                    print(f"      ✅ 正确过滤")
                    
            except Exception as e:
                print(f"      ❌ 异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误示例测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ 厂商模式匹配修正验证")
    print("=" * 50)
    
    print("📋 修正说明:")
    print("   - 基于实际数据库中的真实映射")
    print("   - 修正了我之前错误的示例")
    print("   - 确保模式匹配与数据库一致")
    
    # 执行测试
    tests = [
        ("实际数据库模式测试", test_studio_patterns),
        ("错误示例过滤测试", test_wrong_examples)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                print(f"✅ {test_name}: 通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"📋 测试总结: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        print("\n🎉 厂商模式匹配修正成功！")
        
        print("\n📋 修正内容:")
        print("   ✅ 基于实际数据库调整厂商模式")
        print("   ✅ SSIS: 只支持 ssis 前缀（单一映射）")
        print("   ✅ NEO: 只支持 433neo 前缀（单一映射）")
        print("   ✅ MILK: 支持 h_1240milk 和 milk 前缀（多重映射）")
        print("   ✅ HODV: 支持 41hodv 和 5642hodv 前缀（多重映射）")
        print("   ✅ ID: 支持 8 种前缀（多重映射）")
        
        print("\n🎯 现在的厂商支持:")
        print("   📊 SSIS: ssis00001")
        print("   📊 NEO: 433neo00834")
        print("   📊 MILK: h_1240milk00251, milk00251")
        print("   📊 HODV: 41hodv21987, 5642hodv21987")
        print("   📊 ID: 5531id00021, 5524id00021, ..., h_113id00021")
    else:
        print("\n⚠️ 部分模式仍需调整")

if __name__ == "__main__":
    main()
