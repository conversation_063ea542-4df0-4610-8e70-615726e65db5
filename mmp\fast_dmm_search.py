#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高效DMM番号搜索系统
模拟DMM网站的快速搜索机制
"""

import json
import sqlite3
import re
import time
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from urllib.parse import quote


@dataclass
class SearchResult:
    """搜索结果数据类"""
    code: str
    dmm_cid: str
    url: str
    confidence: float  # 匹配置信度
    source: str  # 数据来源：local/dmm/generated


class FastDMMSearch:
    """快速DMM搜索引擎"""
    
    def __init__(self, config_file: str = "dmm_studio_mappings.json", db_file: str = "fast_dmm.db"):
        self.config_file = config_file
        self.db_file = db_file
        self.load_config()
        self.init_database()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.studio_mappings = config['studio_mappings']
                self.search_patterns = config['search_patterns']
                self.dmm_urls = config['dmm_base_urls']
        except FileNotFoundError:
            print(f"配置文件 {self.config_file} 不存在，使用默认配置")
            self.studio_mappings = {}
            self.search_patterns = {
                "number_padding": [3, 4, 5],
                "prefix_variations": ["", "0", "00"],
                "separator_variations": ["-", ""]
            }
            self.dmm_urls = {
                "detail": "https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/",
                "search": "https://www.dmm.co.jp/search/=/searchstr={query}/"
            }
    
    def init_database(self):
        """初始化高性能数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 创建主表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS video_index (
                id INTEGER PRIMARY KEY,
                original_code TEXT NOT NULL,
                studio TEXT NOT NULL,
                number INTEGER NOT NULL,
                dmm_cid TEXT NOT NULL,
                url TEXT NOT NULL,
                verified BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建变体索引表（用于快速查找）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS code_variants (
                variant_code TEXT PRIMARY KEY,
                video_id INTEGER NOT NULL,
                FOREIGN KEY (video_id) REFERENCES video_index (id)
            )
        ''')
        
        # 创建高性能索引
        cursor.execute('CREATE UNIQUE INDEX IF NOT EXISTS idx_dmm_cid ON video_index(dmm_cid)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_studio_number ON video_index(studio, number)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_original_code ON video_index(original_code)')
        
        conn.commit()
        conn.close()
    
    def parse_code(self, code: str) -> Optional[Tuple[str, int]]:
        """解析番号，返回(厂商, 数字)"""
        code = code.strip().upper().replace(' ', '')
        
        # 匹配模式
        patterns = [
            r'^([A-Z]+)-(\d+)$',  # MILK-251
            r'^([A-Z]+)(\d+)$',   # MILK251
        ]
        
        for pattern in patterns:
            match = re.match(pattern, code)
            if match:
                studio = match.group(1)
                number = int(match.group(2))
                return studio, number
        
        return None
    
    def generate_all_variants(self, studio: str, number: int) -> List[str]:
        """生成所有可能的番号变体"""
        variants = set()
        
        # 基础格式
        variants.add(f"{studio}-{number}")
        variants.add(f"{studio}{number}")
        
        # 补零变体
        for padding in self.search_patterns["number_padding"]:
            padded_num = str(number).zfill(padding)
            
            # 不同分隔符
            for sep in self.search_patterns["separator_variations"]:
                variants.add(f"{studio}{sep}{padded_num}")
            
            # 前缀变体
            for prefix in self.search_patterns["prefix_variations"]:
                for sep in self.search_patterns["separator_variations"]:
                    variants.add(f"{studio}{sep}{prefix}{padded_num}")
        
        return list(variants)
    
    def generate_dmm_cid(self, studio: str, number: int) -> Optional[str]:
        """生成DMM CID"""
        if studio in self.studio_mappings:
            dmm_prefix = self.studio_mappings[studio]
            padded_number = str(number).zfill(5)
            return f"{dmm_prefix}{padded_number}"
        return None
    
    def build_index_for_code(self, studio: str, number: int) -> bool:
        """为特定番号构建索引"""
        dmm_cid = self.generate_dmm_cid(studio, number)
        if not dmm_cid:
            return False
        
        original_code = f"{studio}-{number}"
        url = self.dmm_urls["detail"].format(cid=dmm_cid)
        
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        try:
            # 插入主记录
            cursor.execute('''
                INSERT OR IGNORE INTO video_index 
                (original_code, studio, number, dmm_cid, url)
                VALUES (?, ?, ?, ?, ?)
            ''', (original_code, studio, number, dmm_cid, url))
            
            video_id = cursor.lastrowid
            if video_id == 0:  # 记录已存在
                cursor.execute(
                    'SELECT id FROM video_index WHERE dmm_cid = ?', 
                    (dmm_cid,)
                )
                video_id = cursor.fetchone()[0]
            
            # 生成所有变体并插入索引
            variants = self.generate_all_variants(studio, number)
            for variant in variants:
                cursor.execute('''
                    INSERT OR IGNORE INTO code_variants (variant_code, video_id)
                    VALUES (?, ?)
                ''', (variant, video_id))
            
            conn.commit()
            return True
            
        except sqlite3.Error as e:
            print(f"数据库错误: {e}")
            return False
        finally:
            conn.close()
    
    def search_in_index(self, code: str) -> List[SearchResult]:
        """在本地索引中搜索"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 直接查找变体
        cursor.execute('''
            SELECT vi.original_code, vi.dmm_cid, vi.url, vi.verified
            FROM code_variants cv
            JOIN video_index vi ON cv.video_id = vi.id
            WHERE cv.variant_code = ?
        ''', (code.upper(),))
        
        results = []
        for row in cursor.fetchall():
            confidence = 1.0 if row[3] else 0.8  # 已验证的置信度更高
            results.append(SearchResult(
                code=row[0],
                dmm_cid=row[1],
                url=row[2],
                confidence=confidence,
                source="local"
            ))
        
        conn.close()
        return results
    
    def verify_dmm_url(self, url: str) -> bool:
        """验证DMM URL是否有效"""
        try:
            response = self.session.head(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def smart_search(self, code: str) -> List[SearchResult]:
        """智能搜索：多策略组合"""
        results = []
        
        # 1. 首先在本地索引中搜索
        local_results = self.search_in_index(code)
        if local_results:
            return local_results
        
        # 2. 解析番号并生成可能的结果
        parsed = self.parse_code(code)
        if not parsed:
            return []
        
        studio, number = parsed
        
        # 3. 如果厂商在映射中，直接生成结果
        if studio in self.studio_mappings:
            # 构建索引
            if self.build_index_for_code(studio, number):
                # 再次搜索本地索引
                local_results = self.search_in_index(code)
                if local_results:
                    # 异步验证URL
                    for result in local_results:
                        if self.verify_dmm_url(result.url):
                            result.confidence = 1.0
                            result.source = "generated+verified"
                            # 更新数据库验证状态
                            self.mark_as_verified(result.dmm_cid)
                    return local_results
        
        return []
    
    def mark_as_verified(self, dmm_cid: str):
        """标记为已验证"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        cursor.execute(
            'UPDATE video_index SET verified = TRUE WHERE dmm_cid = ?',
            (dmm_cid,)
        )
        conn.commit()
        conn.close()
    
    def batch_build_index(self, studio_list: List[str], number_range: Tuple[int, int]):
        """批量构建索引"""
        print(f"开始批量构建索引...")
        start_time = time.time()
        
        total_built = 0
        for studio in studio_list:
            if studio not in self.studio_mappings:
                continue
                
            for number in range(number_range[0], number_range[1] + 1):
                if self.build_index_for_code(studio, number):
                    total_built += 1
                
                if total_built % 100 == 0:
                    print(f"已构建 {total_built} 个索引...")
        
        end_time = time.time()
        print(f"索引构建完成！共构建 {total_built} 个索引，耗时 {end_time - start_time:.2f} 秒")
    
    def get_statistics(self) -> Dict:
        """获取数据库统计信息"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM video_index')
        total_videos = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM video_index WHERE verified = TRUE')
        verified_videos = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM code_variants')
        total_variants = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(DISTINCT studio) FROM video_index')
        unique_studios = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'total_videos': total_videos,
            'verified_videos': verified_videos,
            'total_variants': total_variants,
            'unique_studios': unique_studios,
            'verification_rate': verified_videos / total_videos if total_videos > 0 else 0
        }


def main():
    """主函数示例"""
    search_engine = FastDMMSearch()
    
    # 显示统计信息
    stats = search_engine.get_statistics()
    print("数据库统计信息:")
    print(f"  总视频数: {stats['total_videos']}")
    print(f"  已验证: {stats['verified_videos']}")
    print(f"  总变体数: {stats['total_variants']}")
    print(f"  厂商数: {stats['unique_studios']}")
    print(f"  验证率: {stats['verification_rate']:.2%}")
    print()
    
    # 测试搜索
    test_codes = [
        "MILK-251",
        "MILK251", 
        "MILK00251",
        "MILK-0251",
        "SSIS-001",
        "STARS-123"
    ]
    
    for code in test_codes:
        print(f"搜索: {code}")
        start_time = time.time()
        
        results = search_engine.smart_search(code)
        
        end_time = time.time()
        print(f"耗时: {end_time - start_time:.3f}秒")
        
        if results:
            for result in results:
                print(f"  找到: {result.code}")
                print(f"  CID: {result.dmm_cid}")
                print(f"  URL: {result.url}")
                print(f"  置信度: {result.confidence:.2f}")
                print(f"  来源: {result.source}")
        else:
            print("  未找到结果")
        print("-" * 50)


if __name__ == "__main__":
    main()
