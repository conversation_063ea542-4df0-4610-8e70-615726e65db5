#!/usr/bin/env python3
"""
简单测试模块化版本的海报获取功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

def test_modular_poster():
    """测试模块化版本海报获取"""
    print("=== 测试模块化版本海报获取功能 ===")

    try:
        from modules.search_detail import SearchDetailModule
        search_module = SearchDetailModule()

        # 测试多个不同的番号
        test_codes = ["MILK-251", "IPX-123", "SSIS-001", "PRED-100"]

        for code in test_codes:
            print(f"\n🔍 测试番号: {code}")

            # 先搜索获取CID
            search_result = search_module.search_dmm_enhanced(code)
            if search_result['success']:
                cid = search_result.get('cid', '')
                print(f"   搜索成功，CID: {cid}")

                # 测试海报获取
                poster_result = search_module.get_poster_with_cid(cid)
                if poster_result['success']:
                    print(f"   ✅ 海报获取成功！使用CID: {poster_result.get('cid', cid)}")
                    if poster_result.get('poster_bytes'):
                        print(f"      高清海报大小: {len(poster_result['poster_bytes'])} bytes")
                    if poster_result.get('thumb_bytes'):
                        print(f"      缩略图大小: {len(poster_result['thumb_bytes'])} bytes")
                else:
                    print(f"   ❌ 海报获取失败: {poster_result.get('message', '未知错误')}")
            else:
                print(f"   ❌ 搜索失败: {search_result.get('message', '未知错误')}")

    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_modular_poster()
