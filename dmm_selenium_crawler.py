#!/usr/bin/env python3
"""
DMM Selenium爬虫 - 使用浏览器自动化处理JavaScript渲染
解决Next.js应用的动态内容加载问题
"""
import sqlite3
import time
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

class DMMSeleniumCrawler:
    """DMM Selenium爬虫"""
    
    def __init__(self, db_file: str = "dmm_selenium_database.db", headless: bool = True):
        self.db_file = db_file
        self.driver = None
        self.headless = headless
        
        # 爬取配置
        self.config = {
            'start_page': 1,
            'end_page': 417,
            'page_load_timeout': 30,    # 页面加载超时
            'element_wait_timeout': 15, # 元素等待超时
            'min_delay': 3.0,           # 最小延迟
            'max_delay': 6.0,           # 最大延迟
            'batch_size': 20,           # 每20页休息一次
            'batch_delay': 120,         # 批次休息2分钟
            'retry_times': 3,           # 重试次数
        }
        
        # 统计信息
        self.stats = {
            'total_pages': 0,
            'total_items': 0,
            'success_pages': 0,
            'failed_pages': 0,
            'start_time': None,
            'errors': []
        }
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('dmm_selenium_crawler.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库
        self.init_database()

    def generate_number_from_cid(self, cid: str) -> Optional[str]:
        """从CID生成番号"""
        try:
            # 移除数字前缀（如1nhdtc00082 -> nhdtc00082）
            clean_cid = re.sub(r'^\d+', '', cid.lower())

            # 提取字母前缀和数字
            match = re.match(r'^([a-zA-Z]+)(\d+)$', clean_cid)
            if match:
                prefix = match.group(1).upper()
                number = int(match.group(2))

                # 生成标准番号格式
                return f"{prefix}-{number:03d}"

            return None

        except Exception:
            return None
    
    def init_database(self):
        """初始化数据库"""
        print("📊 初始化数据库...")
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 创建主表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dmm_works (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cid TEXT UNIQUE NOT NULL,
                    title TEXT,
                    number TEXT,
                    studio TEXT,
                    detail_url TEXT,
                    thumbnail_url TEXT,
                    price TEXT,
                    release_date TEXT,
                    duration TEXT,
                    page_number INTEGER,
                    crawl_time TEXT,
                    raw_html TEXT,
                    UNIQUE(cid)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_cid ON dmm_works(cid)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_number ON dmm_works(number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_studio ON dmm_works(studio)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_page ON dmm_works(page_number)')
            
            # 创建爬取进度表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_progress (
                    page_number INTEGER PRIMARY KEY,
                    status TEXT,
                    items_count INTEGER,
                    crawl_time TEXT,
                    error_message TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            print("✅ 数据库初始化完成")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
    
    def init_driver(self):
        """初始化Selenium WebDriver"""
        print("🚀 初始化浏览器...")

        try:
            chrome_options = Options()

            if self.headless:
                chrome_options.add_argument('--headless')

            # 解决用户数据目录冲突问题
            import tempfile
            import os
            temp_dir = tempfile.mkdtemp()
            chrome_options.add_argument(f'--user-data-dir={temp_dir}')

            # 优化选项 - 针对低资源环境
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-logging')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument('--disable-infobars')
            chrome_options.add_argument('--disable-notifications')



            # 设置用户代理
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # 设置窗口大小
            chrome_options.add_argument('--window-size=1920,1080')

            # 禁用图片加载以提高速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 排除自动化标识
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            self.driver = webdriver.Chrome(options=chrome_options)

            # 隐藏自动化标识
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 设置超时时间
            self.driver.set_page_load_timeout(60)  # 增加到60秒
            self.driver.implicitly_wait(10)  # 隐式等待10秒

            print("✅ 浏览器初始化成功")
            return True

        except Exception as e:
            print(f"❌ Chrome浏览器初始化失败: {e}")
            print("🔄 尝试使用Firefox浏览器...")

            try:
                from selenium.webdriver.firefox.options import Options as FirefoxOptions

                firefox_options = FirefoxOptions()

                if self.headless:
                    firefox_options.add_argument('--headless')

                firefox_options.add_argument('--no-sandbox')
                firefox_options.add_argument('--disable-dev-shm-usage')

                self.driver = webdriver.Firefox(options=firefox_options)
                self.driver.set_page_load_timeout(self.config['page_load_timeout'])

                print("✅ Firefox浏览器初始化成功")
                return True

            except Exception as firefox_error:
                print(f"❌ Firefox浏览器也初始化失败: {firefox_error}")
                print("💡 建议解决方案:")
                print("   1. 关闭所有浏览器窗口")
                print("   2. 重启系统")
                print("   3. 或者安装/更新ChromeDriver和GeckoDriver")
                return False
    
    def handle_age_verification(self):
        """处理年龄验证"""
        print("🔐 处理年龄验证...")

        # 尝试多种年龄验证方式
        verification_methods = [
            self._method_1_age_verification,
            self._method_2_skip_verification,
            self._method_3_direct_access
        ]

        for i, method in enumerate(verification_methods, 1):
            try:
                print(f"   🔄 尝试方法 {i}/3...")
                if method():
                    print("✅ 年龄验证完成")
                    return True
            except Exception as e:
                print(f"   ❌ 方法 {i} 失败: {str(e)[:100]}...")
                continue

        print("⚠️ 所有年龄验证方法都失败，尝试跳过验证继续...")
        return True  # 继续执行，看看能否直接访问

    def _method_1_age_verification(self):
        """方法1: 标准年龄验证"""
        age_check_url = "https://www.dmm.co.jp/age_check/=/declared=yes/?rurl=https%3A%2F%2Fvideo.dmm.co.jp%2F"

        # 设置更长的超时时间
        self.driver.set_page_load_timeout(60)
        self.driver.get(age_check_url)

        # 等待页面稳定
        time.sleep(5)
        return True

    def _method_2_skip_verification(self):
        """方法2: 直接设置cookies跳过验证"""
        # 访问主页
        self.driver.get("https://www.dmm.co.jp/")

        # 设置年龄验证cookies
        self.driver.add_cookie({
            'name': 'age_check_done',
            'value': '1',
            'domain': '.dmm.co.jp'
        })
        self.driver.add_cookie({
            'name': 'ckcy',
            'value': '1',
            'domain': '.dmm.co.jp'
        })

        time.sleep(2)
        return True

    def _method_3_direct_access(self):
        """方法3: 直接访问目标页面"""
        # 直接访问视频列表页面
        self.driver.get("https://video.dmm.co.jp/av/list/?page=1")
        time.sleep(3)

        # 检查是否成功访问
        current_url = self.driver.current_url
        if "video.dmm.co.jp" in current_url:
            return True
        else:
            return False
    
    def extract_work_info_from_element(self, element, page_number: int) -> Optional[Dict]:
        """从单个作品元素中提取信息"""
        try:
            work_info = {
                'page_number': page_number,
                'crawl_time': datetime.now().isoformat()
            }
            
            # 查找链接元素 - 尝试多种方式
            detail_url = None

            # 方式1: 如果元素本身就是链接
            if element.tag_name == 'a':
                detail_url = element.get_attribute('href')

            # 方式2: 查找子元素中的链接
            if not detail_url:
                try:
                    link_element = element.find_element(By.TAG_NAME, 'a')
                    detail_url = link_element.get_attribute('href')
                except NoSuchElementException:
                    pass

            # 方式3: 查找包含cid的任何链接
            if not detail_url:
                try:
                    cid_links = element.find_elements(By.CSS_SELECTOR, '[href*="cid="]')
                    if cid_links:
                        detail_url = cid_links[0].get_attribute('href')
                except NoSuchElementException:
                    pass

            if detail_url and 'cid=' in detail_url:
                work_info['detail_url'] = detail_url

                # 从URL中提取CID - 尝试多种模式
                cid_patterns = [
                    r'cid=([^&/]+)',           # 标准模式: cid=abc123
                    r'/cid=([^&/]+)/',         # 路径模式: /cid=abc123/
                    r'cid=([^&/?]+)',          # 包含问号: cid=abc123?
                    r'cid=([a-zA-Z0-9_-]+)',   # 字母数字模式
                ]

                cid = None
                for pattern in cid_patterns:
                    cid_match = re.search(pattern, detail_url)
                    if cid_match:
                        cid = cid_match.group(1)
                        break

                if cid:
                    work_info['cid'] = cid
                else:
                    print(f"        ❌ 无法提取CID from: {detail_url}")
                    return None
            else:
                print(f"        ❌ 未找到有效的CID链接")
                return None
            
            # 提取标题
            try:
                title_element = element.find_element(By.CSS_SELECTOR, '[title], .title, .txt')
                title = title_element.get_attribute('title') or title_element.text
                if title:
                    work_info['title'] = title.strip()
            except NoSuchElementException:
                pass
            
            # 提取缩略图
            try:
                img_element = element.find_element(By.TAG_NAME, 'img')
                thumbnail_url = img_element.get_attribute('src')
                if thumbnail_url:
                    work_info['thumbnail_url'] = thumbnail_url
            except NoSuchElementException:
                pass
            
            # 从CID生成番号和厂商
            cid = work_info.get('cid', '')
            if cid:
                number = self.generate_number_from_cid(cid)
                if number:
                    work_info['number'] = number
                    # 从番号提取厂商前缀
                    studio_match = re.match(r'^([A-Z]+)', number)
                    if studio_match:
                        work_info['studio'] = studio_match.group(1)

            # 从标题中提取番号（备用方案）
            if not work_info.get('number'):
                title = work_info.get('title', '')
                if title:
                    number_match = re.search(r'([A-Z]+[-_]?\d+)', title.upper())
                    if number_match:
                        work_info['number'] = number_match.group(1)
                        # 从番号提取厂商
                        studio_match = re.match(r'^([A-Z]+)', number_match.group(1))
                        if studio_match:
                            work_info['studio'] = studio_match.group(1)
            
            # 保存原始HTML
            work_info['raw_html'] = element.get_attribute('outerHTML')
            
            return work_info
            
        except Exception as e:
            self.logger.error(f"提取作品信息失败: {e}")
            return None
    
    def crawl_single_page(self, page_number: int) -> Tuple[bool, List[Dict]]:
        """爬取单页数据"""
        url = f"https://video.dmm.co.jp/av/list/?page={page_number}"
        
        for attempt in range(self.config['retry_times']):
            try:
                print(f"   🔍 爬取第 {page_number} 页 (尝试 {attempt + 1}/{self.config['retry_times']})")
                
                # 访问页面
                self.driver.get(url)
                
                # 等待页面加载完成
                print(f"      ⏳ 等待页面加载...")

                # 添加进度提示
                import threading
                import time as time_module

                def progress_indicator():
                    for i in range(30):  # 30秒超时
                        time_module.sleep(1)
                        if hasattr(self, '_page_loaded'):
                            break
                        if i % 5 == 0:
                            print(f"      ⏳ 等待中... ({i+1}秒)")

                progress_thread = threading.Thread(target=progress_indicator)
                progress_thread.daemon = True
                progress_thread.start()

                # 等待作品列表出现
                wait = WebDriverWait(self.driver, self.config['element_wait_timeout'])
                
                # 尝试多种可能的选择器
                selectors_to_try = [
                    '[data-testid="item"]',
                    '.item',
                    '.tmb',
                    '.product',
                    'article',
                    '[href*="cid="]',
                ]
                
                work_elements = []
                for selector in selectors_to_try:
                    try:
                        # 等待元素出现
                        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                        work_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                        if work_elements:
                            # 停止进度提示
                            self._page_loaded = True
                            print(f"      ✅ 使用选择器 '{selector}' 找到 {len(work_elements)} 个元素")
                            break
                    except TimeoutException:
                        continue

                # 确保停止进度提示
                self._page_loaded = True
                
                if not work_elements:
                    print(f"      ❌ 未找到作品列表元素")
                    
                    # 调试：保存页面截图和HTML
                    if page_number <= 3:
                        self.driver.save_screenshot(f'debug_page_{page_number}.png')
                        with open(f'debug_selenium_page_{page_number}.html', 'w', encoding='utf-8') as f:
                            f.write(self.driver.page_source)
                        print(f"      🔍 已保存调试文件: debug_page_{page_number}.png, debug_selenium_page_{page_number}.html")
                    
                    return True, []
                
                # 提取作品信息 - 最简单版本
                works_data = []

                for element in work_elements:
                    work_info = self.extract_work_info_from_element(element, page_number)
                    if work_info and work_info.get('cid'):
                        works_data.append(work_info)
                
                print(f"      ✅ 提取到 {len(works_data)} 个作品")
                return True, works_data
                
            except Exception as e:
                print(f"      ❌ 爬取异常: {e}")
                if attempt < self.config['retry_times'] - 1:
                    print(f"      🔄 等待重试...")
                    time.sleep(10)
        
        return False, []
    
    def save_works_to_db(self, works_data: List[Dict]):
        """保存作品数据到数据库"""
        if not works_data:
            return
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            for work in works_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO dmm_works 
                    (cid, title, number, studio, detail_url, thumbnail_url, price, 
                     release_date, duration, page_number, crawl_time, raw_html)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    work.get('cid'),
                    work.get('title'),
                    work.get('number'),
                    work.get('studio'),
                    work.get('detail_url'),
                    work.get('thumbnail_url'),
                    work.get('price'),
                    work.get('release_date'),
                    work.get('duration'),
                    work.get('page_number'),
                    work.get('crawl_time'),
                    work.get('raw_html')
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")
    
    def update_crawl_progress(self, page_number: int, status: str, items_count: int = 0, error_message: str = None):
        """更新爬取进度"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO crawl_progress 
                (page_number, status, items_count, crawl_time, error_message)
                VALUES (?, ?, ?, ?, ?)
            ''', (page_number, status, items_count, datetime.now().isoformat(), error_message))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"更新进度失败: {e}")
    
    def start_crawling(self, resume: bool = True):
        """开始爬取"""
        print("🕷️ DMM Selenium全站爬虫")
        print("=" * 60)
        
        print(f"📋 爬取配置:")
        print(f"   页面范围: {self.config['start_page']} - {self.config['end_page']}")
        print(f"   浏览器模式: {'无头模式' if self.headless else '有界面模式'}")
        print(f"   页面超时: {self.config['page_load_timeout']} 秒")
        print(f"   元素等待: {self.config['element_wait_timeout']} 秒")
        
        # 初始化浏览器
        if not self.init_driver():
            print("❌ 浏览器初始化失败，爬取终止")
            return
        
        try:
            # 处理年龄验证
            if not self.handle_age_verification():
                print("❌ 年龄验证失败，爬取终止")
                return
            
            # 获取已完成的页面
            completed_pages = set()
            if resume:
                try:
                    conn = sqlite3.connect(self.db_file)
                    cursor = conn.cursor()
                    cursor.execute('SELECT page_number FROM crawl_progress WHERE status = "success"')
                    completed_pages = {row[0] for row in cursor.fetchall()}
                    conn.close()
                    print(f"📊 恢复爬取，已完成 {len(completed_pages)} 页")
                except:
                    pass
            
            self.stats['start_time'] = datetime.now()
            
            for page_num in range(self.config['start_page'], self.config['end_page'] + 1):
                if resume and page_num in completed_pages:
                    print(f"⏭️ 跳过已完成页面: {page_num}")
                    continue
                
                print(f"\n📄 处理第 {page_num} 页 ({page_num - self.config['start_page'] + 1}/{self.config['end_page'] - self.config['start_page'] + 1})")
                
                # 爬取单页
                success, works_data = self.crawl_single_page(page_num)
                
                if success:
                    # 保存数据
                    self.save_works_to_db(works_data)
                    self.update_crawl_progress(page_num, 'success', len(works_data))
                    
                    self.stats['success_pages'] += 1
                    self.stats['total_items'] += len(works_data)
                    
                    print(f"   ✅ 第 {page_num} 页完成，保存 {len(works_data)} 个作品")
                    
                else:
                    self.update_crawl_progress(page_num, 'failed', 0, "爬取失败")
                    self.stats['failed_pages'] += 1
                    print(f"   ❌ 第 {page_num} 页失败")
                
                # 显示进度统计
                if page_num % 10 == 0:
                    self.print_progress_stats()
                
                # 批次休息
                if page_num % self.config['batch_size'] == 0:
                    print(f"😴 批次休息 {self.config['batch_delay']} 秒...")
                    time.sleep(self.config['batch_delay'])
                else:
                    # 随机延迟
                    import random
                    delay = random.uniform(self.config['min_delay'], self.config['max_delay'])
                    time.sleep(delay)
            
            # 最终统计
            self.print_final_stats()
            
        finally:
            # 关闭浏览器
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")
    
    def print_progress_stats(self):
        """打印进度统计"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM crawl_progress WHERE status = "success"')
            completed_pages = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM crawl_progress WHERE status = "failed"')
            failed_pages = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM dmm_works')
            total_works = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"\n📊 进度统计:")
            print(f"   已完成页面: {completed_pages}")
            print(f"   失败页面: {failed_pages}")
            print(f"   总作品数: {total_works}")
            
            if self.stats['start_time']:
                elapsed = datetime.now() - self.stats['start_time']
                print(f"   已用时间: {elapsed}")
                
        except Exception as e:
            self.logger.error(f"获取进度统计失败: {e}")
    
    def print_final_stats(self):
        """打印最终统计"""
        print("\n" + "=" * 60)
        print("🎉 DMM Selenium爬取完成")
        print("=" * 60)
        
        self.print_progress_stats()
        print(f"💾 数据库文件: {self.db_file}")
        print("✅ 爬取任务完成")

def main():
    """主函数"""
    print("🕷️ DMM Selenium全站爬虫")
    print("=" * 60)
    
    print("📋 任务说明:")
    print("   - 使用Selenium处理JavaScript渲染")
    print("   - 爬取DMM视频列表页 1-417 页")
    print("   - 每页约120个作品，总计约50,000个作品")
    print("   - 提取CID、番号、标题、详情页等信息")
    print("   - 保存到SQLite数据库")
    
    # 用户确认
    user_input = input("\n是否开始爬取？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("爬取已取消")
        return
    
    # 询问浏览器模式
    headless_input = input("是否使用无头模式？(Y/n): ").strip().lower()
    headless = headless_input != 'n'
    
    # 询问是否恢复爬取
    resume_input = input("是否恢复之前的爬取进度？(Y/n): ").strip().lower()
    resume = resume_input != 'n'
    
    # 开始爬取
    crawler = DMMSeleniumCrawler(headless=headless)
    crawler.start_crawling(resume=resume)

if __name__ == "__main__":
    main()
