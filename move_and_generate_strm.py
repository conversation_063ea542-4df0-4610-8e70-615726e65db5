import os
import shutil
import time
import logging
from datetime import datetime
from colorama import init, Fore, Style

# 配置路径
SRC_DIR = r"/vol1/1000/CloudDrive/115open/R+18/待整理/刮削中/刮削目录/"      # 源目录
DST_DIR = r"/vol1/1000/CloudDrive/115open/R+18/已整理/更新/"      # 存放目录
STRM_DIR = r"/vol1/1000/媒体库/115/R+18/JAV/"     # strm存放目录
URL_PREFIX = r"http://192.168.1.2:5244/d/115/R+18/已整理/更新"  # 你的URL前缀

# 支持的视频后缀
VIDEO_EXTS = ['.mp4', '.mkv', '.avi', '.mov']
# 支持的元数据后缀
META_EXTS = ['.jpg', '.png', '.nfo', '.srt', '.ass', '.sub']

def ensure_dir(path):
    if not os.path.exists(path):
        os.makedirs(path)

class SimpleLogFormatter(logging.Formatter):
    def format(self, record):
        return f"{record.getMessage()}"

def setup_logger():
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    ensure_dir(log_dir)
    # 保留最新3个日志
    logs = sorted([f for f in os.listdir(log_dir) if f.endswith('.log')])
    while len(logs) > 2:
        old_log = os.path.join(log_dir, logs[0])
        os.remove(old_log)
        print(f"Removed old log file: {old_log}")
        logs.pop(0)
    log_name = f"add_strm2_{datetime.now().strftime('%Y%m%d-%H%M%S')}.log"
    log_path = os.path.join(log_dir, log_name)

    # 设置日志格式
    logger = logging.getLogger()
    logger.handlers.clear()
    logger.setLevel(logging.INFO)
    file_handler = logging.FileHandler(log_path, encoding='utf-8')
    file_handler.setFormatter(SimpleLogFormatter())
    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(SimpleLogFormatter())
    logger.addHandler(file_handler)
    logger.addHandler(stream_handler)
    return log_path

def move_and_generate(src_dir, dst_dir, strm_dir, url_prefix, tg_callback=None):
    init(autoreset=True)
    skipped_files = set()
    for root, dirs, files in os.walk(src_dir):
        rel_path = os.path.relpath(root, src_dir)
        dst_path = os.path.join(dst_dir, rel_path)
        strm_path = os.path.join(strm_dir, rel_path)
        ensure_dir(dst_path)
        ensure_dir(strm_path)
        for file in files:
            src_file = os.path.join(root, file)
            dst_file = os.path.join(dst_path, file)
            # 检查目标目录是否有同名文件
            if os.path.exists(dst_file):
                if dst_file not in skipped_files:
                    msg = f"目标目录已存在同名文件，跳过：{dst_file}"
                    # 只高亮打印 + logging.info
                    print(Fore.YELLOW + msg + Style.RESET_ALL)
                    logging.info(msg)
                    if tg_callback:
                        tg_callback(msg)
                    skipped_files.add(dst_file)
                continue
            shutil.move(src_file, dst_file)
            msg = f"文件 {src_file} 成功移动到 {dst_file}"
            print(msg)
            logging.info(msg)
            if tg_callback:
                tg_callback(msg)
            ext = os.path.splitext(file)[1].lower()
            # 复制元数据到strm目录
            if ext in META_EXTS:
                strm_file = os.path.join(strm_path, file)
                shutil.copy2(dst_file, strm_file)
                msg = f"元数据 {dst_file} 复制到 {strm_file}"
                print(msg)
                logging.info(msg)
                if tg_callback:
                    tg_callback(msg)
            # 生成strm文件
            if ext in VIDEO_EXTS:
                strm_filename = os.path.splitext(file)[0] + '.strm'
                strm_file = os.path.join(strm_path, strm_filename)
                url_path = os.path.join(rel_path, file).replace("\\", "/")
                url = f"{url_prefix}/{url_path}"
                with open(strm_file, 'w', encoding='utf-8') as f:
                    f.write(url)
                msg = f"生成STRM文件 {strm_file}，内容为 {url}"
                print(msg)
                logging.info(msg)
                if tg_callback:
                    tg_callback(msg)

if __name__ == "__main__":
    start = time.time()
    log_path = setup_logger()
    move_and_generate(SRC_DIR, DST_DIR, STRM_DIR, URL_PREFIX)
    end = time.time()
    cost = end - start
    logging.info(f"所有小姐姐已高潮，总共用了 {int(cost//3600)}小时{int((cost%3600)//60)}分钟{int(cost%60)}秒。")
    print(f"日志已保存到: {log_path}")