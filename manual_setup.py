#!/usr/bin/env python3
"""
手动设置脚本 - 创建数据库并导入映射数据
"""
import sqlite3
import json
import os
from datetime import datetime

def create_database_and_import_data():
    """创建数据库并导入映射数据"""
    print("🔧 开始手动设置...")
    
    try:
        # 1. 确保目录存在
        print("1. 创建目录...")
        os.makedirs('mmp', exist_ok=True)
        print("   ✅ 目录创建完成")
        
        # 2. 创建数据库连接
        print("2. 创建数据库...")
        db_path = 'mmp/fast_dmm.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        print(f"   ✅ 数据库连接成功: {os.path.abspath(db_path)}")
        
        # 3. 创建表结构
        print("3. 创建数据库表...")
        
        # 映射主表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS studio_mappings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                studio TEXT UNIQUE NOT NULL,
                mapping_type TEXT NOT NULL,
                primary_mapping TEXT NOT NULL,
                mapping_count INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 备选映射表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS studio_alternative_mappings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                studio TEXT NOT NULL,
                alternative_mapping TEXT NOT NULL,
                priority INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 映射元数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mapping_metadata (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_studio ON studio_mappings (studio)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_alt_studio ON studio_alternative_mappings (studio)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_mapping_type ON studio_mappings (mapping_type)')
        
        conn.commit()
        print("   ✅ 数据库表创建完成")
        
        # 4. 加载JSON数据
        print("4. 加载映射数据...")
        json_file = 'studio_mappings_all.json'
        
        if not os.path.exists(json_file):
            print(f"   ❌ 文件不存在: {json_file}")
            return False
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        print(f"   ✅ JSON数据加载完成: {len(mappings)} 个厂商")
        
        # 5. 清空现有数据
        print("5. 清空现有数据...")
        cursor.execute('DELETE FROM studio_alternative_mappings')
        cursor.execute('DELETE FROM studio_mappings')
        cursor.execute('DELETE FROM mapping_metadata')
        print("   ✅ 现有数据已清空")
        
        # 6. 导入映射数据
        print("6. 导入映射数据...")
        
        single_count = 0
        multiple_count = 0
        total_alternatives = 0
        
        for i, (studio, mapping_info) in enumerate(mappings.items()):
            # 插入主映射
            cursor.execute('''
                INSERT INTO studio_mappings 
                (studio, mapping_type, primary_mapping, mapping_count)
                VALUES (?, ?, ?, ?)
            ''', (
                studio,
                mapping_info['type'],
                mapping_info['primary'],
                mapping_info['count']
            ))
            
            # 统计
            if mapping_info['type'] == 'single':
                single_count += 1
            else:
                multiple_count += 1
            
            # 插入备选映射
            alternatives = mapping_info.get('alternatives', [])
            for j, alt_mapping in enumerate(alternatives):
                cursor.execute('''
                    INSERT INTO studio_alternative_mappings
                    (studio, alternative_mapping, priority)
                    VALUES (?, ?, ?)
                ''', (studio, alt_mapping, j + 1))
                total_alternatives += 1
            
            # 显示进度
            if (i + 1) % 500 == 0:
                print(f"   进度: {i + 1}/{len(mappings)} ({(i + 1)/len(mappings)*100:.1f}%)")
        
        # 7. 插入元数据
        print("7. 插入元数据...")
        metadata = [
            ('last_sync', datetime.now().isoformat()),
            ('total_studios', str(len(mappings))),
            ('single_mapping_studios', str(single_count)),
            ('multiple_mapping_studios', str(multiple_count)),
            ('total_alternatives', str(total_alternatives)),
            ('import_source', 'manual_setup.py'),
            ('import_version', '1.0')
        ]
        
        for key, value in metadata:
            cursor.execute('''
                INSERT OR REPLACE INTO mapping_metadata (key, value)
                VALUES (?, ?)
            ''', (key, value))
        
        # 8. 提交事务
        conn.commit()
        print("   ✅ 数据导入完成")
        
        # 9. 验证数据
        print("8. 验证导入结果...")
        
        cursor.execute('SELECT COUNT(*) FROM studio_mappings')
        main_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM studio_alternative_mappings')
        alt_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM mapping_metadata')
        meta_count = cursor.fetchone()[0]
        
        conn.close()
        
        print("   ✅ 验证结果:")
        print(f"      主映射记录: {main_count}")
        print(f"      备选映射记录: {alt_count}")
        print(f"      元数据记录: {meta_count}")
        print(f"      单一映射厂商: {single_count}")
        print(f"      多重映射厂商: {multiple_count}")
        
        # 10. 检查文件大小
        if os.path.exists(db_path):
            file_size = os.path.getsize(db_path)
            print(f"      数据库文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
        
        print("\n🎉 手动设置完成！")
        print(f"数据库文件位置: {os.path.abspath(db_path)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 设置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mapping_manager():
    """测试映射管理器"""
    print("\n🧪 测试映射管理器...")
    
    try:
        # 导入映射管理器
        import sys
        sys.path.append('.')
        from modules.mapping_manager import MappingManager
        
        # 创建映射管理器实例
        manager = MappingManager(
            config_file="studio_mappings_all.json",
            db_file="mmp/fast_dmm.db",
            enable_db_sync=True
        )
        
        # 获取统计信息
        stats = manager.get_statistics()
        print("📊 统计信息:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        # 测试映射查询
        print("\n🔍 测试映射查询:")
        test_studios = ['GDRD', 'MOND', 'SSIS', 'AVOP', 'UNKNOWN']
        
        for studio in test_studios:
            info = manager.get_mapping_info(studio)
            if info['has_mapping']:
                print(f"   {studio}: {info['type']} - {info['primary']} (来源: {info['source']})")
                if info['alternatives']:
                    print(f"      备选映射: {', '.join(info['alternatives'])}")
            else:
                print(f"   {studio}: 未找到映射")
        
        # 测试多重映射检测
        print("\n🎯 测试多重映射检测:")
        multi_test_studios = ['MOND', 'AVOP', 'GDRD']
        for studio in multi_test_studios:
            should_show_multiple = manager.should_show_multiple_results(studio)
            print(f"   {studio}: {'需要显示多选择' if should_show_multiple else '单一结果'}")
        
        print("\n✅ 映射管理器测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 映射管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 智能映射系统手动设置")
    print("=" * 60)
    
    # 步骤1: 创建数据库并导入数据
    db_success = create_database_and_import_data()
    
    if not db_success:
        print("❌ 数据库设置失败，请检查错误信息")
        return
    
    # 步骤2: 测试映射管理器
    test_success = test_mapping_manager()
    
    print("\n" + "=" * 60)
    if db_success and test_success:
        print("🎉 所有设置完成！可以继续下一步系统集成")
        print("\n📋 下一步操作:")
        print("1. 运行 python test_integration.py 测试系统集成")
        print("2. 或者直接开始使用智能搜索功能")
    else:
        print("❌ 设置过程中出现问题，请检查错误信息")

if __name__ == "__main__":
    main()
