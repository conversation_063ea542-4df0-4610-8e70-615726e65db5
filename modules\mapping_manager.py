#!/usr/bin/env python3
"""
映射管理器 - 混合架构实现
支持内存缓存 + 数据库同步的智能映射管理
"""
import json
import sqlite3
import time
import os
from datetime import datetime
from typing import Dict, List, Optional


class MappingManager:
    """映射管理器 - 混合架构实现"""
    
    def __init__(self, config_file="studio_mappings_all.json", 
                 db_file="mmp/fast_dmm.db", enable_db_sync=True):
        self.config_file = config_file
        self.db_file = db_file
        self.enable_db_sync = enable_db_sync
        
        # 内存缓存
        self.memory_cache = {}
        self.cache_timestamp = None
        self.cache_ttl = 3600  # 1小时缓存过期
        
        # 初始化
        self._initialize()
    
    def _initialize(self):
        """初始化映射管理器"""
        print("🔧 初始化映射管理器...")
        
        # 1. 创建数据库表结构
        if self.enable_db_sync:
            self._create_db_tables()
        
        # 2. 加载到内存缓存
        self._load_to_memory_cache()
        
        # 3. 同步到数据库（如果启用）
        if self.enable_db_sync:
            self._sync_to_database()
    
    def _create_db_tables(self):
        """创建数据库表结构"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.db_file), exist_ok=True)
            
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 映射主表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS studio_mappings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    studio TEXT UNIQUE NOT NULL,
                    mapping_type TEXT NOT NULL,
                    primary_mapping TEXT NOT NULL,
                    mapping_count INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 备选映射表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS studio_alternative_mappings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    studio TEXT NOT NULL,
                    alternative_mapping TEXT NOT NULL,
                    priority INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (studio) REFERENCES studio_mappings (studio)
                )
            ''')
            
            # 映射元数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS mapping_metadata (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_studio ON studio_mappings (studio)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_alt_studio ON studio_alternative_mappings (studio)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_mapping_type ON studio_mappings (mapping_type)')
            
            conn.commit()
            conn.close()
            print("✅ 数据库表结构创建成功")
            
        except Exception as e:
            print(f"❌ 创建数据库表失败: {e}")
    
    def _load_to_memory_cache(self):
        """加载数据到内存缓存"""
        try:
            if not os.path.exists(self.config_file):
                print(f"⚠️ 配置文件不存在: {self.config_file}")
                self.memory_cache = {}
                return
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.memory_cache = config.get('mappings', {})
                self.cache_timestamp = time.time()
                print(f"✅ 映射数据已加载到内存缓存: {len(self.memory_cache)} 个厂商")
        except Exception as e:
            print(f"❌ 加载映射数据到内存失败: {e}")
            self.memory_cache = {}
    
    def _sync_to_database(self):
        """同步数据到数据库"""
        if not self.memory_cache:
            print("⚠️ 内存缓存为空，跳过数据库同步")
            return
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 清空现有数据
            cursor.execute('DELETE FROM studio_alternative_mappings')
            cursor.execute('DELETE FROM studio_mappings')
            
            # 插入映射数据
            for studio, mapping_info in self.memory_cache.items():
                # 插入主映射
                cursor.execute('''
                    INSERT INTO studio_mappings 
                    (studio, mapping_type, primary_mapping, mapping_count)
                    VALUES (?, ?, ?, ?)
                ''', (
                    studio,
                    mapping_info['type'],
                    mapping_info['primary'],
                    mapping_info['count']
                ))
                
                # 插入备选映射
                for i, alt_mapping in enumerate(mapping_info.get('alternatives', [])):
                    cursor.execute('''
                        INSERT INTO studio_alternative_mappings
                        (studio, alternative_mapping, priority)
                        VALUES (?, ?, ?)
                    ''', (studio, alt_mapping, i + 1))
            
            # 更新元数据
            cursor.execute('''
                INSERT OR REPLACE INTO mapping_metadata (key, value)
                VALUES ('last_sync', ?)
            ''', (datetime.now().isoformat(),))
            
            cursor.execute('''
                INSERT OR REPLACE INTO mapping_metadata (key, value)
                VALUES ('total_studios', ?)
            ''', (str(len(self.memory_cache)),))
            
            conn.commit()
            conn.close()
            print(f"✅ 映射数据已同步到数据库: {len(self.memory_cache)} 个厂商")
            
        except Exception as e:
            print(f"❌ 同步映射数据到数据库失败: {e}")
    
    def get_mapping_info(self, studio: str) -> Dict:
        """获取映射信息 - 智能缓存策略"""
        # 1. 优先从内存缓存获取
        if self._is_cache_valid() and studio in self.memory_cache:
            return self._format_mapping_info(self.memory_cache[studio])
        
        # 2. 缓存过期或未命中，尝试重新加载
        if not self._is_cache_valid():
            self._load_to_memory_cache()
            if studio in self.memory_cache:
                return self._format_mapping_info(self.memory_cache[studio])
        
        # 3. 内存缓存失败，从数据库查询
        if self.enable_db_sync:
            db_result = self._query_from_database(studio)
            if db_result:
                return db_result
        
        # 4. 都失败了，返回空结果
        return self._create_empty_mapping_info()
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if not self.cache_timestamp:
            return False
        return (time.time() - self.cache_timestamp) < self.cache_ttl
    
    def _query_from_database(self, studio: str) -> Optional[Dict]:
        """从数据库查询映射信息"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 查询主映射
            cursor.execute('''
                SELECT mapping_type, primary_mapping, mapping_count
                FROM studio_mappings WHERE studio = ?
            ''', (studio,))
            
            main_result = cursor.fetchone()
            if not main_result:
                conn.close()
                return None
            
            mapping_type, primary_mapping, mapping_count = main_result
            
            # 查询备选映射
            cursor.execute('''
                SELECT alternative_mapping FROM studio_alternative_mappings
                WHERE studio = ? ORDER BY priority
            ''', (studio,))
            
            alternatives = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            
            return {
                'has_mapping': True,
                'type': mapping_type,
                'primary': primary_mapping,
                'alternatives': alternatives,
                'all_mappings': [primary_mapping] + alternatives,
                'count': mapping_count,
                'source': 'database'
            }
            
        except Exception as e:
            print(f"❌ 数据库查询映射信息失败: {e}")
            return None
    
    def _format_mapping_info(self, raw_info: Dict) -> Dict:
        """格式化映射信息"""
        return {
            'has_mapping': True,
            'type': raw_info['type'],
            'primary': raw_info['primary'],
            'alternatives': raw_info.get('alternatives', []),
            'all_mappings': [raw_info['primary']] + raw_info.get('alternatives', []),
            'count': raw_info['count'],
            'source': 'memory_cache'
        }
    
    def _create_empty_mapping_info(self) -> Dict:
        """创建空映射信息"""
        return {
            'has_mapping': False,
            'type': 'unknown',
            'primary': None,
            'alternatives': [],
            'all_mappings': [],
            'count': 0,
            'source': 'not_found'
        }
    
    def should_show_multiple_results(self, studio: str) -> bool:
        """判断是否应该显示多个结果"""
        mapping_info = self.get_mapping_info(studio)
        return mapping_info['has_mapping'] and mapping_info['type'] == 'multiple'
    
    def refresh_cache(self):
        """手动刷新缓存"""
        self._load_to_memory_cache()
        if self.enable_db_sync:
            self._sync_to_database()
    
    def get_statistics(self) -> Dict:
        """获取映射统计信息"""
        if not self.memory_cache:
            return {}
        
        single_count = sum(1 for info in self.memory_cache.values() if info['type'] == 'single')
        multiple_count = sum(1 for info in self.memory_cache.values() if info['type'] == 'multiple')
        
        return {
            'total_studios': len(self.memory_cache),
            'single_mapping_studios': single_count,
            'multiple_mapping_studios': multiple_count,
            'cache_valid': self._is_cache_valid(),
            'last_updated': datetime.fromtimestamp(self.cache_timestamp) if self.cache_timestamp else None
        }


if __name__ == "__main__":
    # 测试映射管理器
    print("🧪 测试映射管理器")
    manager = MappingManager()
    
    # 测试统计信息
    stats = manager.get_statistics()
    print(f"📊 统计信息: {stats}")
    
    # 测试几个映射查询
    test_studios = ['GDRD', 'MOND', 'UNKNOWN']
    for studio in test_studios:
        info = manager.get_mapping_info(studio)
        print(f"🔍 {studio}: {info['type']} - {info.get('primary', 'N/A')}")
