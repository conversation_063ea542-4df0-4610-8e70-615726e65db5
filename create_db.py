import sqlite3
import os
import json

# 确保目录存在
os.makedirs('mmp', exist_ok=True)

# 创建数据库
conn = sqlite3.connect('mmp/fast_dmm.db')
cursor = conn.cursor()

# 创建表
cursor.execute('''
    CREATE TABLE IF NOT EXISTS studio_mappings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        studio TEXT UNIQUE NOT NULL,
        mapping_type TEXT NOT NULL,
        primary_mapping TEXT NOT NULL,
        mapping_count INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
''')

cursor.execute('''
    CREATE TABLE IF NOT EXISTS studio_alternative_mappings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        studio TEXT NOT NULL,
        alternative_mapping TEXT NOT NULL,
        priority INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
''')

# 加载JSON数据
with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

mappings = data.get('mappings', {})

# 插入数据
for studio, mapping_info in mappings.items():
    cursor.execute('''
        INSERT OR REPLACE INTO studio_mappings 
        (studio, mapping_type, primary_mapping, mapping_count)
        VALUES (?, ?, ?, ?)
    ''', (
        studio,
        mapping_info['type'],
        mapping_info['primary'],
        mapping_info['count']
    ))
    
    # 删除旧的备选映射
    cursor.execute('DELETE FROM studio_alternative_mappings WHERE studio = ?', (studio,))
    
    # 插入备选映射
    for i, alt_mapping in enumerate(mapping_info.get('alternatives', [])):
        cursor.execute('''
            INSERT INTO studio_alternative_mappings
            (studio, alternative_mapping, priority)
            VALUES (?, ?, ?)
        ''', (studio, alt_mapping, i + 1))

conn.commit()

# 验证数据
cursor.execute('SELECT COUNT(*) FROM studio_mappings')
main_count = cursor.fetchone()[0]

cursor.execute('SELECT COUNT(*) FROM studio_alternative_mappings')
alt_count = cursor.fetchone()[0]

conn.close()

print(f"数据库创建成功!")
print(f"主映射: {main_count} 个")
print(f"备选映射: {alt_count} 个")
print(f"数据库文件: {os.path.abspath('mmp/fast_dmm.db')}")
