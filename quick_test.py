#!/usr/bin/env python3
"""
快速测试脚本
"""
import json
import os

def test_json_loading():
    """测试JSON文件加载"""
    print("🔍 测试JSON文件加载...")
    
    try:
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        print(f"✅ JSON加载成功，包含 {len(mappings)} 个厂商映射")
        
        # 测试几个映射
        test_studios = ['GDRD', 'MOND', 'SSIS']
        for studio in test_studios:
            if studio in mappings:
                info = mappings[studio]
                print(f"   {studio}: {info['type']} - {info['primary']}")
                if info.get('alternatives'):
                    print(f"      备选: {', '.join(info['alternatives'])}")
            else:
                print(f"   {studio}: 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON加载失败: {e}")
        return False

def test_database_creation():
    """测试数据库创建"""
    print("\n🔧 测试数据库创建...")
    
    try:
        import sqlite3
        
        # 确保目录存在
        os.makedirs('mmp', exist_ok=True)
        
        # 创建数据库
        conn = sqlite3.connect('mmp/fast_dmm.db')
        cursor = conn.cursor()
        
        # 创建测试表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY,
                name TEXT
            )
        ''')
        
        # 插入测试数据
        cursor.execute("INSERT INTO test_table (name) VALUES ('test')")
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 快速测试")
    print("=" * 40)
    
    json_ok = test_json_loading()
    db_ok = test_database_creation()
    
    print("\n" + "=" * 40)
    if json_ok and db_ok:
        print("✅ 所有测试通过")
    else:
        print("❌ 部分测试失败")
