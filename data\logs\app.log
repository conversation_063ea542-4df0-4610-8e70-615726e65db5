2025-07-24 16:32:11,985 - __main__ - ERROR - 应用启动失败: name '<PERSON><PERSON>' is not defined
2025-07-24 16:34:40,859 - app.ui.pages.search - ERROR - 渲染搜索页面失败: 'SearchPage' object has no attribute 'render_search_tips'
2025-07-24 16:35:55,590 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:36:04,148 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:36:04,207 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:36:09,982 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:36:14,972 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:37:31,941 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:39:13,557 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:39:16,631 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:39:43,985 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:42:19,213 - app.ui.pages.search - ERROR - 渲染搜索页面失败: 'SearchPage' object has no attribute 'render_search_tips'
2025-07-24 16:47:33,292 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:47:55,929 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:47:59,233 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:47:59,293 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:48:01,835 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:48:01,896 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:48:02,416 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:48:02,474 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:48:02,668 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:48:02,725 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:48:02,884 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:48:02,943 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:48:03,102 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:48:03,160 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:49:56,076 - app.ui.pages.monitor - ERROR - 渲染实时监控页面失败: 'FileMonitor' object has no attribute 'get_recent_activities'
2025-07-24 16:51:45,657 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:53:01,686 - app.ui.pages.monitor - ERROR - 渲染实时监控页面失败: 'FileMonitor' object has no attribute 'get_recent_activities'
2025-07-24 16:56:03,472 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 16:56:38,587 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 17:00:01,348 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 17:00:37,872 - app.core.activation.validator - INFO - 激活数据保存成功
2025-07-24 17:00:47,637 - app.core.activation.validator - INFO - 激活数据保存成功
2025-07-24 17:01:34,357 - app.ui.pages.monitor - ERROR - 渲染实时监控页面失败: 'FileMonitor' object has no attribute 'get_recent_activities'
2025-07-24 17:02:04,451 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 17:02:54,601 - app.ui.pages.monitor - ERROR - 渲染实时监控页面失败: 'FileMonitor' object has no attribute 'get_recent_activities'
2025-07-24 17:02:57,870 - app.ui.pages.monitor - ERROR - 渲染实时监控页面失败: 'FileMonitor' object has no attribute 'get_recent_activities'
2025-07-24 17:27:05,586 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 17:27:14,794 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
2025-07-24 17:27:14,892 - app.ui.app - ERROR - 应用运行失败: st.session_state has no attribute "app_initialized". Did you forget to initialize it? More info: https://docs.streamlit.io/develop/concepts/architecture/session-state#initialization
