{"summary": {"total_studios": 50, "total_prefixes": 53, "total_valid_prefixes": 49, "overall_success_rate": 0.9245283018867925, "verification_time": 135.7237937450409, "total_requests": 91, "avg_requests_per_minute": 40.22876055363357, "timestamp": "2025-07-25T12:01:41.626398"}, "status_distribution": {"excellent": 46, "good": 3, "poor": 0, "failed": 1, "error": 0, "no_mappings": 0}, "range_discoveries": {"0-99": ["GDRD:gdrd", "NSFS:nsfs", "GMA:gma", "GAJK:gajk", "NGHJ:nghj", "DNJR:dnjr", "HOWS:hows", "CHRV:chrv", "MOND:18mond", "ALDN:aldn", "DGCEMD:dgcemd", "CEMD:cemd", "EKDV:49ekdv", "CRNX:crnx", "MKON:mkon", "URKK:urkk", "MMUS:mmus", "KNIP:knip", "PFAS:pfas", "BASJ:basj", "BAGR:bagr", "BACJ:bacj", "AVSA:avsa", "SONE:sone", "TEK:tek", "ROE:roe", "JUR:jur", "ACHJ:achj", "IPZZ:ipzz", "SVCAO:1svcao", "START:1start", "SDMM:1sdmm", "SDHS:1sdhs", "SDAB:1sdab", "MDON:mdon", "HZGD:h_1100hzgd", "NACR:h_237nacr", "HNAMH:1hnamh", "FWAY:fway", "MFCT:h_1711mfct", "DAL:h_1711dal", "TUPP:1tupp", "GINAV:h_1350ginav", "TKFC:h_1783tkfc", "GOJU:h_1165goju", "ROP:433rop", "CLOT:h_237clot"], "100-199": ["MGT:h_1711mgt", "SDJS:1sdjs"]}, "intelligent_config": {"min_delay": 0.1, "max_delay": 0.5, "max_requests_per_minute": 100, "batch_size": 25, "batch_delay": 15, "probe_points": [1, 50, 100, 150, 200, 300, 500, 800, 1000, 1100, 1500], "max_probe_tests": 8, "early_stop_on_success": true, "adaptive_probing": true, "early_stop": true}, "detailed_results": {"GDRD": {"studio": "GDRD", "total_prefixes": 1, "valid_prefixes": ["gdrd"], "invalid_prefixes": [], "prefix_details": {"gdrd": {"prefix": "gdrd", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.2958016395568848, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "NSFS": {"studio": "NSFS", "total_prefixes": 1, "valid_prefixes": ["nsfs"], "invalid_prefixes": [], "prefix_details": {"nsfs": {"prefix": "nsfs", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.486999750137329, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "GMA": {"studio": "GMA", "total_prefixes": 1, "valid_prefixes": ["gma"], "invalid_prefixes": [], "prefix_details": {"gma": {"prefix": "gma", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.1434416770935059, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "GAJK": {"studio": "GAJK", "total_prefixes": 1, "valid_prefixes": ["gajk"], "invalid_prefixes": [], "prefix_details": {"gajk": {"prefix": "gajk", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.4038803577423096, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "NGHJ": {"studio": "NGHJ", "total_prefixes": 1, "valid_prefixes": ["nghj"], "invalid_prefixes": [], "prefix_details": {"nghj": {"prefix": "nghj", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.234605550765991, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "DNJR": {"studio": "DNJR", "total_prefixes": 1, "valid_prefixes": ["dnjr"], "invalid_prefixes": [], "prefix_details": {"dnjr": {"prefix": "dnjr", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.95589017868042, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "HOWS": {"studio": "HOWS", "total_prefixes": 1, "valid_prefixes": ["hows"], "invalid_prefixes": [], "prefix_details": {"hows": {"prefix": "hows", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.240232467651367, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "CHRV": {"studio": "CHRV", "total_prefixes": 1, "valid_prefixes": ["chrv"], "invalid_prefixes": [], "prefix_details": {"chrv": {"prefix": "chrv", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.2154664993286133, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MOND": {"studio": "MOND", "total_prefixes": 2, "valid_prefixes": ["18mond"], "invalid_prefixes": ["mond"], "prefix_details": {"mond": {"prefix": "mond", "successful_numbers": [], "failed_numbers": [1, 50, 100, 150, 200, 300, 500, 800], "total_tests": 8, "success_count": 0, "is_valid": false, "confidence": 0.0, "strategy": "universal_probe"}, "18mond": {"prefix": "18mond", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 0.5, "verification_time": 7.054500579833984, "status": "good", "total_requests": 9, "verification_method": "intelligent_range_probing"}, "ALDN": {"studio": "ALDN", "total_prefixes": 1, "valid_prefixes": ["aldn"], "invalid_prefixes": [], "prefix_details": {"aldn": {"prefix": "aldn", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.436777114868164, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "DGCEMD": {"studio": "DGCEMD", "total_prefixes": 1, "valid_prefixes": ["dgcemd"], "invalid_prefixes": [], "prefix_details": {"dgcemd": {"prefix": "dgcemd", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.3636550903320312, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "CEMD": {"studio": "CEMD", "total_prefixes": 1, "valid_prefixes": ["cemd"], "invalid_prefixes": [], "prefix_details": {"cemd": {"prefix": "cemd", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.657944679260254, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "EKDV": {"studio": "EKDV", "total_prefixes": 2, "valid_prefixes": ["49ekdv"], "invalid_prefixes": ["ekdv"], "prefix_details": {"ekdv": {"prefix": "ekdv", "successful_numbers": [], "failed_numbers": [1, 50, 100, 150, 200, 300, 500, 800], "total_tests": 8, "success_count": 0, "is_valid": false, "confidence": 0.0, "strategy": "universal_probe"}, "49ekdv": {"prefix": "49ekdv", "successful_numbers": [50], "failed_numbers": [1], "total_tests": 2, "success_count": 1, "is_valid": true, "confidence": 0.5, "strategy": "universal_probe"}}, "success_rate": 0.5, "verification_time": 7.353535890579224, "status": "good", "total_requests": 10, "verification_method": "intelligent_range_probing"}, "CRNX": {"studio": "CRNX", "total_prefixes": 1, "valid_prefixes": ["crnx"], "invalid_prefixes": [], "prefix_details": {"crnx": {"prefix": "crnx", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.752964496612549, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MKON": {"studio": "MKON", "total_prefixes": 1, "valid_prefixes": ["mkon"], "invalid_prefixes": [], "prefix_details": {"mkon": {"prefix": "mkon", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.036414384841919, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "URKK": {"studio": "URKK", "total_prefixes": 1, "valid_prefixes": ["urkk"], "invalid_prefixes": [], "prefix_details": {"urkk": {"prefix": "urkk", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.36027455329895, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MMUS": {"studio": "MMUS", "total_prefixes": 1, "valid_prefixes": ["mmus"], "invalid_prefixes": [], "prefix_details": {"mmus": {"prefix": "mmus", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.2593226432800293, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "KNIP": {"studio": "KNIP", "total_prefixes": 1, "valid_prefixes": ["knip"], "invalid_prefixes": [], "prefix_details": {"knip": {"prefix": "knip", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.5614831447601318, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "PFAS": {"studio": "PFAS", "total_prefixes": 1, "valid_prefixes": ["pfas"], "invalid_prefixes": [], "prefix_details": {"pfas": {"prefix": "pfas", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.6657166481018066, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MGT": {"studio": "MGT", "total_prefixes": 2, "valid_prefixes": ["h_1711mgt"], "invalid_prefixes": ["118mgt"], "prefix_details": {"118mgt": {"prefix": "118mgt", "successful_numbers": [], "failed_numbers": [1, 50, 100, 150, 200, 300, 500, 800], "total_tests": 8, "success_count": 0, "is_valid": false, "confidence": 0.0, "strategy": "universal_probe"}, "h_1711mgt": {"prefix": "h_1711mgt", "successful_numbers": [150], "failed_numbers": [1, 50, 100], "total_tests": 4, "success_count": 1, "is_valid": true, "confidence": 0.25, "strategy": "universal_probe"}}, "success_rate": 0.5, "verification_time": 10.785471200942993, "status": "good", "total_requests": 12, "verification_method": "intelligent_range_probing"}, "MAAN": {"studio": "MAAN", "total_prefixes": 1, "valid_prefixes": [], "invalid_prefixes": ["h_1711maan"], "prefix_details": {"h_1711maan": {"prefix": "h_1711maan", "successful_numbers": [], "failed_numbers": [1, 50, 100, 150, 200, 300, 500, 800], "total_tests": 8, "success_count": 0, "is_valid": false, "confidence": 0.0, "strategy": "universal_probe"}}, "success_rate": 0.0, "verification_time": 6.244067907333374, "status": "failed", "total_requests": 8, "verification_method": "intelligent_range_probing"}, "BASJ": {"studio": "BASJ", "total_prefixes": 1, "valid_prefixes": ["basj"], "invalid_prefixes": [], "prefix_details": {"basj": {"prefix": "basj", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 3.03617787361145, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "BAGR": {"studio": "BAGR", "total_prefixes": 1, "valid_prefixes": ["bagr"], "invalid_prefixes": [], "prefix_details": {"bagr": {"prefix": "bagr", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.5817019939422607, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "BACJ": {"studio": "BACJ", "total_prefixes": 1, "valid_prefixes": ["bacj"], "invalid_prefixes": [], "prefix_details": {"bacj": {"prefix": "bacj", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.3986809253692627, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "AVSA": {"studio": "AVSA", "total_prefixes": 1, "valid_prefixes": ["avsa"], "invalid_prefixes": [], "prefix_details": {"avsa": {"prefix": "avsa", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.332951307296753, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "SONE": {"studio": "SONE", "total_prefixes": 1, "valid_prefixes": ["sone"], "invalid_prefixes": [], "prefix_details": {"sone": {"prefix": "sone", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.2472972869873047, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "TEK": {"studio": "TEK", "total_prefixes": 1, "valid_prefixes": ["tek"], "invalid_prefixes": [], "prefix_details": {"tek": {"prefix": "tek", "successful_numbers": [50], "failed_numbers": [1], "total_tests": 2, "success_count": 1, "is_valid": true, "confidence": 0.5, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.5780961513519287, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "ROE": {"studio": "ROE", "total_prefixes": 1, "valid_prefixes": ["roe"], "invalid_prefixes": [], "prefix_details": {"roe": {"prefix": "roe", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.0821280479431152, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "JUR": {"studio": "JUR", "total_prefixes": 1, "valid_prefixes": ["jur"], "invalid_prefixes": [], "prefix_details": {"jur": {"prefix": "jur", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.4111955165863037, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "ACHJ": {"studio": "ACHJ", "total_prefixes": 1, "valid_prefixes": ["achj"], "invalid_prefixes": [], "prefix_details": {"achj": {"prefix": "achj", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.4410085678100586, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "IPZZ": {"studio": "IPZZ", "total_prefixes": 1, "valid_prefixes": ["ipzz"], "invalid_prefixes": [], "prefix_details": {"ipzz": {"prefix": "ipzz", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.1269023418426514, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "SVCAO": {"studio": "SVCAO", "total_prefixes": 1, "valid_prefixes": ["1svcao"], "invalid_prefixes": [], "prefix_details": {"1svcao": {"prefix": "1svcao", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.3330070972442627, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "START": {"studio": "START", "total_prefixes": 1, "valid_prefixes": ["1start"], "invalid_prefixes": [], "prefix_details": {"1start": {"prefix": "1start", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.4591395854949951, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "SDMM": {"studio": "SDMM", "total_prefixes": 1, "valid_prefixes": ["1sdmm"], "invalid_prefixes": [], "prefix_details": {"1sdmm": {"prefix": "1sdmm", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.2568771839141846, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "SDJS": {"studio": "SDJS", "total_prefixes": 1, "valid_prefixes": ["1sdjs"], "invalid_prefixes": [], "prefix_details": {"1sdjs": {"prefix": "1sdjs", "successful_numbers": [100], "failed_numbers": [1, 50], "total_tests": 3, "success_count": 1, "is_valid": true, "confidence": 0.3333333333333333, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 3.6241185665130615, "status": "excellent", "total_requests": 3, "verification_method": "intelligent_range_probing"}, "SDHS": {"studio": "SDHS", "total_prefixes": 1, "valid_prefixes": ["1sdhs"], "invalid_prefixes": [], "prefix_details": {"1sdhs": {"prefix": "1sdhs", "successful_numbers": [50], "failed_numbers": [1], "total_tests": 2, "success_count": 1, "is_valid": true, "confidence": 0.5, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.238814115524292, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "SDAB": {"studio": "SDAB", "total_prefixes": 1, "valid_prefixes": ["1sdab"], "invalid_prefixes": [], "prefix_details": {"1sdab": {"prefix": "1sdab", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.1879510879516602, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MDON": {"studio": "MDON", "total_prefixes": 1, "valid_prefixes": ["mdon"], "invalid_prefixes": [], "prefix_details": {"mdon": {"prefix": "mdon", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.561767578125, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "HZGD": {"studio": "HZGD", "total_prefixes": 1, "valid_prefixes": ["h_1100hzgd"], "invalid_prefixes": [], "prefix_details": {"h_1100hzgd": {"prefix": "h_1100hzgd", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.3879170417785645, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "NACR": {"studio": "NACR", "total_prefixes": 1, "valid_prefixes": ["h_237nacr"], "invalid_prefixes": [], "prefix_details": {"h_237nacr": {"prefix": "h_237nacr", "successful_numbers": [50], "failed_numbers": [1], "total_tests": 2, "success_count": 1, "is_valid": true, "confidence": 0.5, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.8506267070770264, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "HNAMH": {"studio": "HNAMH", "total_prefixes": 1, "valid_prefixes": ["1hnamh"], "invalid_prefixes": [], "prefix_details": {"1hnamh": {"prefix": "1hnamh", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.318753480911255, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "FWAY": {"studio": "FWAY", "total_prefixes": 1, "valid_prefixes": ["fway"], "invalid_prefixes": [], "prefix_details": {"fway": {"prefix": "fway", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.1240234375, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MFCT": {"studio": "MFCT", "total_prefixes": 1, "valid_prefixes": ["h_1711mfct"], "invalid_prefixes": [], "prefix_details": {"h_1711mfct": {"prefix": "h_1711mfct", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.213031530380249, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "DAL": {"studio": "DAL", "total_prefixes": 1, "valid_prefixes": ["h_1711dal"], "invalid_prefixes": [], "prefix_details": {"h_1711dal": {"prefix": "h_1711dal", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.1635503768920898, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "TUPP": {"studio": "TUPP", "total_prefixes": 1, "valid_prefixes": ["1tupp"], "invalid_prefixes": [], "prefix_details": {"1tupp": {"prefix": "1tupp", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.5506196022033691, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "GINAV": {"studio": "GINAV", "total_prefixes": 1, "valid_prefixes": ["h_1350ginav"], "invalid_prefixes": [], "prefix_details": {"h_1350ginav": {"prefix": "h_1350ginav", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.40079665184021, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "TKFC": {"studio": "TKFC", "total_prefixes": 1, "valid_prefixes": ["h_1783tkfc"], "invalid_prefixes": [], "prefix_details": {"h_1783tkfc": {"prefix": "h_1783tkfc", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.3635671138763428, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "GOJU": {"studio": "GOJU", "total_prefixes": 1, "valid_prefixes": ["h_1165goju"], "invalid_prefixes": [], "prefix_details": {"h_1165goju": {"prefix": "h_1165goju", "successful_numbers": [50], "failed_numbers": [1], "total_tests": 2, "success_count": 1, "is_valid": true, "confidence": 0.5, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.2520527839660645, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "ROP": {"studio": "ROP", "total_prefixes": 1, "valid_prefixes": ["433rop"], "invalid_prefixes": [], "prefix_details": {"433rop": {"prefix": "433rop", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 2.01505970954895, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "CLOT": {"studio": "CLOT", "total_prefixes": 1, "valid_prefixes": ["h_237clot"], "invalid_prefixes": [], "prefix_details": {"h_237clot": {"prefix": "h_237clot", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0, "strategy": "universal_probe"}}, "success_rate": 1.0, "verification_time": 1.6680293083190918, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}}}