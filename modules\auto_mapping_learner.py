#!/usr/bin/env python3
"""
自动映射学习系统
当搜索成功但映射不存在时，自动学习并更新映射数据库
"""

import json
import os
import re
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import sqlite3


class AutoMappingLearner:
    """自动映射学习器"""
    
    def __init__(self, config_file: str = "studio_mappings_all.json", 
                 db_file: str = "mmp/fast_dmm.db",
                 learning_log_file: str = "mapping_learning_log.json"):
        self.config_file = config_file
        self.db_file = db_file
        self.learning_log_file = learning_log_file
        
        # 学习统计
        self.learning_stats = {
            "total_learned": 0,
            "new_studios": 0,
            "updated_mappings": 0,
            "last_update": None
        }
        
        # 加载现有学习日志
        self._load_learning_log()
    
    def _load_learning_log(self):
        """加载学习日志"""
        try:
            if os.path.exists(self.learning_log_file):
                with open(self.learning_log_file, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
                    self.learning_stats = log_data.get('stats', self.learning_stats)
        except Exception as e:
            print(f"⚠️ 加载学习日志失败: {e}")
    
    def _save_learning_log(self, learned_mapping: Dict):
        """保存学习日志"""
        try:
            log_data = {
                "stats": self.learning_stats,
                "last_learned": learned_mapping,
                "timestamp": datetime.now().isoformat()
            }
            
            with open(self.learning_log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"⚠️ 保存学习日志失败: {e}")
    
    def learn_from_successful_search(self, code: str, found_cid: str, 
                                   search_source: str = "dmm_tools") -> Dict:
        """
        从成功的搜索中学习映射关系
        
        Args:
            code: 原始番号 (如: NEO-834)
            found_cid: 找到的CID (如: 433neo00834)
            search_source: 搜索来源
            
        Returns:
            学习结果字典
        """
        print(f"🎓 开始学习映射: {code} -> {found_cid}")
        
        # 1. 解析番号
        parsed_code = self._parse_code(code)
        if not parsed_code:
            return {"success": False, "message": "无法解析番号格式"}
        
        studio, number = parsed_code
        
        # 2. 分析CID模式
        cid_analysis = self._analyze_cid_pattern(found_cid, studio, number)
        if not cid_analysis["success"]:
            return {"success": False, "message": f"无法分析CID模式: {cid_analysis['message']}"}
        
        extracted_prefix = cid_analysis["prefix"]
        
        # 3. 检查是否需要学习
        learning_decision = self._should_learn_mapping(studio, extracted_prefix)
        if not learning_decision["should_learn"]:
            return {
                "success": False, 
                "message": f"跳过学习: {learning_decision['reason']}",
                "action": "skipped"
            }
        
        # 4. 执行学习
        learning_result = self._execute_learning(studio, extracted_prefix, code, found_cid, search_source)
        
        if learning_result["success"]:
            self.learning_stats["total_learned"] += 1
            if learning_result["action"] == "new_studio":
                self.learning_stats["new_studios"] += 1
            elif learning_result["action"] == "updated_mapping":
                self.learning_stats["updated_mappings"] += 1
            
            self.learning_stats["last_update"] = datetime.now().isoformat()
            
            # 保存学习日志
            learned_mapping = {
                "studio": studio,
                "prefix": extracted_prefix,
                "example_code": code,
                "example_cid": found_cid,
                "source": search_source,
                "action": learning_result["action"]
            }
            self._save_learning_log(learned_mapping)
        
        return learning_result
    
    def _parse_code(self, code: str) -> Optional[Tuple[str, str]]:
        """解析番号，返回(厂商, 数字)"""
        code = code.strip().upper().replace(' ', '')
        
        patterns = [
            r'^([A-Z]+)-(\d+)$',  # NEO-834
            r'^([A-Z]+)(\d+)$',   # NEO834
        ]
        
        for pattern in patterns:
            match = re.match(pattern, code)
            if match:
                return match.group(1), match.group(2)
        
        return None
    
    def _analyze_cid_pattern(self, cid: str, studio: str, number: str) -> Dict:
        """分析CID模式，提取前缀"""
        cid_lower = cid.lower()
        studio_lower = studio.lower()
        
        # 常见模式分析
        patterns = [
            # 数字前缀模式: 433neo00834
            (r'^(\d+)([a-z]+)\d+$', "数字前缀"),
            # h_前缀模式: h_1240milk00251
            (r'^(h_\d+)([a-z]+)\d+$', "h_前缀"),
            # 简单前缀模式: ssis00001
            (r'^([a-z]+)\d+$', "简单前缀"),
            # 复杂前缀模式: mbddneo00834
            (r'^([a-z]+)([a-z]+)\d+$', "复杂前缀")
        ]
        
        for pattern, pattern_type in patterns:
            match = re.match(pattern, cid_lower)
            if match:
                if pattern_type == "数字前缀":
                    prefix = match.group(1) + match.group(2)  # 433neo
                elif pattern_type == "h_前缀":
                    prefix = match.group(1) + match.group(2)  # h_1240milk
                elif pattern_type == "简单前缀":
                    prefix = match.group(1)  # ssis
                elif pattern_type == "复杂前缀":
                    # 需要更智能的判断
                    full_prefix = match.group(1) + match.group(2)
                    if studio_lower in full_prefix:
                        prefix = full_prefix
                    else:
                        prefix = match.group(1)
                
                return {
                    "success": True,
                    "prefix": prefix,
                    "pattern_type": pattern_type,
                    "confidence": self._calculate_confidence(studio, prefix, pattern_type)
                }
        
        return {
            "success": False,
            "message": f"无法识别CID模式: {cid}"
        }
    
    def _calculate_confidence(self, studio: str, prefix: str, pattern_type: str) -> float:
        """计算映射置信度"""
        confidence = 0.5  # 基础置信度
        
        # 如果前缀包含厂商名，增加置信度
        if studio.lower() in prefix.lower():
            confidence += 0.3
        
        # 根据模式类型调整置信度
        pattern_confidence = {
            "数字前缀": 0.8,
            "h_前缀": 0.9,
            "简单前缀": 0.7,
            "复杂前缀": 0.6
        }
        
        confidence = max(confidence, pattern_confidence.get(pattern_type, 0.5))
        
        return min(confidence, 1.0)
    
    def _should_learn_mapping(self, studio: str, prefix: str) -> Dict:
        """判断是否应该学习这个映射"""
        try:
            # 检查现有映射
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            mappings = data.get('mappings', {})
            
            if studio in mappings:
                existing_mapping = mappings[studio]
                existing_primary = existing_mapping['primary']
                
                if existing_primary == prefix:
                    return {
                        "should_learn": False,
                        "reason": f"映射已存在且相同: {studio} -> {prefix}"
                    }
                elif prefix in existing_mapping.get('alternatives', []):
                    return {
                        "should_learn": False,
                        "reason": f"映射已存在于备选项中: {studio} -> {prefix}"
                    }
                else:
                    # 可能需要更新为多重映射
                    return {
                        "should_learn": True,
                        "reason": f"发现新的映射变体: {studio} -> {prefix} (现有: {existing_primary})",
                        "action": "add_alternative"
                    }
            else:
                return {
                    "should_learn": True,
                    "reason": f"新厂商映射: {studio} -> {prefix}",
                    "action": "new_studio"
                }
                
        except Exception as e:
            return {
                "should_learn": False,
                "reason": f"检查现有映射失败: {e}"
            }
    
    def _execute_learning(self, studio: str, prefix: str, example_code: str, 
                         example_cid: str, source: str) -> Dict:
        """执行学习操作"""
        try:
            # 1. 加载现有配置
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            mappings = data.get('mappings', {})
            single_mappings = data.get('single_mappings', {})
            multi_mappings = data.get('multi_mappings', {})
            
            action = "unknown"
            
            if studio in mappings:
                # 更新现有映射为多重映射
                existing_mapping = mappings[studio]
                existing_primary = existing_mapping['primary']
                
                if existing_primary != prefix:
                    # 转换为多重映射
                    alternatives = existing_mapping.get('alternatives', [])
                    if prefix not in alternatives:
                        alternatives.append(prefix)
                    
                    mappings[studio] = {
                        "primary": existing_primary,
                        "alternatives": alternatives,
                        "count": len(alternatives) + 1,
                        "type": "multiple"
                    }
                    
                    # 更新分类映射
                    if studio in single_mappings:
                        del single_mappings[studio]
                    
                    multi_mappings[studio] = mappings[studio]
                    action = "updated_mapping"
                    
                    print(f"✅ 更新为多重映射: {studio} -> {existing_primary} + {alternatives}")
            else:
                # 添加新的单一映射
                mappings[studio] = {
                    "primary": prefix,
                    "alternatives": [],
                    "count": 1,
                    "type": "single"
                }
                
                single_mappings[studio] = mappings[studio]
                action = "new_studio"
                
                print(f"✅ 添加新映射: {studio} -> {prefix}")
            
            # 2. 保存更新后的配置
            data['mappings'] = mappings
            data['single_mappings'] = single_mappings
            data['multi_mappings'] = multi_mappings
            
            # 创建备份
            backup_file = f"{self.config_file}.backup.{int(time.time())}"
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 保存主文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 3. 更新数据库（如果存在）
            self._update_database_mapping(studio, prefix, action)
            
            return {
                "success": True,
                "message": f"成功学习映射: {studio} -> {prefix}",
                "action": action,
                "backup_file": backup_file
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"学习失败: {e}",
                "action": "failed"
            }
    
    def _update_database_mapping(self, studio: str, prefix: str, action: str):
        """更新数据库中的映射"""
        try:
            if not os.path.exists(self.db_file):
                print(f"⚠️ 数据库文件不存在: {self.db_file}")
                return False

            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            # 开始事务
            cursor.execute('BEGIN TRANSACTION')

            try:
                if action == "new_studio":
                    # 插入新映射
                    cursor.execute('''
                        INSERT OR REPLACE INTO studio_mappings
                        (studio, mapping_type, primary_mapping, mapping_count, updated_at)
                        VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (studio, "single", prefix, 1))

                    print(f"✅ 新增厂商映射: {studio} -> {prefix}")

                elif action == "updated_mapping":
                    # 检查是否已存在该备选映射
                    cursor.execute('''
                        SELECT COUNT(*) FROM studio_alternative_mappings
                        WHERE studio = ? AND alternative_mapping = ?
                    ''', (studio, prefix))

                    if cursor.fetchone()[0] == 0:
                        # 更新主映射表
                        cursor.execute('''
                            UPDATE studio_mappings
                            SET mapping_type = ?, mapping_count = mapping_count + 1, updated_at = CURRENT_TIMESTAMP
                            WHERE studio = ?
                        ''', ("multiple", studio))

                        # 添加备选映射
                        cursor.execute('''
                            INSERT INTO studio_alternative_mappings
                            (studio, alternative_mapping, priority)
                            VALUES (?, ?, (
                                SELECT COALESCE(MAX(priority), 0) + 1
                                FROM studio_alternative_mappings
                                WHERE studio = ?
                            ))
                        ''', (studio, prefix, studio))

                        print(f"✅ 添加备选映射: {studio} -> {prefix}")
                    else:
                        print(f"💡 备选映射已存在: {studio} -> {prefix}")

                # 提交事务
                cursor.execute('COMMIT')

                # 验证更新结果
                verification_result = self._verify_database_update(cursor, studio, prefix)
                if verification_result:
                    print(f"✅ 数据库映射验证成功: {studio} -> {prefix}")
                    return True
                else:
                    print(f"⚠️ 数据库映射验证失败: {studio} -> {prefix}")
                    return False

            except Exception as e:
                # 回滚事务
                cursor.execute('ROLLBACK')
                raise e

        except Exception as e:
            print(f"❌ 数据库更新失败: {e}")
            return False
        finally:
            if 'conn' in locals():
                conn.close()

    def _verify_database_update(self, cursor, studio: str, prefix: str) -> bool:
        """验证数据库更新是否成功"""
        try:
            # 检查主映射表
            cursor.execute('''
                SELECT mapping_type, primary_mapping, mapping_count
                FROM studio_mappings
                WHERE studio = ?
            ''', (studio,))

            main_result = cursor.fetchone()
            if not main_result:
                return False

            mapping_type, primary_mapping, mapping_count = main_result

            # 验证主映射或备选映射
            if primary_mapping == prefix:
                return True
            elif mapping_type == "multiple":
                # 检查备选映射表
                cursor.execute('''
                    SELECT COUNT(*) FROM studio_alternative_mappings
                    WHERE studio = ? AND alternative_mapping = ?
                ''', (studio, prefix))

                return cursor.fetchone()[0] > 0

            return False

        except Exception as e:
            print(f"⚠️ 验证数据库更新失败: {e}")
            return False
    
    def sync_database_with_config(self) -> Dict:
        """同步数据库与JSON配置文件"""
        try:
            print("🔄 开始同步数据库与配置文件...")

            # 加载JSON配置
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            mappings = data.get('mappings', {})

            if not os.path.exists(self.db_file):
                print(f"⚠️ 数据库文件不存在: {self.db_file}")
                return {"success": False, "message": "数据库文件不存在"}

            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            sync_stats = {
                "total_studios": len(mappings),
                "synced_studios": 0,
                "new_studios": 0,
                "updated_studios": 0,
                "errors": []
            }

            for studio, mapping_info in mappings.items():
                try:
                    # 检查数据库中是否存在该厂商
                    cursor.execute('SELECT * FROM studio_mappings WHERE studio = ?', (studio,))
                    existing = cursor.fetchone()

                    primary_mapping = mapping_info.get('primary', '')
                    alternatives = mapping_info.get('alternatives', [])
                    mapping_type = mapping_info.get('type', 'single')
                    mapping_count = mapping_info.get('count', 1)

                    if existing:
                        # 更新现有记录
                        cursor.execute('''
                            UPDATE studio_mappings
                            SET mapping_type = ?, primary_mapping = ?, mapping_count = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE studio = ?
                        ''', (mapping_type, primary_mapping, mapping_count, studio))

                        # 清除旧的备选映射
                        cursor.execute('DELETE FROM studio_alternative_mappings WHERE studio = ?', (studio,))

                        sync_stats["updated_studios"] += 1
                    else:
                        # 插入新记录
                        cursor.execute('''
                            INSERT INTO studio_mappings
                            (studio, mapping_type, primary_mapping, mapping_count)
                            VALUES (?, ?, ?, ?)
                        ''', (studio, mapping_type, primary_mapping, mapping_count))

                        sync_stats["new_studios"] += 1

                    # 插入备选映射
                    for i, alt_mapping in enumerate(alternatives):
                        cursor.execute('''
                            INSERT INTO studio_alternative_mappings
                            (studio, alternative_mapping, priority)
                            VALUES (?, ?, ?)
                        ''', (studio, alt_mapping, i + 1))

                    sync_stats["synced_studios"] += 1

                except Exception as e:
                    error_msg = f"同步厂商 {studio} 失败: {e}"
                    sync_stats["errors"].append(error_msg)
                    print(f"⚠️ {error_msg}")

            conn.commit()
            conn.close()

            print(f"✅ 数据库同步完成:")
            print(f"   总厂商数: {sync_stats['total_studios']}")
            print(f"   成功同步: {sync_stats['synced_studios']}")
            print(f"   新增厂商: {sync_stats['new_studios']}")
            print(f"   更新厂商: {sync_stats['updated_studios']}")
            print(f"   错误数量: {len(sync_stats['errors'])}")

            return {
                "success": True,
                "message": "数据库同步完成",
                "stats": sync_stats
            }

        except Exception as e:
            error_msg = f"数据库同步失败: {e}"
            print(f"❌ {error_msg}")
            return {"success": False, "message": error_msg}

    def validate_database_integrity(self) -> Dict:
        """验证数据库完整性"""
        try:
            print("🔍 开始验证数据库完整性...")

            if not os.path.exists(self.db_file):
                return {"success": False, "message": "数据库文件不存在"}

            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            validation_results = {
                "total_studios": 0,
                "valid_studios": 0,
                "orphaned_alternatives": 0,
                "missing_primaries": 0,
                "inconsistent_counts": 0,
                "errors": []
            }

            # 检查主映射表
            cursor.execute('SELECT COUNT(*) FROM studio_mappings')
            validation_results["total_studios"] = cursor.fetchone()[0]

            # 检查每个厂商的映射完整性
            cursor.execute('SELECT studio, mapping_type, primary_mapping, mapping_count FROM studio_mappings')

            for studio, mapping_type, primary_mapping, mapping_count in cursor.fetchall():
                try:
                    # 检查主映射是否为空
                    if not primary_mapping:
                        validation_results["missing_primaries"] += 1
                        validation_results["errors"].append(f"厂商 {studio} 缺少主映射")
                        continue

                    # 检查备选映射数量
                    cursor.execute('SELECT COUNT(*) FROM studio_alternative_mappings WHERE studio = ?', (studio,))
                    alt_count = cursor.fetchone()[0]

                    expected_count = alt_count + 1  # 主映射 + 备选映射

                    if mapping_count != expected_count:
                        validation_results["inconsistent_counts"] += 1
                        validation_results["errors"].append(
                            f"厂商 {studio} 映射数量不一致: 记录={mapping_count}, 实际={expected_count}"
                        )

                    validation_results["valid_studios"] += 1

                except Exception as e:
                    validation_results["errors"].append(f"验证厂商 {studio} 失败: {e}")

            # 检查孤立的备选映射
            cursor.execute('''
                SELECT COUNT(*) FROM studio_alternative_mappings a
                LEFT JOIN studio_mappings m ON a.studio = m.studio
                WHERE m.studio IS NULL
            ''')
            validation_results["orphaned_alternatives"] = cursor.fetchone()[0]

            conn.close()

            print(f"✅ 数据库完整性验证完成:")
            print(f"   总厂商数: {validation_results['total_studios']}")
            print(f"   有效厂商: {validation_results['valid_studios']}")
            print(f"   孤立备选映射: {validation_results['orphaned_alternatives']}")
            print(f"   缺少主映射: {validation_results['missing_primaries']}")
            print(f"   数量不一致: {validation_results['inconsistent_counts']}")
            print(f"   错误数量: {len(validation_results['errors'])}")

            is_valid = (validation_results["orphaned_alternatives"] == 0 and
                       validation_results["missing_primaries"] == 0 and
                       validation_results["inconsistent_counts"] == 0)

            return {
                "success": True,
                "valid": is_valid,
                "message": "数据库完整性验证完成",
                "results": validation_results
            }

        except Exception as e:
            error_msg = f"数据库完整性验证失败: {e}"
            print(f"❌ {error_msg}")
            return {"success": False, "message": error_msg}

    def get_learning_statistics(self) -> Dict:
        """获取学习统计信息"""
        return {
            "stats": self.learning_stats,
            "config_file": self.config_file,
            "db_file": self.db_file,
            "log_file": self.learning_log_file
        }
