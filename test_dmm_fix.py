#!/usr/bin/env python3
"""
测试DMM爬虫修复效果
"""
import sys
import os
sys.path.append('.')

def main():
    print("🔍 测试DMM爬虫修复效果...")
    
    try:
        print("1. 测试基本导入...")
        import requests
        import time
        from bs4 import BeautifulSoup
        from urllib.parse import quote
        print("   ✅ 基本模块导入成功")
        
        print("2. 测试DMM爬虫模块导入...")
        from modules.dmm_search_crawler import DMMSearchCrawler
        print("   ✅ DMM爬虫模块导入成功")
        
        print("3. 测试爬虫初始化...")
        crawler = DMMSearchCrawler()
        print("   ✅ 爬虫初始化成功")
        print(f"   年龄验证状态: {crawler.age_verified}")
        
        print("4. 测试搜索格式生成...")
        studio = "ID"
        number = 21
        studio_lower = studio.lower()
        search_key = f"{studio_lower}{number:05d}"
        print(f"   生成搜索关键词: {search_key}")
        
        encoded_key = quote(search_key)
        search_url = f"https://video.dmm.co.jp/av/list/?key={encoded_key}"
        print(f"   生成搜索URL: {search_url}")
        print("   ✅ 搜索格式生成成功")
        
        print("\n🎉 所有基本测试通过！")
        
        # 询问是否进行网络测试
        user_input = input("\n是否进行网络测试？(y/N): ").strip().lower()
        
        if user_input == 'y':
            print("\n5. 测试网络爬取...")
            print("   ⚠️ 注意: 此测试将访问真实的DMM网站")
            
            start_time = time.time()
            mappings = crawler._crawl_single_number("ID", 21)
            end_time = time.time()
            
            print(f"\n📊 爬取结果:")
            print(f"   耗时: {end_time - start_time:.2f} 秒")
            print(f"   发现映射: {len(mappings)} 个")
            
            if mappings:
                print(f"\n📋 发现的CID:")
                for i, mapping in enumerate(mappings, 1):
                    print(f"   {i:2d}. {mapping['cid']} -> {mapping['prefix']} (置信度: {mapping['confidence']:.2f})")
                
                # 验证是否包含预期的CID
                expected_cids = [
                    "5533id00021", "5532id00021", "5531id00021", "5530id00021",
                    "5529id00021", "h_113id00021", "5526id00021", "5524id00021",
                    "5525id00021", "5522id00021", "5521id00021", "5519id00021"
                ]
                
                found_cids = [m['cid'] for m in mappings]
                matched_count = sum(1 for cid in expected_cids if cid in found_cids)
                
                print(f"\n🎯 验证结果:")
                print(f"   预期CID: {len(expected_cids)} 个")
                print(f"   匹配CID: {matched_count} 个")
                print(f"   匹配率: {matched_count/len(expected_cids)*100:.1f}%")
                
                if matched_count > 0:
                    print("   ✅ 网络测试成功")
                else:
                    print("   ⚠️ 未找到预期的CID")
            else:
                print("   ❌ 未发现任何映射")
        else:
            print("   💡 跳过网络测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n💡 基本功能正常")
    else:
        print("\n⚠️ 基本功能存在问题，需要修复")
