#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试DMM搜索机制
"""

import re
from typing import Optional, Tuple, List


def parse_code(code: str) -> Optional[Tuple[str, int]]:
    """解析番号"""
    code = code.strip().upper().replace(' ', '')
    
    patterns = [
        r'^([A-Z]+)-(\d+)$',  # MILK-251
        r'^([A-Z]+)(\d+)$',   # MILK251
    ]
    
    for pattern in patterns:
        match = re.match(pattern, code)
        if match:
            studio = match.group(1)
            number = int(match.group(2))
            return studio, number
    
    return None


def generate_dmm_cid(studio: str, number: int) -> Optional[str]:
    """生成DMM CID"""
    studio_mappings = {
        'MILK': 'h_1240milk',
        'SSIS': 'h_1116ssis', 
        'STARS': 'h_1116stars',
        'PRED': 'h_1116pred',
        'IPX': 'h_1116ipx',
        'MIDE': 'h_1116mide',
    }
    
    if studio in studio_mappings:
        dmm_prefix = studio_mappings[studio]
        padded_number = str(number).zfill(5)
        return f"{dmm_prefix}{padded_number}"
    return None


def generate_variants(studio: str, number: int) -> List[str]:
    """生成番号变体"""
    variants = []
    
    # 基础格式
    variants.append(f"{studio}-{number}")
    variants.append(f"{studio}{number}")
    
    # 补零格式
    for width in [3, 4, 5]:
        padded = str(number).zfill(width)
        variants.append(f"{studio}-{padded}")
        variants.append(f"{studio}{padded}")
        variants.append(f"{studio}0{padded}")
        variants.append(f"{studio}00{padded}")
    
    return list(set(variants))


def test_search():
    """测试搜索功能"""
    print("=== DMM番号搜索机制演示 ===\n")
    
    test_cases = [
        "MILK-251",    # 你提到的例子
        "MILK251",     # 无分隔符
        "MILK00251",   # 补零格式
        "SSIS-001",    # 其他厂商
    ]
    
    for code in test_cases:
        print(f"输入番号: {code}")
        print("-" * 50)
        
        # 解析番号
        parsed = parse_code(code)
        if not parsed:
            print("❌ 解析失败")
            continue
        
        studio, number = parsed
        print(f"✅ 解析成功: 厂商={studio}, 数字={number}")
        
        # 生成DMM CID
        dmm_cid = generate_dmm_cid(studio, number)
        if dmm_cid:
            url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={dmm_cid}/"
            print(f"✅ DMM CID: {dmm_cid}")
            print(f"✅ 详情页URL: {url}")
        else:
            print(f"❌ 厂商 {studio} 不在支持列表中")
        
        # 生成变体
        variants = generate_variants(studio, number)
        print(f"✅ 生成 {len(variants)} 个变体:")
        for i, variant in enumerate(variants[:8], 1):  # 只显示前8个
            print(f"   {i}. {variant}")
        if len(variants) > 8:
            print(f"   ... 还有 {len(variants) - 8} 个变体")
        
        print()


def explain_mechanism():
    """解释搜索机制"""
    print("=== DMM快速搜索的秘密 ===\n")
    
    print("🔍 问题分析:")
    print("你遇到的问题是：自己写的搜索规则慢且不准确")
    print("而DMM网站能快速处理各种番号格式\n")
    
    print("💡 DMM的解决方案:")
    print("1. 预建数据库索引")
    print("   - 将所有可能的番号变体都存储在数据库中")
    print("   - MILK-251, MILK251, MILK00251 等都指向同一个视频")
    print()
    
    print("2. 标准化映射规则")
    print("   - 每个厂商有固定的DMM内部前缀")
    print("   - MILK → h_1240milk")
    print("   - 数字部分统一补零到5位")
    print()
    
    print("3. 多重索引策略")
    print("   - 原始格式索引: MILK-251")
    print("   - 标准化索引: MILK00251") 
    print("   - DMM内部ID: h_1240milk00251")
    print()
    
    print("⚡ 为什么这样快:")
    print("✅ 预计算: 所有变体都预先生成，无需实时计算")
    print("✅ 索引优化: 数据库索引支持毫秒级查找")
    print("✅ 内存缓存: 热门数据常驻内存")
    print("✅ 规则简单: 固定的映射规则，无需复杂逻辑")
    print()
    
    print("🛠️ 你的解决方案:")
    print("1. 建立本地番号数据库")
    print("2. 预生成所有常用番号的变体")
    print("3. 使用SQLite建立高效索引")
    print("4. 实现智能搜索算法")


if __name__ == "__main__":
    test_search()
    print("=" * 60)
    print()
    explain_mechanism()
