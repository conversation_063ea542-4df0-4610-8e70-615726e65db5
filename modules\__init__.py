# MMA Pro 模块化功能包
"""
MMA Pro 功能模块化包
提供各种独立的功能模块，保持与原有UI的兼容性
"""

__version__ = "1.0.0"
__author__ = "MMA Pro Team"

# 模块导入
try:
    from .search_detail import SearchDetailModule
    from .search_detail_ui import SearchDetailUI
    from .ui_utils import UIUtils
except ImportError as e:
    print(f"模块导入警告: {e}")
    SearchDetailModule = None
    SearchDetailUI = None
    UIUtils = None

__all__ = [
    'SearchDetailModule',
    'SearchDetailUI',
    'UIUtils'
]
