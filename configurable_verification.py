#!/usr/bin/env python3
"""
可配置的高速验证工具
用户可以自定义所有参数，包括速度设置
"""
import sys
import json
import time
import random
from datetime import datetime
from typing import Dict, List, Tuple
sys.path.append('.')

class ConfigurableVerifier:
    """可配置验证器"""
    
    def __init__(self):
        self.crawler = None
        self.verification_results = {}
        self.start_time = None
        self.request_count = 0
        self.last_request_time = 0
        self.request_times = []
        
        # 默认配置（用户可修改）
        self.config = {
            # 速度控制
            'min_delay': 1.0,      # 最小请求间隔（秒）- 可调整
            'max_delay': 2.0,      # 最大请求间隔（秒）- 可调整
            'max_requests_per_minute': 20,  # 每分钟最大请求数 - 可调整
            'batch_size': 30,      # 批次大小 - 可调整
            'batch_delay': 30,     # 批次间休息时间（秒）- 可调整
            
            # 验证策略
            'test_numbers': [1, 100, 200, 300, 500, 275, 250, 150, 50, 999],  # 测试番号
            'min_success_for_valid': 1,     # 至少成功几个认为有效
            'max_tests_per_prefix': 3,      # 每个前缀最多测试几个番号
            'early_stop_on_success': True,  # 成功一个就停止
            
            # 错误处理
            'error_threshold': 5,   # 连续错误阈值
            'cooldown_time': 180,   # 冷却时间（秒）
        }
    
    def show_current_config(self):
        """显示当前配置"""
        print("⚙️ 当前验证配置:")
        print(f"   🚀 速度设置:")
        print(f"      请求间隔: {self.config['min_delay']}-{self.config['max_delay']} 秒")
        print(f"      最大频率: {self.config['max_requests_per_minute']} 请求/分钟")
        print(f"      批次大小: {self.config['batch_size']} 个厂商")
        print(f"      批次休息: {self.config['batch_delay']} 秒")
        
        print(f"   🧪 验证策略:")
        print(f"      测试番号: {self.config['test_numbers']}")
        print(f"      每前缀最多测试: {self.config['max_tests_per_prefix']} 个")
        print(f"      成功阈值: {self.config['min_success_for_valid']} 个")
        print(f"      早停策略: {'开启' if self.config['early_stop_on_success'] else '关闭'}")
    
    def customize_config(self):
        """用户自定义配置"""
        print("\n🔧 配置自定义（直接回车保持默认值）:")
        
        # 速度设置
        print("\n🚀 速度设置:")
        
        min_delay = input(f"   最小请求间隔 (当前: {self.config['min_delay']}秒): ").strip()
        if min_delay and min_delay.replace('.', '').isdigit():
            self.config['min_delay'] = float(min_delay)
        
        max_delay = input(f"   最大请求间隔 (当前: {self.config['max_delay']}秒): ").strip()
        if max_delay and max_delay.replace('.', '').isdigit():
            self.config['max_delay'] = float(max_delay)
        
        max_freq = input(f"   每分钟最大请求数 (当前: {self.config['max_requests_per_minute']}): ").strip()
        if max_freq and max_freq.isdigit():
            self.config['max_requests_per_minute'] = int(max_freq)
        
        batch_size = input(f"   批次大小 (当前: {self.config['batch_size']}): ").strip()
        if batch_size and batch_size.isdigit():
            self.config['batch_size'] = int(batch_size)
        
        batch_delay = input(f"   批次休息时间 (当前: {self.config['batch_delay']}秒): ").strip()
        if batch_delay and batch_delay.isdigit():
            self.config['batch_delay'] = int(batch_delay)
        
        # 验证策略
        print("\n🧪 验证策略:")
        
        test_numbers = input(f"   测试番号列表 (当前: {self.config['test_numbers']}, 用逗号分隔): ").strip()
        if test_numbers:
            try:
                numbers = [int(x.strip()) for x in test_numbers.split(',')]
                self.config['test_numbers'] = numbers
            except:
                print("   ⚠️ 番号格式错误，保持默认")
        
        max_tests = input(f"   每前缀最多测试数 (当前: {self.config['max_tests_per_prefix']}): ").strip()
        if max_tests and max_tests.isdigit():
            self.config['max_tests_per_prefix'] = int(max_tests)
        
        early_stop = input(f"   早停策略 (当前: {'开启' if self.config['early_stop_on_success'] else '关闭'}, y/n): ").strip().lower()
        if early_stop in ['y', 'yes']:
            self.config['early_stop_on_success'] = True
        elif early_stop in ['n', 'no']:
            self.config['early_stop_on_success'] = False
        
        print("\n✅ 配置更新完成！")
        self.show_current_config()
    
    def get_speed_preset(self):
        """获取速度预设"""
        print("\n🚀 选择速度预设:")
        print("   1. 🐌 安全模式 (3-6秒间隔, 8请求/分钟)")
        print("   2. 🚶 标准模式 (1-3秒间隔, 15请求/分钟)")
        print("   3. 🏃 快速模式 (0.5-1.5秒间隔, 30请求/分钟)")
        print("   4. 🚀 极速模式 (0.2-0.8秒间隔, 60请求/分钟) ⚠️ 风险较高")
        print("   5. 🔧 自定义配置")
        
        choice = input("选择预设 (1-5): ").strip()
        
        if choice == '1':  # 安全模式
            self.config.update({
                'min_delay': 3.0, 'max_delay': 6.0,
                'max_requests_per_minute': 8,
                'batch_size': 15, 'batch_delay': 60
            })
        elif choice == '2':  # 标准模式
            self.config.update({
                'min_delay': 1.0, 'max_delay': 3.0,
                'max_requests_per_minute': 15,
                'batch_size': 25, 'batch_delay': 45
            })
        elif choice == '3':  # 快速模式
            self.config.update({
                'min_delay': 0.5, 'max_delay': 1.5,
                'max_requests_per_minute': 30,
                'batch_size': 40, 'batch_delay': 30
            })
        elif choice == '4':  # 极速模式
            self.config.update({
                'min_delay': 0.2, 'max_delay': 0.8,
                'max_requests_per_minute': 60,
                'batch_size': 50, 'batch_delay': 20
            })
            print("⚠️ 极速模式风险提醒:")
            print("   - 可能触发反爬机制")
            print("   - 建议小批量测试")
            confirm = input("确认使用极速模式？(y/N): ").strip().lower()
            if confirm != 'y':
                return self.get_speed_preset()
        elif choice == '5':  # 自定义
            self.customize_config()
            return
        else:
            print("无效选择，使用标准模式")
            choice = '2'
        
        print(f"✅ 已应用预设配置")
        self.show_current_config()
    
    def safe_delay(self):
        """安全延迟"""
        delay = random.uniform(self.config['min_delay'], self.config['max_delay'])
        if delay >= 1.0:
            print(f"         😴 等待 {delay:.1f} 秒...")
        time.sleep(delay)
        
        current_time = time.time()
        self.request_times.append(current_time)
        self.last_request_time = current_time
        self.request_count += 1
        
        # 清理1分钟前的记录
        one_minute_ago = current_time - 60
        self.request_times = [t for t in self.request_times if t > one_minute_ago]
    
    def check_rate_limit(self):
        """频率检查"""
        current_time = time.time()
        one_minute_ago = current_time - 60
        self.request_times = [t for t in self.request_times if t > one_minute_ago]
        
        recent_requests = len(self.request_times)
        
        if recent_requests >= self.config['max_requests_per_minute']:
            wait_time = 60
            print(f"⚠️ 达到频率限制: {recent_requests}/{self.config['max_requests_per_minute']}")
            print(f"😴 等待 {wait_time} 秒...")
            time.sleep(wait_time)
            return True
        
        return True
    
    def initialize_crawler(self):
        """初始化DMM爬虫"""
        print("🔧 初始化DMM爬虫...")
        
        try:
            from modules.dmm_search_crawler import DMMSearchCrawler
            
            self.crawler = DMMSearchCrawler()
            
            if not self.crawler.age_verified:
                print("❌ 年龄验证失败")
                return False
            
            print("✅ DMM爬虫初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 爬虫初始化失败: {e}")
            return False
    
    def load_database_mappings(self):
        """加载数据库映射"""
        print("📖 加载数据库映射...")
        
        try:
            with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'mappings' not in data:
                print("❌ 映射数据格式错误")
                return None
            
            mappings = data['mappings']
            print(f"✅ 加载了 {len(mappings)} 个厂商映射")
            
            return mappings
            
        except Exception as e:
            print(f"❌ 加载映射数据失败: {e}")
            return None
    
    def verify_prefix(self, prefix: str) -> Dict:
        """验证单个前缀"""
        result = {
            'prefix': prefix,
            'tested_numbers': [],
            'successful_numbers': [],
            'failed_numbers': [],
            'total_tests': 0,
            'success_count': 0,
            'is_valid': False,
            'confidence': 0.0
        }
        
        print(f"      🧪 验证前缀: {prefix}")
        
        test_numbers = self.config['test_numbers'][:self.config['max_tests_per_prefix']]
        
        for test_num in test_numbers:
            try:
                # 检查频率限制
                self.check_rate_limit()
                
                # 构建测试CID
                test_cid = f"{prefix}{test_num:05d}"
                detail_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={test_cid}/"
                
                print(f"         🔍 测试: {test_cid}")
                
                # 安全延迟
                self.safe_delay()
                
                # 验证CID
                if self.crawler._verify_cid_exists(detail_url, test_cid):
                    result['successful_numbers'].append(test_num)
                    result['success_count'] += 1
                    print(f"         ✅ 成功: {test_cid}")
                    
                    # 早停策略
                    if self.config['early_stop_on_success']:
                        print(f"         🎯 早停：已验证前缀有效")
                        break
                else:
                    result['failed_numbers'].append(test_num)
                    print(f"         ❌ 失败: {test_cid}")
                
                result['tested_numbers'].append(test_num)
                result['total_tests'] += 1
                
            except Exception as e:
                print(f"         ⚠️ 测试异常: {e}")
                result['failed_numbers'].append(test_num)
                result['total_tests'] += 1
                continue
        
        # 判断前缀是否有效
        result['is_valid'] = result['success_count'] >= self.config['min_success_for_valid']
        
        # 修复置信度计算
        if result['total_tests'] > 0:
            result['confidence'] = result['success_count'] / result['total_tests']
        elif result['success_count'] > 0:  # 早停情况
            result['confidence'] = 1.0
        
        print(f"      📊 前缀验证结果: {prefix}")
        print(f"         测试数: {result['total_tests']}")
        print(f"         成功数: {result['success_count']}")
        print(f"         有效性: {'✅ 有效' if result['is_valid'] else '❌ 无效'}")
        print(f"         置信度: {result['confidence']:.2f}")
        
        return result
    
    def verify_studio_mapping(self, studio: str, mapping_data: Dict) -> Dict:
        """验证单个厂商的映射"""
        print(f"\n🔬 验证厂商: {studio}")
        
        result = {
            'studio': studio,
            'total_prefixes': 0,
            'valid_prefixes': [],
            'invalid_prefixes': [],
            'prefix_details': {},
            'success_rate': 0.0,
            'verification_time': 0,
            'status': 'unknown',
            'total_requests': 0
        }
        
        try:
            start_time = time.time()
            
            # 获取映射信息
            primary = mapping_data.get('primary', '')
            alternatives = mapping_data.get('alternatives', [])
            
            all_prefixes = [primary] + alternatives if primary else alternatives
            result['total_prefixes'] = len(all_prefixes)
            
            print(f"   总前缀数: {len(all_prefixes)}")
            
            if not all_prefixes:
                result['status'] = 'no_mappings'
                return result
            
            request_count_start = self.request_count
            
            # 验证每个前缀
            for prefix in all_prefixes:
                prefix_result = self.verify_prefix(prefix)
                result['prefix_details'][prefix] = prefix_result
                
                if prefix_result['is_valid']:
                    result['valid_prefixes'].append(prefix)
                else:
                    result['invalid_prefixes'].append(prefix)
            
            result['total_requests'] = self.request_count - request_count_start
            
            # 计算成功率
            valid_count = len(result['valid_prefixes'])
            total_count = len(all_prefixes)
            result['success_rate'] = valid_count / total_count if total_count > 0 else 0
            
            # 确定状态
            if result['success_rate'] >= 0.8:
                result['status'] = 'excellent'
            elif result['success_rate'] >= 0.5:
                result['status'] = 'good'
            elif result['success_rate'] >= 0.2:
                result['status'] = 'poor'
            else:
                result['status'] = 'failed'
            
            result['verification_time'] = time.time() - start_time
            
            print(f"   📊 验证完成:")
            print(f"      有效前缀: {valid_count}/{total_count} ({result['success_rate']:.1%})")
            print(f"      总请求数: {result['total_requests']}")
            
            return result
            
        except Exception as e:
            print(f"   ❌ 验证异常: {e}")
            result['status'] = 'error'
            result['error'] = str(e)
            return result
    
    def batch_verification(self, mappings: Dict, start_index: int = 0, limit: int = None) -> Dict:
        """批量验证"""
        print(f"\n🚀 开始可配置批量验证...")
        
        self.start_time = time.time()
        studios = list(mappings.keys())[start_index:]
        
        if limit:
            studios = studios[:limit]
            print(f"⚠️ 限制验证数量: {limit} 个厂商")
        
        print(f"📋 计划验证 {len(studios)} 个厂商")
        
        for i, studio in enumerate(studios, 1):
            print(f"\n{'='*60}")
            print(f"进度: {i}/{len(studios)} ({i/len(studios):.1%})")
            
            # 批次休息
            if i > 1 and (i - 1) % self.config['batch_size'] == 0:
                print(f"😴 批次休息 {self.config['batch_delay']} 秒...")
                time.sleep(self.config['batch_delay'])
            
            mapping_data = mappings[studio]
            result = self.verify_studio_mapping(studio, mapping_data)
            self.verification_results[studio] = result
            
            # 显示进度和统计
            elapsed = time.time() - self.start_time
            avg_time = elapsed / i
            remaining = (len(studios) - i) * avg_time
            
            print(f"⏱️ 已用时: {elapsed:.1f}s, 预计剩余: {remaining:.1f}s")
            print(f"📡 总请求数: {self.request_count}")
            print(f"📊 当前频率: {len(self.request_times)} 请求/分钟")
        
        return self.verification_results

def main():
    """主函数"""
    print("🔧 可配置高速验证工具")
    print("=" * 60)
    
    print("💡 特性:")
    print("   - 🚀 可自定义速度设置")
    print("   - 🧪 可配置验证策略")
    print("   - 📊 实时性能监控")
    print("   - ⚙️ 多种速度预设")
    
    # 初始化验证器
    verifier = ConfigurableVerifier()
    
    # 选择速度预设
    verifier.get_speed_preset()
    
    # 用户确认
    user_input = input("\n是否开始验证？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("验证已取消")
        return
    
    # 询问验证数量限制
    limit_input = input("验证数量限制 (建议20-50，回车=20): ").strip()
    limit = 20  # 默认20个
    
    if limit_input.isdigit():
        limit = int(limit_input)
    
    print(f"🎯 将验证 {limit} 个厂商")
    
    # 执行验证
    # 1. 初始化爬虫
    if not verifier.initialize_crawler():
        print("❌ 爬虫初始化失败，验证终止")
        return
    
    # 2. 加载映射数据
    mappings = verifier.load_database_mappings()
    if not mappings:
        print("❌ 映射数据加载失败，验证终止")
        return
    
    # 3. 执行验证
    print(f"\n🚀 开始验证...")
    results = verifier.batch_verification(mappings, limit=limit)
    
    # 4. 简单统计
    total_studios = len(results)
    total_requests = verifier.request_count
    total_time = time.time() - verifier.start_time
    
    print(f"\n📊 验证完成:")
    print(f"   验证厂商: {total_studios} 个")
    print(f"   总请求数: {total_requests}")
    print(f"   总耗时: {total_time:.1f} 秒")
    print(f"   平均频率: {total_requests/(total_time/60):.1f} 请求/分钟")
    
    # 5. 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"configurable_verification_report_{timestamp}.json"
    
    try:
        report_data = {
            'config': verifier.config,
            'summary': {
                'total_studios': total_studios,
                'total_requests': total_requests,
                'total_time': total_time,
                'avg_frequency': total_requests/(total_time/60) if total_time > 0 else 0
            },
            'results': results
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 详细报告已保存: {report_file}")
    except Exception as e:
        print(f"❌ 报告保存失败: {e}")

if __name__ == "__main__":
    main()
