#!/usr/bin/env python3
"""
DMM Firefox爬虫 - 避免Chrome渲染器问题
"""
import sqlite3
import time
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.firefox.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

class DMMFirefoxCrawler:
    """DMM Firefox爬虫"""
    
    def __init__(self, db_file: str = "dmm_firefox_database.db"):
        self.db_file = db_file
        self.driver = None
        
        # 简单配置
        self.config = {
            'page_load_timeout': 60,  # 增加超时时间
        }
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        print("📊 初始化数据库...")
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dmm_works (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cid TEXT UNIQUE NOT NULL,
                    title TEXT,
                    detail_url TEXT,
                    page_number INTEGER,
                    crawl_time TEXT,
                    UNIQUE(cid)
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ 数据库初始化完成")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
    
    def init_driver(self):
        """初始化Firefox浏览器"""
        print("🦊 初始化Firefox浏览器...")
        
        try:
            firefox_options = Options()
            firefox_options.add_argument('--headless')
            firefox_options.add_argument('--no-sandbox')
            firefox_options.add_argument('--disable-dev-shm-usage')
            
            self.driver = webdriver.Firefox(options=firefox_options)
            self.driver.set_page_load_timeout(self.config['page_load_timeout'])
            
            print("✅ Firefox浏览器初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ Firefox浏览器初始化失败: {e}")
            print("💡 请确保已安装Firefox和geckodriver")
            return False
    
    def extract_cid_from_url(self, url: str) -> Optional[str]:
        """从URL中提取CID"""
        try:
            cid_match = re.search(r'cid=([^&/]+)', url)
            if cid_match:
                return cid_match.group(1)
            return None
        except:
            return None
    
    def crawl_single_page(self, page_number: int) -> Tuple[bool, List[Dict]]:
        """爬取单页数据"""
        url = f"https://video.dmm.co.jp/av/list/?page={page_number}"

        try:
            print(f"   🔍 爬取第 {page_number} 页")
            print(f"      🌐 访问: {url}")

            # 访问页面
            self.driver.get(url)

            # 等待页面加载
            print(f"      ⏳ 等待页面加载...")
            time.sleep(5)

            # 检查是否被重定向到年龄验证页面
            current_url = self.driver.current_url
            if "age_check" in current_url:
                print(f"      🔐 检测到年龄验证页面，正在处理...")

                # 查找并点击"はい"按钮
                try:
                    yes_button = self.driver.find_element(By.LINK_TEXT, "はい")
                    yes_button.click()
                    print(f"      ✅ 已点击年龄验证按钮")

                    # 等待重定向完成
                    time.sleep(10)

                except NoSuchElementException:
                    print(f"      ❌ 未找到年龄验证按钮")
                    return False, []

            # 再次等待页面完全加载
            time.sleep(10)
            
            # 查找包含CID的链接
            cid_links = self.driver.find_elements(By.CSS_SELECTOR, '[href*="cid="]')
            
            if cid_links:
                print(f"      ✅ 找到 {len(cid_links)} 个CID链接")
                
                # 提取CID和URL
                works_data = []
                seen_cids = set()
                
                for link in cid_links:
                    try:
                        href = link.get_attribute('href')
                        if href:
                            cid = self.extract_cid_from_url(href)
                            if cid and cid not in seen_cids:
                                seen_cids.add(cid)
                                
                                work_info = {
                                    'cid': cid,
                                    'detail_url': href,
                                    'page_number': page_number,
                                    'crawl_time': datetime.now().isoformat()
                                }
                                works_data.append(work_info)
                    except:
                        continue
                
                print(f"      ✅ 提取到 {len(works_data)} 个唯一作品")
                return True, works_data
            else:
                print(f"      ❌ 未找到CID链接")
                
                # 保存页面源码用于调试
                with open(f'debug_firefox_page_{page_number}.html', 'w', encoding='utf-8') as f:
                    f.write(self.driver.page_source)
                print(f"      📄 页面源码已保存到 debug_firefox_page_{page_number}.html")
                
                return True, []
                
        except Exception as e:
            print(f"      ❌ 爬取异常: {e}")
            return False, []
    
    def save_works_to_db(self, works_data: List[Dict]):
        """保存作品数据到数据库"""
        if not works_data:
            return
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            for work in works_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO dmm_works 
                    (cid, title, detail_url, page_number, crawl_time)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    work.get('cid'),
                    work.get('title'),
                    work.get('detail_url'),
                    work.get('page_number'),
                    work.get('crawl_time')
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def crawl_multiple_pages(self, start_page: int = 1, end_page: int = 10):
        """爬取多页数据"""
        print("🦊 DMM Firefox多页爬虫")
        print("=" * 50)

        print(f"📋 爬取配置:")
        print(f"   页面范围: {start_page} - {end_page}")
        print(f"   预计作品数: {(end_page - start_page + 1) * 120}")
        print(f"   页面间隔: 30秒")

        # 初始化浏览器
        if not self.init_driver():
            print("❌ Firefox浏览器初始化失败")
            return

        total_works = 0
        success_pages = 0
        failed_pages = 0

        try:
            for page_num in range(start_page, end_page + 1):
                print(f"\n📄 处理第 {page_num} 页 ({page_num - start_page + 1}/{end_page - start_page + 1})")

                # 爬取单页
                success, works_data = self.crawl_single_page(page_num)

                if success:
                    # 保存数据
                    self.save_works_to_db(works_data)
                    success_pages += 1
                    total_works += len(works_data)

                    print(f"   ✅ 第 {page_num} 页完成，保存 {len(works_data)} 个作品")

                    # 显示进度统计
                    print(f"   📊 累计进度: {success_pages}页成功, {failed_pages}页失败, 总计{total_works}个作品")

                else:
                    failed_pages += 1
                    print(f"   ❌ 第 {page_num} 页失败")

                # 页面间隔（最后一页不需要等待）
                if page_num < end_page:
                    print(f"   😴 等待30秒后继续下一页...")
                    time.sleep(30)

            # 最终统计
            print(f"\n🎉 爬取完成!")
            print(f"📊 最终统计:")
            print(f"   成功页面: {success_pages}/{end_page - start_page + 1}")
            print(f"   失败页面: {failed_pages}/{end_page - start_page + 1}")
            print(f"   总作品数: {total_works}")
            print(f"   数据库文件: {self.db_file}")

        finally:
            # 关闭浏览器
            if self.driver:
                self.driver.quit()
                print("🔚 Firefox浏览器已关闭")

    def crawl_first_page(self):
        """只爬取第一页进行测试"""
        self.crawl_multiple_pages(1, 1)

def main():
    """主函数"""
    print("🦊 DMM Firefox爬虫")
    print("=" * 50)

    print("📋 说明:")
    print("   - 使用Firefox浏览器避免Chrome渲染器问题")
    print("   - 支持单页测试和多页爬取")
    print("   - 页面间隔30秒，避免被反爬")

    print("\n📋 爬取选项:")
    print("   1. 测试单页 (第1页)")
    print("   2. 爬取前10页 (推荐)")
    print("   3. 爬取前50页")
    print("   4. 自定义页面范围")

    # 用户选择
    choice = input("\n请选择爬取方式 (1-4): ").strip()

    crawler = DMMFirefoxCrawler()

    if choice == '1':
        print("🧪 开始测试第一页...")
        crawler.crawl_first_page()

    elif choice == '2':
        print("🚀 开始爬取前10页...")
        crawler.crawl_multiple_pages(1, 10)

    elif choice == '3':
        print("🚀 开始爬取前50页...")
        print("⚠️  这将需要约25分钟完成")
        confirm = input("确认继续？(y/N): ").strip().lower()
        if confirm == 'y':
            crawler.crawl_multiple_pages(1, 50)
        else:
            print("爬取已取消")

    elif choice == '4':
        try:
            start_page = int(input("起始页码: ").strip())
            end_page = int(input("结束页码: ").strip())

            if start_page < 1 or end_page < start_page or end_page > 417:
                print("❌ 页码范围无效 (1-417)")
                return

            estimated_time = (end_page - start_page + 1) * 0.5  # 每页约30秒
            print(f"🚀 开始爬取第{start_page}-{end_page}页...")
            print(f"⏰ 预计需要约{estimated_time:.1f}分钟")

            confirm = input("确认继续？(y/N): ").strip().lower()
            if confirm == 'y':
                crawler.crawl_multiple_pages(start_page, end_page)
            else:
                print("爬取已取消")

        except ValueError:
            print("❌ 请输入有效的数字")

    else:
        print("❌ 无效选择")
        return

if __name__ == "__main__":
    main()
