#!/usr/bin/env python3
"""
数据文件验证脚本
"""
import json
import os

def validate_studio_mappings():
    """验证studio_mappings_all.json文件"""
    print("🔍 开始验证数据文件...")
    
    file_path = "studio_mappings_all.json"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 加载JSON数据
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 获取各部分数据
        mappings = data.get('mappings', {})
        single_mappings = data.get('single_mappings', {})
        multi_mappings = data.get('multi_mappings', {})
        statistics = data.get('statistics', {})
        
        print("✅ JSON文件验证结果:")
        print(f"   总厂商映射: {len(mappings)} 个")
        print(f"   单一映射: {len(single_mappings)} 个")
        print(f"   多重映射: {len(multi_mappings)} 个")
        
        # 验证数据结构
        if mappings:
            sample_studio = list(mappings.keys())[0]
            sample_data = mappings[sample_studio]
            required_fields = ['primary', 'alternatives', 'count', 'type']
            
            missing_fields = [field for field in required_fields if field not in sample_data]
            if missing_fields:
                print(f"⚠️ 样本数据缺少字段: {missing_fields}")
                return False
            else:
                print("✅ 数据结构验证通过")
        
        # 验证统计信息
        if statistics:
            print("📊 统计信息:")
            for key, value in statistics.items():
                print(f"   {key}: {value}")
        
        # 验证多重映射示例
        multi_count = 0
        for studio, mapping_info in mappings.items():
            if mapping_info.get('type') == 'multiple':
                multi_count += 1
                if multi_count <= 3:  # 只显示前3个例子
                    alternatives = mapping_info.get('alternatives', [])
                    print(f"   多重映射示例 - {studio}: {mapping_info['primary']} + {len(alternatives)}个备选")
        
        print(f"✅ 数据文件验证完成，发现 {multi_count} 个多重映射厂商")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def check_fastdmm_database():
    """检查FastDMM数据库状态"""
    print("\n🔍 检查FastDMM数据库状态...")
    
    db_path = "mmp/fast_dmm.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        import sqlite3
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"✅ 数据库连接成功")
        print(f"   现有表: {', '.join(tables)}")
        
        # 检查是否已有映射表
        mapping_tables = [t for t in tables if 'mapping' in t.lower()]
        if mapping_tables:
            print(f"⚠️ 发现现有映射表: {', '.join(mapping_tables)}")
        else:
            print("✅ 没有发现映射表，可以安全创建")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 数据准备和验证")
    print("=" * 50)
    
    # 验证JSON文件
    json_valid = validate_studio_mappings()
    
    # 检查数据库
    db_valid = check_fastdmm_database()
    
    print("\n" + "=" * 50)
    if json_valid and db_valid:
        print("✅ 所有验证通过，可以继续下一步")
    else:
        print("❌ 验证失败，请检查问题后重试")
