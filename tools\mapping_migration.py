#!/usr/bin/env python3
"""
映射数据迁移和管理工具
"""
import json
import sqlite3
import os
import sys
import argparse
from datetime import datetime
from typing import Dict, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.mapping_manager import MappingManager


class MappingMigrationTool:
    """映射数据迁移和管理工具"""
    
    def __init__(self):
        self.mapping_manager = None
    
    def migrate_from_json(self, json_file: str, force: bool = False):
        """从JSON文件迁移数据"""
        print(f"🔄 开始从 {json_file} 迁移映射数据...")
        
        if not os.path.exists(json_file):
            print(f"❌ 文件不存在: {json_file}")
            return False
        
        try:
            # 创建映射管理器
            self.mapping_manager = MappingManager(
                config_file=json_file,
                db_file="mmp/fast_dmm.db",
                enable_db_sync=True
            )
            
            # 显示统计信息
            stats = self.mapping_manager.get_statistics()
            print(f"✅ 迁移完成:")
            print(f"   总厂商数: {stats.get('total_studios', 0)}")
            print(f"   单一映射: {stats.get('single_mapping_studios', 0)}")
            print(f"   多重映射: {stats.get('multiple_mapping_studios', 0)}")
            print(f"   缓存状态: {'有效' if stats.get('cache_valid', False) else '无效'}")
            
            return True
            
        except Exception as e:
            print(f"❌ 迁移失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def validate_data_consistency(self):
        """验证数据一致性"""
        print("🔍 验证映射数据一致性...")
        
        if not self.mapping_manager:
            print("❌ 请先执行迁移操作")
            return False
        
        try:
            # 检查数据库文件是否存在
            db_file = "mmp/fast_dmm.db"
            if not os.path.exists(db_file):
                print(f"❌ 数据库文件不存在: {db_file}")
                return False
            
            # 检查数据库表
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['studio_mappings', 'studio_alternative_mappings', 'mapping_metadata']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                print(f"❌ 缺少数据库表: {', '.join(missing_tables)}")
                conn.close()
                return False
            
            # 检查数据数量
            cursor.execute("SELECT COUNT(*) FROM studio_mappings")
            db_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM studio_alternative_mappings")
            alt_count = cursor.fetchone()[0]
            
            conn.close()
            
            # 获取内存缓存统计
            stats = self.mapping_manager.get_statistics()
            memory_count = stats.get('total_studios', 0)
            
            print(f"✅ 数据一致性检查:")
            print(f"   内存缓存厂商数: {memory_count}")
            print(f"   数据库主映射数: {db_count}")
            print(f"   数据库备选映射数: {alt_count}")
            
            if memory_count == db_count:
                print("✅ 数据一致性验证通过")
                return True
            else:
                print(f"⚠️ 数据不一致: 内存({memory_count}) vs 数据库({db_count})")
                return False
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def export_to_json(self, output_file: str):
        """导出数据到JSON文件"""
        print(f"📤 导出映射数据到 {output_file}...")
        
        if not self.mapping_manager:
            print("❌ 请先执行迁移操作")
            return False
        
        try:
            stats = self.mapping_manager.get_statistics()
            
            export_data = {
                'mappings': self.mapping_manager.memory_cache,
                'statistics': stats,
                'export_info': {
                    'exported_at': datetime.now().isoformat(),
                    'source': 'MappingManager',
                    'version': '1.0'
                }
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 导出完成: {len(export_data['mappings'])} 个厂商映射")
            return True
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return False
    
    def show_statistics(self):
        """显示统计信息"""
        print("📊 映射数据统计:")
        
        if not self.mapping_manager:
            # 尝试创建映射管理器
            try:
                self.mapping_manager = MappingManager()
            except Exception as e:
                print(f"❌ 无法创建映射管理器: {e}")
                return False
        
        try:
            stats = self.mapping_manager.get_statistics()
            
            for key, value in stats.items():
                print(f"   {key}: {value}")
            
            # 显示一些示例映射
            print("\n🔍 映射示例:")
            test_studios = ['GDRD', 'MOND', 'SSIS', 'AVOP']
            for studio in test_studios:
                info = self.mapping_manager.get_mapping_info(studio)
                if info['has_mapping']:
                    print(f"   {studio}: {info['type']} - {info['primary']} (来源: {info['source']})")
                    if info['alternatives']:
                        print(f"      备选: {', '.join(info['alternatives'])}")
                else:
                    print(f"   {studio}: 未找到映射")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
            return False
    
    def test_database_connection(self):
        """测试数据库连接"""
        print("🔧 测试数据库连接...")
        
        db_file = "mmp/fast_dmm.db"
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(db_file), exist_ok=True)
            
            # 测试连接
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 执行简单查询
            cursor.execute("SELECT sqlite_version()")
            version = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"✅ 数据库连接成功 (SQLite版本: {version})")
            print(f"   数据库文件: {os.path.abspath(db_file)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="映射数据迁移和管理工具")
    parser.add_argument('action', choices=['migrate', 'validate', 'export', 'stats', 'test-db'])
    parser.add_argument('--input', help='输入JSON文件路径', default='studio_mappings_all.json')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--force', action='store_true', help='强制执行操作')
    
    args = parser.parse_args()
    
    tool = MappingMigrationTool()
    success = False
    
    print("🛠️ 映射数据迁移工具")
    print("=" * 50)
    
    if args.action == 'migrate':
        success = tool.migrate_from_json(args.input, args.force)
        
    elif args.action == 'validate':
        # 先尝试加载现有数据
        if os.path.exists(args.input):
            tool.migrate_from_json(args.input)
        success = tool.validate_data_consistency()
        
    elif args.action == 'export':
        output_file = args.output or 'exported_mappings.json'
        if os.path.exists(args.input):
            tool.migrate_from_json(args.input)
        success = tool.export_to_json(output_file)
        
    elif args.action == 'stats':
        success = tool.show_statistics()
        
    elif args.action == 'test-db':
        success = tool.test_database_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 操作完成")
    else:
        print("❌ 操作失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
