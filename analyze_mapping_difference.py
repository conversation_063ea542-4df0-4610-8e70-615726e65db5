#!/usr/bin/env python3
"""
分析映射差异：为什么新爬虫发现12个，智能系统只返回8个
"""
import sys
sys.path.append('.')

def analyze_mapping_difference():
    """分析映射差异"""
    print("🔍 分析映射差异")
    print("=" * 60)
    
    # 新爬虫发现的12个CID
    new_crawler_cids = [
        "5533id00021", "5532id00021", "5531id00021", "5530id00021",
        "5529id00021", "h_113id00021", "5526id00021", "5524id00021", 
        "5525id00021", "5522id00021", "5521id00021", "5519id00021"
    ]
    
    # 智能系统返回的8个CID（从测试日志中提取）
    smart_system_cids = [
        "5531id00021", "5524id00021", "5526id00021", "5529id00021",
        "5530id00021", "5532id00021", "5533id00021", "h_113id00021"
    ]
    
    print(f"📊 数据对比:")
    print(f"   新爬虫发现: {len(new_crawler_cids)} 个CID")
    print(f"   智能系统返回: {len(smart_system_cids)} 个CID")
    
    # 找出差异
    missing_in_smart = set(new_crawler_cids) - set(smart_system_cids)
    extra_in_smart = set(smart_system_cids) - set(new_crawler_cids)
    common_cids = set(new_crawler_cids) & set(smart_system_cids)
    
    print(f"\n🔍 详细分析:")
    print(f"   共同CID: {len(common_cids)} 个")
    print(f"   新爬虫独有: {len(missing_in_smart)} 个")
    print(f"   智能系统独有: {len(extra_in_smart)} 个")
    
    if missing_in_smart:
        print(f"\n❓ 新爬虫发现但智能系统未返回的CID:")
        for cid in sorted(missing_in_smart):
            print(f"      - {cid}")
    
    if extra_in_smart:
        print(f"\n🆕 智能系统返回但新爬虫未发现的CID:")
        for cid in sorted(extra_in_smart):
            print(f"      - {cid}")
    
    print(f"\n✅ 共同的CID:")
    for cid in sorted(common_cids):
        print(f"      - {cid}")
    
    return missing_in_smart, extra_in_smart, common_cids

def check_database_mappings():
    """检查数据库中的映射数据"""
    print("\n🗄️ 检查数据库中的映射数据")
    print("-" * 40)
    
    try:
        # 检查JSON映射文件
        import json
        
        print("1. 检查JSON映射文件...")
        try:
            with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
                mappings_data = json.load(f)
            
            if 'mappings' in mappings_data and 'ID' in mappings_data['mappings']:
                id_mapping = mappings_data['mappings']['ID']
                print(f"   ID厂商映射:")
                print(f"      主映射: {id_mapping.get('primary', 'N/A')}")
                print(f"      备选映射: {id_mapping.get('alternatives', [])}")
                print(f"      总数: {id_mapping.get('count', 0)}")
                
                # 生成预期的CID
                all_prefixes = [id_mapping.get('primary', '')] + id_mapping.get('alternatives', [])
                expected_cids = [f"{prefix}00021" for prefix in all_prefixes if prefix]
                
                print(f"   基于JSON数据的预期CID ({len(expected_cids)}个):")
                for cid in expected_cids:
                    print(f"      - {cid}")
                
                return expected_cids
            else:
                print("   ❌ JSON文件中未找到ID厂商映射")
                return []
                
        except Exception as e:
            print(f"   ❌ 读取JSON文件失败: {e}")
            return []
    
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return []

def check_smart_system_logic():
    """检查智能系统的逻辑"""
    print("\n🧠 检查智能系统逻辑")
    print("-" * 30)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_engine = SearchDetailModule()
        
        print("1. 测试智能系统搜索...")
        
        # 直接调用智能系统
        result = search_engine.search_dmm_enhanced("ID-021")
        
        print(f"   搜索结果类型: {type(result)}")
        print(f"   搜索结果内容: {result}")
        
        # 如果结果是列表，分析每个项目
        if isinstance(result, dict):
            if 'results' in result:
                results = result['results']
                print(f"   结果数量: {len(results)}")
                
                print(f"   详细结果:")
                for i, item in enumerate(results, 1):
                    if isinstance(item, dict):
                        cid = item.get('cid', 'N/A')
                        print(f"      {i}. CID: {cid}")
                    else:
                        print(f"      {i}. {item}")
        
        return result
        
    except Exception as e:
        print(f"❌ 智能系统检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_filtering_logic():
    """分析可能的过滤逻辑"""
    print("\n🔧 分析可能的过滤逻辑")
    print("-" * 35)
    
    missing_cids = ["5525id00021", "5522id00021", "5521id00021", "5519id00021"]
    returned_cids = ["5531id00021", "5524id00021", "5526id00021", "5529id00021",
                     "5530id00021", "5532id00021", "5533id00021", "h_113id00021"]
    
    print("🔍 分析缺失的CID模式:")
    
    # 分析数字模式
    missing_numbers = []
    returned_numbers = []
    
    for cid in missing_cids:
        if cid.startswith('55') and cid.endswith('id00021'):
            number = cid[2:4]  # 提取中间的数字
            missing_numbers.append(number)
    
    for cid in returned_cids:
        if cid.startswith('55') and cid.endswith('id00021'):
            number = cid[2:4]  # 提取中间的数字
            returned_numbers.append(number)
    
    print(f"   缺失CID的数字: {missing_numbers}")
    print(f"   返回CID的数字: {returned_numbers}")
    
    # 分析可能的过滤规则
    print(f"\n🤔 可能的过滤原因:")
    
    # 检查是否有数字范围过滤
    all_numbers = missing_numbers + returned_numbers
    all_numbers_int = [int(n) for n in all_numbers if n.isdigit()]
    
    if all_numbers_int:
        min_returned = min([int(n) for n in returned_numbers if n.isdigit()])
        max_returned = max([int(n) for n in returned_numbers if n.isdigit()])
        
        print(f"   1. 数字范围过滤？")
        print(f"      返回的数字范围: {min_returned} - {max_returned}")
        
        for num in missing_numbers:
            if num.isdigit():
                num_int = int(num)
                if num_int < min_returned or num_int > max_returned:
                    print(f"      - {num} 超出范围")
                else:
                    print(f"      - {num} 在范围内但仍被过滤")
    
    print(f"   2. 质量评分过滤？")
    print(f"      可能基于CID的某些特征进行质量评分")
    
    print(f"   3. 重复检测过滤？")
    print(f"      可能认为某些CID过于相似")
    
    print(f"   4. 数据库同步问题？")
    print(f"      可能数据库中缺少这4个映射")

def main():
    """主函数"""
    print("🛠️ 映射差异分析工具")
    print("=" * 60)
    
    print("💡 分析目标:")
    print("   - 找出为什么新爬虫发现12个CID，智能系统只返回8个")
    print("   - 分析可能的过滤或去重逻辑")
    print("   - 检查数据库中的映射数据")
    
    # 执行分析
    print("\n" + "="*60)
    
    # 1. 基本差异分析
    missing, extra, common = analyze_mapping_difference()
    
    # 2. 检查数据库映射
    expected_from_db = check_database_mappings()
    
    # 3. 检查智能系统逻辑
    smart_result = check_smart_system_logic()
    
    # 4. 分析过滤逻辑
    analyze_filtering_logic()
    
    # 总结
    print("\n" + "="*60)
    print("📊 分析总结:")
    
    print(f"\n🔍 发现的问题:")
    print(f"   - 新爬虫验证了12个CID全部存在")
    print(f"   - 智能系统只返回了8个CID")
    print(f"   - 缺失4个CID: 5525id, 5522id, 5521id, 5519id")
    
    print(f"\n💡 可能的原因:")
    print(f"   1. 智能系统有质量过滤机制")
    print(f"   2. 数据库中可能缺少这4个映射")
    print(f"   3. 去重算法可能过于严格")
    print(f"   4. 可能有数字范围或其他过滤规则")
    
    print(f"\n🔧 建议解决方案:")
    print(f"   1. 检查智能系统的过滤配置")
    print(f"   2. 将新爬虫发现的映射添加到数据库")
    print(f"   3. 调整去重或过滤逻辑")
    print(f"   4. 使用新爬虫作为数据验证工具")

if __name__ == "__main__":
    main()
