import os
import time
import subprocess
import shutil
from concurrent.futures import Thread<PERSON>oolExecutor
from pathlib import Path
import logging
import requests
from typing import Optional, List, Union
from glob import glob  
from pathlib import Path

# Telegram Bot API配置
TELEGRAM_BOT_TOKEN = '**********************************************'
TELEGRAM_CHAT_ID = '5865544216'
TELEGRAM_API_URL = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage'

# Emby API配置
EMBY_SERVER_URL = 'http://***********:8096'
EMBY_API_KEY = '0ccf879eafef4796a7192bab06567029'

# 设定变量用于存储用户输入
#需要监控的中转文件夹路径
source_dir = '/vol1/1000/CloudDrive/115open/R+18/JAV/更新/' 
#需要存储strm的路径
strm_dir = '/vol1/1000/媒体库/115/R+18/JAV/'
#emby内映射的docker路径
docker_dir = '/Media/115/R+18/JAV/'
#最终网盘已整理库路径
library_dir = '/vol1/1000/CloudDrive/115open/R+18/已整理/更新'
#需要补全的strm路径头
cloud_dir = 'http://***********:5244/d/115/R+18/已整理/更新'


#采集待通知库信息
result_set = set() #非填写项
media_set = set() #非填写项

def configure_logging():
    prefix_name = Path(__file__).stem
    log_time = time.strftime("%Y%m%d-%H%M%S")
    prefix = prefix_name + '_'
    script_dir = os.path.dirname(os.path.abspath(__file__))
    log_dir = os.path.join(script_dir, 'logs')
    os.makedirs(log_dir, exist_ok=True)
    log_filename = os.path.join(log_dir, f'{prefix}{log_time}.log')
    logger = logging.getLogger('myapp')
    logger.setLevel(logging.DEBUG)
    # 避免重复添加handler
    if not logger.handlers:
        file_handler = logging.FileHandler(log_filename)
        file_handler.setLevel(logging.DEBUG)
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
    return logger

logger = configure_logging()

def check_and_rotate_log():
    global logger
    current_date = time.strftime("%Y%m%d")
    if not hasattr(check_and_rotate_log, "last_date"):
        check_and_rotate_log.last_date = current_date
    
    if check_and_rotate_log.last_date != current_date:
        logger = configure_logging()
        check_and_rotate_log.last_date = current_date

# 发送Telegram通知的函数
def send_telegram_notification(message):
    max_length = 4000
    for i in range(0, len(message), max_length):
        chunk = message[i:i + max_length]
        payload = {
            'chat_id': TELEGRAM_CHAT_ID,
            'text': chunk
        }
        headers = {
            'Connection': 'close'
        }
        try:
            logger.info(f"尝试发送Telegram通知: {payload}")
            response = requests.post(
                TELEGRAM_API_URL,
                data=payload,
                headers=headers,
                timeout=10
            )
            logger.info(f"Telegram响应: {response.status_code} {response.text}")
            if response.status_code != 200:
                logger.error(f"发送Telegram通知失败: {response.text}")
        except requests.exceptions.RequestException as e:
            logger.error(f"发送Telegram通知异常: {e}")
        time.sleep(1)  # 每条消息间隔1秒，防止被限流

# EmbyRefresh 类定义
class EmbyRefresh:
    def __init__(self, api_key, emby_addr):
        self.api_key = api_key
        self.emby_addr = emby_addr
        self.library_item_ids = self._get_library_item_ids()
    def _get_library_item_ids(self):
        itemIds=[]
        url = f"{self.emby_addr}/Items"
        headers = {
            "X-Emby-Token": self.api_key,
            "Content-Type": "application/json"
        }
        params = {
            "Recursive": True,
            "Fields": "Path",
            "IncludeItemTypes": "Folder"
        }
        response = requests.get(url, headers=headers, params=params)
        if response.status_code == 200:
            data = response.json()
            for item in data.get("Items", []):
                if item.get("Path") in result_set:
                    itemIds.append(item.get("Id"))
        return itemIds

    def refresh_library(self):      
        if self.library_item_ids:
            for item_id in self.library_item_ids:
                url = f"{self.emby_addr}/Items/{item_id}/Refresh"
                headers = {
                    "X-Emby-Token": self.api_key,
                    "Content-Type": "application/json"
                }
                params = {
                    "Recursive": True
                }
                response = requests.post(url, headers=headers, params=params)
                if response.status_code == 204:
                    logger.info(f"完成emby刷新通知 item ID: {item_id}.")
                else:
                    logger.info(f"Failed to refresh library for item ID: {item_id}. Status code: {response.status_code}")
                
                # 每次请求后等待 2 秒钟
                time.sleep(2)
        else:
            logger.info("Library item IDs not found.")


def cleanup_old_logs():
    # 获取当前脚本的文件名（不包括扩展名）
    prefix_name = Path(__file__).stem
    max_files=3 
    prefix = prefix_name + '_'
    script_dir = os.path.dirname(os.path.abspath(__file__)) 
    log_dir = os.path.join(script_dir, 'logs') 
    log_files = glob(os.path.join(log_dir, f'{prefix}*.log'))  
    log_files.sort(key=os.path.getmtime, reverse=True)  # 按修改时间排序  
    if len(log_files) > max_files:  
        for file in log_files[max_files:]:  # 删除超过max_files个的最旧文件  
            try:  
                os.remove(file)  
                logger.info(f'Removed old log file: {file}')  
            except OSError as e:  
                logger.info(f'Error removing file {file}: {e.strerror}') 

def get_first_two_levels_robust(file_path):  
    # 去除路径末尾的文件名部分，只留下目录部分  
    directory_path = '/'.join(file_path.split('/')[:-1])  
    # 分割目录路径  
    parts = directory_path.split('/')  
   # 根据实际情况选择返回的路径  
    if len(parts) >= 2:  
        # 如果路径至少有两个层级，返回前两个层级  
        return '/'.join(parts[:2])  
    elif len(parts) == 1:  
        # 如果只有一个层级（且不是根目录'/'），返回这个层级  
        return parts[0]  
    else:  
        # 如果路径为空或只有根目录'/'，返回根目录  
        return '/' 

def remove_empty_dirs(root_dir, keep_root=False):
    """递归删除空文件夹，默认保留根目录"""
    for dirpath, dirnames, filenames in os.walk(root_dir, topdown=False):
        logger.info(f"检查目录: {dirpath}, 子目录: {dirnames}, 文件: {filenames}")
        if not dirnames and not filenames:
            if keep_root and os.path.abspath(dirpath) == os.path.abspath(root_dir):
                logger.info(f"保留根目录: {dirpath}")
                continue
            try:
                os.rmdir(dirpath)
                logger.info(f"已删除空文件夹: {dirpath}")
            except Exception as e:
                logger.error(f"删除空文件夹失败: {dirpath}，错误: {e}")

def process_file(file_path):
    global result_set, media_set
    filename = os.path.basename(file_path)
    if filename.endswith('.py'):  
        return  # 如果是.py文件，则跳过处理        
    # 计算相对于源目录的路径（不包括源目录本身）
    relative_path = os.path.relpath(file_path, source_dir)
    directory = os.path.dirname(relative_path)
    filename_without_ext, ext = os.path.splitext(relative_path)
    media_info = os.path.join(directory, filename_without_ext)
    # 在strm_dir下创建相同的子目录结构
    strm_file_path = os.path.join(strm_dir, relative_path)
    strm_dir_path = os.path.dirname(strm_file_path)
    os.makedirs(strm_dir_path, exist_ok=True)
    # 在library_dir下创建相同的子目录结构
    library_file_path = os.path.join(library_dir, relative_path)
    library_dir_path = os.path.dirname(library_file_path)
    os.makedirs(library_dir_path, exist_ok=True)
    # 根据扩展名生成.strm文件或拷贝nfo、jpg、png文件
    base_name, extension_with_dot = os.path.splitext(filename)
    extension = extension_with_dot[1:].lower()    
    if extension.lower() in ['mp4', 'mkv', 'avi', 'ts', 'wmv']:
        strm_file = os.path.join(strm_dir_path, base_name + '.strm')
        s_path = os.path.normpath(relative_path).replace('\\', '/')  
        with open(strm_file, 'w', encoding='utf-8') as f:
            f.write(f"{cloud_dir}/{s_path}")
    elif extension.lower() in ['nfo', 'jpg', 'png', 'srt', 'ass', 'mp3']:
        shutil.copy(file_path, strm_dir_path)
    try:
        # 尝试移动文件
        head = get_first_two_levels_robust(relative_path)
        shutil.move(file_path, library_dir_path)
        emby_path = os.path.join(docker_dir, head)
        result_set.add(emby_path)
        media_set.add(filename_without_ext)
    except Exception as e:
        # 如果出现任何异常，打印错误信息
        logger.error(f"移动文件时出错: {e}")
        # 发送Telegram通知
        send_telegram_notification(f"移动文件时出错: {e}")
         
def format_time(seconds):  
    hours, remainder = divmod(seconds, 3600)  
    minutes, seconds = divmod(remainder, 60)  
    return f"{int(hours)}小时{int(minutes)}分钟{int(seconds)}秒"

# 监控文件夹的函数
def monitor_folder(source_dir):
    global result_set, media_set
    while True:
        # 检查 source_dir 是否为空
        has_content = False
        for root, dirs, files in os.walk(source_dir):
            if dirs or files:
                has_content = True
                break
        if not has_content:
            time.sleep(5)
            continue

        logger.info("开始新一轮监控循环")
        check_and_rotate_log()
        start_time = time.time()
        log_time = time.strftime("%Y%m%d-%H%M%S")
        with ThreadPoolExecutor(max_workers=8) as executor:
            for root, dirs, files in os.walk(source_dir):
                if files:
                    for file in files:
                        file_path = os.path.join(root, file)
                        logger.info(f"发现文件: {file_path}")
                        executor.submit(process_file, file_path)
        executor.shutdown(wait=True)
        logger.info("本轮文件处理完毕，开始删除空文件夹")
        remove_empty_dirs(source_dir, keep_root=True)
        end_time = time.time()
        total_time = end_time - start_time
        formatted_time = format_time(total_time)
        logger.info(f"本轮处理用时: {formatted_time}")
        cleanup_old_logs()
        if result_set:
            logger.info(f"{result_set}")
            media = "\n".join(sorted(media_set))
            send_telegram_notification(f"\n新增媒体：\n\n{media}\n\n处理完成\n")
            logger.info(f"\n新增媒体：\n\n{media}\n\n处理完成\n")
            emby_refresh = EmbyRefresh(EMBY_API_KEY, EMBY_SERVER_URL)
            emby_refresh.refresh_library()
        result_set = set()
        media_set = set()
        time.sleep(5)
        # 更新初始文件列表 

if __name__ == "__main__":   
    # 启动文件夹监控
    monitor_folder(source_dir)
