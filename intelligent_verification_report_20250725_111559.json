{"summary": {"total_studios": 20, "total_prefixes": 23, "total_valid_prefixes": 21, "overall_success_rate": 0.9130434782608695, "verification_time": 339.9207761287689, "total_requests": 80, "avg_requests_per_minute": 14.12093739801789, "timestamp": "2025-07-25T11:15:59.442813"}, "status_distribution": {"excellent": 18, "good": 2, "poor": 0, "failed": 0, "error": 0, "no_mappings": 0}, "range_discoveries": {"0-99": ["GDRD:gdrd", "NSFS:nsfs", "GMA:gma", "GAJK:gajk", "NGHJ:nghj", "DNJR:dnjr", "HOWS:hows", "CHRV:chrv", "MOND:18mond", "ALDN:aldn", "DGCEMD:dgcemd", "CEMD:cemd", "EKDV:49ekdv", "CRNX:crnx", "MKON:mkon", "URKK:urkk", "MMUS:mmus", "KNIP:knip", "PFAS:pfas"], "700-799": ["EKDV:ekdv"], "200-299": ["MGT:h_1711mgt"]}, "intelligent_config": {"min_delay": 0.5, "max_delay": 1.5, "max_requests_per_minute": 25, "batch_size": 30, "batch_delay": 30, "probe_ranges": [[1, 10], [50, 100], [200, 300], [500, 600], [700, 800], [900, 999]], "samples_per_range": 2, "min_success_for_valid": 1, "max_probe_requests": 12}, "detailed_results": {"GDRD": {"studio": "GDRD", "total_prefixes": 1, "valid_prefixes": ["gdrd"], "invalid_prefixes": [], "prefix_details": {"gdrd": {"prefix": "gdrd", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 5.605764150619507, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "NSFS": {"studio": "NSFS", "total_prefixes": 1, "valid_prefixes": ["nsfs"], "invalid_prefixes": [], "prefix_details": {"nsfs": {"prefix": "nsfs", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 5.329877138137817, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "GMA": {"studio": "GMA", "total_prefixes": 1, "valid_prefixes": ["gma"], "invalid_prefixes": [], "prefix_details": {"gma": {"prefix": "gma", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 4.774839639663696, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "GAJK": {"studio": "GAJK", "total_prefixes": 1, "valid_prefixes": ["gajk"], "invalid_prefixes": [], "prefix_details": {"gajk": {"prefix": "gajk", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 6.1553637981414795, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "NGHJ": {"studio": "NGHJ", "total_prefixes": 1, "valid_prefixes": ["nghj"], "invalid_prefixes": [], "prefix_details": {"nghj": {"prefix": "nghj", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 5.699010848999023, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "DNJR": {"studio": "DNJR", "total_prefixes": 1, "valid_prefixes": ["dnjr"], "invalid_prefixes": [], "prefix_details": {"dnjr": {"prefix": "dnjr", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 5.0829174518585205, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "HOWS": {"studio": "HOWS", "total_prefixes": 1, "valid_prefixes": ["hows"], "invalid_prefixes": [], "prefix_details": {"hows": {"prefix": "hows", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 3.215313673019409, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "CHRV": {"studio": "CHRV", "total_prefixes": 1, "valid_prefixes": ["chrv"], "invalid_prefixes": [], "prefix_details": {"chrv": {"prefix": "chrv", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 4.430648565292358, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "MOND": {"studio": "MOND", "total_prefixes": 2, "valid_prefixes": ["18mond"], "invalid_prefixes": ["mond"], "prefix_details": {"mond": {"prefix": "mond", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_50_100": {"start": 50, "end": 100, "tested": [50, 100], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_200_300": {"start": 200, "end": 300, "tested": [200, 300], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_500_600": {"start": 500, "end": 600, "tested": [500, 600], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_700_800": {"start": 700, "end": 800, "tested": [700, 800], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_900_999": {"start": 900, "end": 999, "tested": [900, 999], "success_count": 0, "total_count": 2, "success_rate": 0.0}}, "successful_numbers": [], "failed_numbers": [1, 10, 50, 100, 200, 300, 500, 600, 700, 800, 900, 999], "estimated_range": null, "total_tests": 12, "success_count": 0, "is_valid": false, "confidence": 0.0, "probe_strategy": "intelligent_range_detection"}, "18mond": {"prefix": "18mond", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 0.5, "verification_time": 83.29693174362183, "status": "good", "total_requests": 14, "verification_method": "intelligent_range_probing"}, "ALDN": {"studio": "ALDN", "total_prefixes": 1, "valid_prefixes": ["aldn"], "invalid_prefixes": [], "prefix_details": {"aldn": {"prefix": "aldn", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 5.829889535903931, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "DGCEMD": {"studio": "DGCEMD", "total_prefixes": 1, "valid_prefixes": ["dgcemd"], "invalid_prefixes": [], "prefix_details": {"dgcemd": {"prefix": "dgcemd", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 6.038517951965332, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "CEMD": {"studio": "CEMD", "total_prefixes": 1, "valid_prefixes": ["cemd"], "invalid_prefixes": [], "prefix_details": {"cemd": {"prefix": "cemd", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 5.096728086471558, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "EKDV": {"studio": "EKDV", "total_prefixes": 2, "valid_prefixes": ["ekdv", "49ekdv"], "invalid_prefixes": [], "prefix_details": {"ekdv": {"prefix": "ekdv", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_50_100": {"start": 50, "end": 100, "tested": [50, 100], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_200_300": {"start": 200, "end": 300, "tested": [200, 300], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_500_600": {"start": 500, "end": 600, "tested": [500, 600], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_700_800": {"start": 700, "end": 800, "tested": [700, 800], "success_count": 1, "total_count": 2, "success_rate": 0.5}}, "successful_numbers": [700], "failed_numbers": [1, 10, 50, 100, 200, 300, 500, 600, 800], "estimated_range": [700, 700], "total_tests": 10, "success_count": 1, "is_valid": true, "confidence": 0.1, "probe_strategy": "intelligent_range_detection"}, "49ekdv": {"prefix": "49ekdv", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_50_100": {"start": 50, "end": 100, "tested": [50, 100], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [50, 100], "failed_numbers": [1, 10], "estimated_range": [50, 100], "total_tests": 4, "success_count": 2, "is_valid": true, "confidence": 0.5, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 23.15398645401001, "status": "excellent", "total_requests": 14, "verification_method": "intelligent_range_probing"}, "CRNX": {"studio": "CRNX", "total_prefixes": 1, "valid_prefixes": ["crnx"], "invalid_prefixes": [], "prefix_details": {"crnx": {"prefix": "crnx", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 64.37337756156921, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "MKON": {"studio": "MKON", "total_prefixes": 1, "valid_prefixes": ["mkon"], "invalid_prefixes": [], "prefix_details": {"mkon": {"prefix": "mkon", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 4.515902757644653, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "URKK": {"studio": "URKK", "total_prefixes": 1, "valid_prefixes": ["urkk"], "invalid_prefixes": [], "prefix_details": {"urkk": {"prefix": "urkk", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 4.459883213043213, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "MMUS": {"studio": "MMUS", "total_prefixes": 1, "valid_prefixes": ["mmus"], "invalid_prefixes": [], "prefix_details": {"mmus": {"prefix": "mmus", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 5.7667236328125, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "KNIP": {"studio": "KNIP", "total_prefixes": 1, "valid_prefixes": ["knip"], "invalid_prefixes": [], "prefix_details": {"knip": {"prefix": "knip", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 1, "total_count": 2, "success_rate": 0.5}}, "successful_numbers": [1], "failed_numbers": [10], "estimated_range": [1, 1], "total_tests": 2, "success_count": 1, "is_valid": true, "confidence": 0.5, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 4.251315116882324, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "PFAS": {"studio": "PFAS", "total_prefixes": 1, "valid_prefixes": ["pfas"], "invalid_prefixes": [], "prefix_details": {"pfas": {"prefix": "pfas", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 2, "total_count": 2, "success_rate": 1.0}}, "successful_numbers": [1, 10], "failed_numbers": [], "estimated_range": [1, 10], "total_tests": 2, "success_count": 2, "is_valid": true, "confidence": 1.0, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 1.0, "verification_time": 4.993603467941284, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "MGT": {"studio": "MGT", "total_prefixes": 2, "valid_prefixes": ["h_1711mgt"], "invalid_prefixes": ["118mgt"], "prefix_details": {"118mgt": {"prefix": "118mgt", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_50_100": {"start": 50, "end": 100, "tested": [50, 100], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_200_300": {"start": 200, "end": 300, "tested": [200, 300], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_500_600": {"start": 500, "end": 600, "tested": [500, 600], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_700_800": {"start": 700, "end": 800, "tested": [700, 800], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_900_999": {"start": 900, "end": 999, "tested": [900, 999], "success_count": 0, "total_count": 2, "success_rate": 0.0}}, "successful_numbers": [], "failed_numbers": [1, 10, 50, 100, 200, 300, 500, 600, 700, 800, 900, 999], "estimated_range": null, "total_tests": 12, "success_count": 0, "is_valid": false, "confidence": 0.0, "probe_strategy": "intelligent_range_detection"}, "h_1711mgt": {"prefix": "h_1711mgt", "probe_results": {"range_1_10": {"start": 1, "end": 10, "tested": [1, 10], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_50_100": {"start": 50, "end": 100, "tested": [50, 100], "success_count": 0, "total_count": 2, "success_rate": 0.0}, "range_200_300": {"start": 200, "end": 300, "tested": [200, 300], "success_count": 1, "total_count": 2, "success_rate": 0.5}}, "successful_numbers": [200], "failed_numbers": [1, 10, 50, 100, 300], "estimated_range": [200, 200], "total_tests": 6, "success_count": 1, "is_valid": true, "confidence": 0.16666666666666666, "probe_strategy": "intelligent_range_detection"}}, "success_rate": 0.5, "verification_time": 87.84535026550293, "status": "good", "total_requests": 18, "verification_method": "intelligent_range_probing"}}}