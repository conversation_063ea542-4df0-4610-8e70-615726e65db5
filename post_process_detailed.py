#!/usr/bin/env python3
"""
DMM数据详细后处理 - 获取每个作品的详细信息
"""
import sqlite3
import requests
import time
import re
from datetime import datetime
from typing import Dict, Optional
from bs4 import BeautifulSoup
import json

class DMMDetailedProcessor:
    """DMM详细信息处理器"""
    
    def __init__(self, db_file: str = "dmm_firefox_database.db"):
        self.db_file = db_file
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        # 处理年龄验证
        self.handle_age_verification()
    
    def handle_age_verification(self):
        """处理年龄验证"""
        try:
            age_check_url = "https://www.dmm.co.jp/age_check/=/declared=yes/?rurl=https%3A%2F%2Fwww.dmm.co.jp%2F"
            self.session.get(age_check_url, timeout=10)
        except:
            pass
    
    def add_detail_columns(self):
        """添加详细信息列到数据库"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            print("🔧 检查并添加详细信息列...")
            
            # 获取现有列
            cursor.execute("PRAGMA table_info(dmm_works)")
            existing_columns = [row[1] for row in cursor.fetchall()]
            
            # 需要添加的列
            new_columns = {
                'release_date': 'TEXT',      # 配信開始日
                'sale_date': 'TEXT',         # 商品発売日
                'duration': 'TEXT',          # 収録時間
                'actress': 'TEXT',           # 出演者
                'director': 'TEXT',          # 監督
                'series': 'TEXT',            # シリーズ
                'maker': 'TEXT',             # メーカー
                'label': 'TEXT',             # レーベル
                'genre': 'TEXT',             # ジャンル
                'rating': 'TEXT',            # 平均評価
                'detail_crawled': 'INTEGER DEFAULT 0',  # 是否已爬取详情
                'detail_crawl_time': 'TEXT'  # 详情爬取时间
            }
            
            # 添加缺少的列
            for column, column_type in new_columns.items():
                if column not in existing_columns:
                    print(f"   ➕ 添加列: {column}")
                    cursor.execute(f'ALTER TABLE dmm_works ADD COLUMN {column} {column_type}')
            
            conn.commit()
            conn.close()
            print("✅ 数据库结构更新完成")
            
        except Exception as e:
            print(f"❌ 数据库结构更新失败: {e}")
            raise
    
    def extract_detail_info(self, detail_url: str, cid: str) -> Optional[Dict]:
        """从详情页提取详细信息"""
        try:
            print(f"      🔍 访问详情页: {detail_url}")
            
            response = self.session.get(detail_url, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                detail_info = {}
                
                # 提取配信開始日
                release_date = self.extract_field(soup, ['配信開始日', '配信日'])
                if release_date:
                    detail_info['release_date'] = release_date
                
                # 提取商品発売日
                sale_date = self.extract_field(soup, ['商品発売日', '発売日'])
                if sale_date:
                    detail_info['sale_date'] = sale_date
                
                # 提取収録時間
                duration = self.extract_field(soup, ['収録時間', '時間'])
                if duration:
                    detail_info['duration'] = duration
                
                # 提取出演者
                actress = self.extract_field(soup, ['出演者'])
                if actress:
                    detail_info['actress'] = actress
                
                # 提取監督
                director = self.extract_field(soup, ['監督'])
                if director:
                    detail_info['director'] = director
                
                # 提取シリーズ
                series = self.extract_field(soup, ['シリーズ'])
                if series:
                    detail_info['series'] = series
                
                # 提取メーカー
                maker = self.extract_field(soup, ['メーカー'])
                if maker:
                    detail_info['maker'] = maker
                
                # 提取レーベル
                label = self.extract_field(soup, ['レーベル'])
                if label:
                    detail_info['label'] = label
                
                # 提取ジャンル
                genre = self.extract_genre(soup)
                if genre:
                    detail_info['genre'] = genre
                
                # 提取平均評価
                rating = self.extract_rating(soup)
                if rating:
                    detail_info['rating'] = rating
                
                # 添加爬取标记
                detail_info['detail_crawled'] = 1
                detail_info['detail_crawl_time'] = datetime.now().isoformat()
                
                print(f"      ✅ 提取到 {len(detail_info)} 个字段")
                return detail_info
                
            else:
                print(f"      ❌ 详情页访问失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"      ❌ 详情页解析异常: {e}")
            return None
    
    def extract_field(self, soup: BeautifulSoup, field_names: list) -> Optional[str]:
        """提取指定字段的值 - 基于调试结果的修复版"""
        try:
            # 方法1: 优先使用表格提取 - 基于调试结果，这是最可靠的方法
            for field_name in field_names:
                # 查找包含字段名的元素
                elements = soup.find_all(string=lambda text: text and field_name in text)
                for element in elements:
                    parent = element.parent
                    if parent and parent.name in ['th', 'td']:
                        # 查找同行的下一个单元格
                        next_cell = parent.find_next_sibling(['td', 'th'])
                        if next_cell:
                            value = next_cell.get_text(strip=True)
                            if value and value != '-' and len(value) < 200:  # 避免提取过长内容
                                print(f"         ✅ 表格提取 {field_name}: {value}")
                                return value

            # 方法2: 定义列表提取
            for field_name in field_names:
                elements = soup.find_all(string=lambda text: text and field_name in text)
                for element in elements:
                    parent = element.parent
                    if parent and parent.name == 'dt':
                        dd = parent.find_next_sibling('dd')
                        if dd:
                            value = dd.get_text(strip=True)
                            if value and value != '-' and len(value) < 200:
                                print(f"         ✅ 定义列表提取 {field_name}: {value}")
                                return value

            # 方法3: 冒号分隔提取
            for field_name in field_names:
                elements = soup.find_all(string=lambda text: text and field_name in text)
                for element in elements:
                    parent = element.parent
                    if parent:
                        parent_text = parent.get_text(strip=True)
                        if ':' in parent_text or '：' in parent_text:
                            parts = re.split(r'[:]|[：]', parent_text, 1)
                            if len(parts) > 1 and field_name in parts[0]:
                                value = parts[1].strip()
                                if value and value != '-' and len(value) < 200:
                                    print(f"         ✅ 冒号分隔提取 {field_name}: {value}")
                                    return value

            print(f"         ❌ 未找到字段: {field_names}")
            return None

        except Exception as e:
            print(f"      ❌ 提取字段异常: {e}")
            return None

    def extract_actress(self, soup: BeautifulSoup) -> Optional[str]:
        """提取出演者信息 - 基于调试结果的修复版"""
        try:
            # 方法1: 使用表格提取 - 基于调试结果，这是最可靠的方法
            elements = soup.find_all(string=lambda text: text and '出演者' in text)
            for element in elements:
                parent = element.parent
                if parent and parent.name in ['th', 'td']:
                    next_cell = parent.find_next_sibling(['td', 'th'])
                    if next_cell:
                        value = next_cell.get_text(strip=True)
                        if value and value != '-' and len(value) < 200:
                            print(f"         ✅ 表格提取出演者: {value}")
                            return value

            # 方法2: 查找出演者相关的链接
            actress_links = soup.find_all('a', href=re.compile(r'actress'))
            if actress_links:
                actresses = []
                for link in actress_links:
                    actress_name = link.get_text(strip=True)
                    if actress_name and len(actress_name) > 1:
                        actresses.append(actress_name)

                if actresses:
                    result = ', '.join(actresses)
                    print(f"         ✅ 链接提取出演者: {result}")
                    return result

            # 方法3: 使用通用提取方法
            actress_text = self.extract_field(soup, ['出演者', '女優名'])
            if actress_text:
                print(f"         ✅ 通用方法提取出演者: {actress_text}")
            return actress_text

        except Exception as ex:
            print(f"      ❌ 提取出演者异常: {ex}")
            return None

    def extract_genre(self, soup: BeautifulSoup) -> Optional[str]:
        """提取ジャンル信息 - 改进版"""
        try:
            # 方法1: 查找商品详情表格中的ジャンル
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        header = cells[0].get_text(strip=True)
                        if 'ジャンル' in header:
                            genre_cell = cells[1]
                            # 查找链接
                            genre_links = genre_cell.find_all('a')
                            if genre_links:
                                genres = []
                                for link in genre_links:
                                    genre_text = link.get_text(strip=True)
                                    if genre_text and len(genre_text) > 1:
                                        genres.append(genre_text)

                                if genres:
                                    return ', '.join(genres[:10])  # 最多10个类型

                            # 如果没有链接，直接获取文本
                            genre_text = genre_cell.get_text(strip=True)
                            if genre_text and genre_text != '-':
                                return genre_text

            # 方法2: 查找ジャンル相关的链接
            genre_links = soup.find_all('a', href=re.compile(r'genre'))
            if genre_links:
                genres = []
                for link in genre_links:
                    genre_text = link.get_text(strip=True)
                    if genre_text and len(genre_text) > 1:
                        genres.append(genre_text)

                if genres:
                    return ', '.join(genres[:10])  # 最多10个类型

            # 方法3: 使用通用提取方法
            genre_text = self.extract_field(soup, ['ジャンル'])
            return genre_text

        except Exception as ex:
            print(f"      ❌ 提取类型异常: {ex}")
            return None

    def extract_rating(self, soup: BeautifulSoup) -> Optional[str]:
        """提取平均評価信息 - 基于调试结果的修复版"""
        try:
            # 方法1: 使用调试中发现的模式 - 查找"X.XX点"格式
            page_text = soup.get_text()
            rating_match = re.search(r'(\d+\.?\d*)点', page_text)
            if rating_match:
                rating = rating_match.group(1)
                print(f"         ✅ 点数格式提取评分: {rating}")
                return rating

            # 方法2: 查找表格中的平均評価
            elements = soup.find_all(string=lambda text: text and '平均評価' in text)
            for element in elements:
                parent = element.parent
                if parent and parent.name in ['th', 'td']:
                    next_cell = parent.find_next_sibling(['td', 'th'])
                    if next_cell:
                        rating_text = next_cell.get_text(strip=True)
                        # 如果是"レビューを見る"，说明没有评分
                        if rating_text and rating_text != 'レビューを見る':
                            rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                            if rating_match:
                                rating = rating_match.group(1)
                                print(f"         ✅ 表格提取评分: {rating}")
                                return rating

            # 方法3: 其他评分模式
            rating_patterns = [
                r'平均評価(\d+\.?\d*)点',
                r'評価(\d+\.?\d*)点',
                r'★(\d+\.?\d*)',
                r'(\d+\.?\d*)星'
            ]

            for pattern in rating_patterns:
                match = re.search(pattern, page_text)
                if match:
                    rating = match.group(1)
                    print(f"         ✅ 模式提取评分: {rating}")
                    return rating

            print(f"         ❌ 未找到评分信息")
            return None

        except Exception as ex:
            print(f"      ❌ 提取评分异常: {ex}")
            return None
    
    def process_detailed_info(self, limit: int = None):
        """处理详细信息"""
        print("🔄 DMM详细信息后处理")
        print("=" * 50)
        
        # 添加详细信息列
        self.add_detail_columns()
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 获取需要处理的记录
            if limit:
                cursor.execute('''
                    SELECT id, cid, detail_url FROM dmm_works 
                    WHERE detail_crawled IS NULL OR detail_crawled = 0
                    ORDER BY id LIMIT ?
                ''', (limit,))
            else:
                cursor.execute('''
                    SELECT id, cid, detail_url FROM dmm_works 
                    WHERE detail_crawled IS NULL OR detail_crawled = 0
                    ORDER BY id
                ''')
            
            records = cursor.fetchall()
            
            if not records:
                print("📊 所有记录都已处理完成")
                return
            
            print(f"📊 找到 {len(records)} 条需要处理的记录")
            
            success_count = 0
            failed_count = 0
            
            for i, (record_id, cid, detail_url) in enumerate(records, 1):
                print(f"\n📄 处理第 {i}/{len(records)} 个作品")
                print(f"   CID: {cid}")
                
                # 提取详细信息
                detail_info = self.extract_detail_info(detail_url, cid)
                
                if detail_info:
                    # 更新数据库
                    update_fields = []
                    update_values = []
                    
                    for field, value in detail_info.items():
                        update_fields.append(f"{field} = ?")
                        update_values.append(value)
                    
                    update_values.append(record_id)
                    
                    update_sql = f'''
                        UPDATE dmm_works 
                        SET {', '.join(update_fields)}
                        WHERE id = ?
                    '''
                    
                    cursor.execute(update_sql, update_values)
                    conn.commit()
                    
                    success_count += 1
                    print(f"   ✅ 详细信息更新成功")
                    
                else:
                    # 标记为已尝试但失败
                    cursor.execute('''
                        UPDATE dmm_works 
                        SET detail_crawled = -1, detail_crawl_time = ?
                        WHERE id = ?
                    ''', (datetime.now().isoformat(), record_id))
                    conn.commit()
                    
                    failed_count += 1
                    print(f"   ❌ 详细信息提取失败")
                
                # 进度报告
                if i % 10 == 0:
                    print(f"\n📊 进度报告: {i}/{len(records)} 完成")
                    print(f"   成功: {success_count}, 失败: {failed_count}")
                
                # 请求间隔
                if i < len(records):
                    print(f"   😴 等待5秒...")
                    time.sleep(5)
            
            conn.close()
            
            # 最终统计
            print(f"\n🎉 详细信息处理完成!")
            print(f"📊 最终统计:")
            print(f"   成功处理: {success_count}")
            print(f"   处理失败: {failed_count}")
            print(f"   总计处理: {len(records)}")
            
        except Exception as e:
            print(f"❌ 详细信息处理失败: {e}")

def main():
    """主函数"""
    print("🔄 DMM详细信息后处理")
    print("=" * 50)
    
    print("📋 说明:")
    print("   - 为每个作品获取详细信息")
    print("   - 包括发布日期、时长、演员、导演等")
    print("   - 请求间隔5秒，避免被限制")
    
    print("\n📋 处理选项:")
    print("   1. 处理前10个作品 (测试)")
    print("   2. 处理前50个作品")
    print("   3. 处理所有作品")
    
    choice = input("\n请选择处理方式 (1-3): ").strip()
    
    processor = DMMDetailedProcessor()
    
    if choice == '1':
        print("🧪 开始处理前10个作品...")
        processor.process_detailed_info(10)
        
    elif choice == '2':
        print("🚀 开始处理前50个作品...")
        processor.process_detailed_info(50)
        
    elif choice == '3':
        print("🚀 开始处理所有作品...")
        print("⚠️  这可能需要较长时间")
        confirm = input("确认继续？(y/N): ").strip().lower()
        if confirm == 'y':
            processor.process_detailed_info()
        else:
            print("处理已取消")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
