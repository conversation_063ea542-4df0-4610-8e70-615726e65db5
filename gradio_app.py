# ====== 模块化配置 ======
USE_MODULAR_SEARCH = True  # 控制是否使用模块化搜索详情页

import os
import asyncio
import dmm_tools
from PIL import Image, ImageFilter
import io
import requests
import streamlit as st
import pandas as pd
import json
import random
import base64
import re
import shutil
import time
from datetime import datetime

import hashlib
import uuid
from urllib.parse import urlencode, parse_qs
import locale

# 设置Streamlit中文化
try:
    locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
    except:
        pass

# 导入中文化模块
try:
    from streamlit_chinese import inject_chinese_ui, set_chinese_config
    set_chinese_config()
except ImportError:
    def inject_chinese_ui():
        pass

BG_UPLOAD_DIR = "./bg_upload"
os.makedirs(BG_UPLOAD_DIR, exist_ok=True)

def extract_dir_name(code):
    """
    例：CAWD-849 (cawd00849)-C -> CAWD-849 (cawd00849)
    例：IPX-123 -> IPX-123
    """
    m = re.match(r"^([A-Z0-9]+-\d+)\s*\(([^)]+)\)", code, re.I)
    if m:
        return f"{m.group(1)} ({m.group(2)})"
    m2 = re.match(r"^([A-Z0-9]+-\d+)", code, re.I)
    return m2.group(1) if m2 else code

# 动态设置页面标题（根据激活状态）
def get_dynamic_page_title():
    """根据激活状态获取动态页面标题"""
    try:
        is_activated, _ = check_activation_status()
        if is_activated:
            return "MMA Pro - 多功能媒体管理系统"
        else:
            return "MMA - 媒体管理系统"
    except:
        return "MMA - 媒体管理系统"

st.set_page_config(page_title=get_dynamic_page_title(), layout="wide")

# 动态更新浏览器标题的JavaScript
def update_browser_title():
    """动态更新浏览器标题"""
    try:
        is_activated, activation_info = get_current_activation_status()
        if is_activated:
            activation_type = activation_info.get("type", "").upper()
            if activation_type == "TRIAL":
                title = "MMA Pro (试用版) - 多功能媒体管理系统"
            else:
                title = "MMA Pro - 多功能媒体管理系统"
        else:
            title = "MMA - 媒体管理系统"

        # 使用JavaScript更新页面标题
        st.markdown(f"""
        <script>
        document.title = "{title}";
        </script>
        """, unsafe_allow_html=True)
    except:
        pass

# ====== 激活验证系统 ======
def check_activation_status():
    """检查软件激活状态"""
    ACTIVATION_FILE = "activation_config.json"

    try:
        if os.path.exists(ACTIVATION_FILE):
            with open(ACTIVATION_FILE, 'r', encoding='utf-8') as f:
                status = json.load(f)

            if status.get("activated", False):
                # 检查是否过期
                try:
                    from datetime import datetime, timedelta

                    # 处理试用版到期时间计算
                    if status.get("type") == "trial" and "activated_date" in status:
                        activated_date = datetime.strptime(status["activated_date"], "%Y-%m-%d %H:%M:%S")
                        trial_days = status.get("trial_days", 7)
                        expire_date = activated_date + timedelta(days=trial_days)

                        if datetime.now() <= expire_date:
                            # 更新状态中的到期时间显示
                            status["expires"] = expire_date.strftime("%Y-%m-%d")
                            status["expires_datetime"] = expire_date.strftime("%Y-%m-%d %H:%M:%S")
                            return True, status
                        else:
                            return False, {"error": "试用期已过期"}
                    else:
                        # 固定到期时间的激活码
                        expire_date = datetime.strptime(status.get('expires', ''), "%Y-%m-%d")
                        if datetime.now() <= expire_date:
                            return True, status
                        else:
                            return False, {"error": "激活已过期"}
                except Exception as e:
                    return False, {"error": f"激活信息错误: {str(e)}"}
    except Exception:
        pass

    return False, {"error": "未激活"}

def is_feature_enabled(feature_name):
    """检查特定功能是否启用"""
    is_activated, status = get_current_activation_status()

    if not is_activated:
        return False

    features = status.get("features", [])
    if "all" in features:
        return True

    # 功能映射
    feature_mapping = {
        "strm": ["strm", "monitor", "all"],
        "monitor": ["strm", "monitor", "all"],
        "upload": ["upload", "all"],
        "download": ["download", "all"],
        "advanced": ["advanced", "all"],
        "nfo": ["nfo", "all"]
    }

    allowed_features = feature_mapping.get(feature_name, [feature_name])
    return any(f in features for f in allowed_features)

# 全局激活状态检查函数
def get_current_activation_status():
    """获取当前激活状态（实时检查）"""
    # 优先从session_state获取，如果不存在则从文件加载
    if 'activation_status' in st.session_state:
        status = st.session_state.activation_status
        if status.get("activated", False):
            # 检查是否过期
            try:
                from datetime import datetime, timedelta
                if status.get("type") == "trial" and "activated_date" in status:
                    activated_date = datetime.strptime(status["activated_date"], "%Y-%m-%d %H:%M:%S")
                    trial_days = status.get("trial_days", 7)
                    expire_date = activated_date + timedelta(days=trial_days)

                    if datetime.now() <= expire_date:
                        return True, status
                    else:
                        return False, {"error": "试用期已过期"}
                else:
                    return True, status
            except Exception as e:
                # 如果检查失败，返回原始状态（避免因为日期解析问题导致激活失效）
                return True, status
        else:
            return False, {"error": "未激活"}
    else:
        # 从文件加载激活状态
        return check_activation_status()

# ====== 自动随机背景图片并注入CSS（放在st.set_page_config后，全局CSS前）======
thumb_dir = "/vol1/1000/媒体库/115/R+18/JAV/"
thumb_files = []
thumb_path = None
b64_img = None

if os.path.exists(thumb_dir):
    thumb_files = [f for f in os.listdir(thumb_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    if thumb_files:
        thumb_path = os.path.join(thumb_dir, random.choice(thumb_files))
        try:
            img = Image.open(thumb_path).convert("RGB")
            blur_img = img.filter(ImageFilter.GaussianBlur(radius=18))
            buf = io.BytesIO()
            blur_img.save(buf, format="JPEG")
            b64_img = base64.b64encode(buf.getvalue()).decode()
            st.markdown(f"""
            <style>
            body {{
                background: url("data:image/jpeg;base64,{b64_img}") no-repeat center center fixed !important;
                background-size: cover !important;
            }}
            [data-theme="dark"] body::before {{
                content: "";
                position: fixed;
                left: 0; top: 0; width: 100vw; height: 100vh;
                background: rgba(24,28,36,0.65);
                z-index: 0;
                pointer-events: none;
            }}
            .stApp {{
                background: transparent !important;
            }}
            </style>
            """, unsafe_allow_html=True)
        except Exception as e:
            b64_img = None
            thumb_path = None

# ====== 全局美化CSS（建议放在这里）======
st.markdown("""
<style>
/* --------- 隐藏标题悬停链接符号 - 终极解决方案 --------- */
/* 隐藏所有可能的标题链接元素 */
.stMarkdown h1 .stHeaderActionElements,
.stMarkdown h2 .stHeaderActionElements,
.stMarkdown h3 .stHeaderActionElements,
.stMarkdown h4 .stHeaderActionElements,
.stMarkdown h5 .stHeaderActionElements,
.stMarkdown h6 .stHeaderActionElements,
.stMarkdown h1:hover .stHeaderActionElements,
.stMarkdown h2:hover .stHeaderActionElements,
.stMarkdown h3:hover .stHeaderActionElements,
.stMarkdown h4:hover .stHeaderActionElements,
.stMarkdown h5:hover .stHeaderActionElements,
.stMarkdown h6:hover .stHeaderActionElements,
/* 使用属性选择器匹配所有可能的类名 */
.stMarkdown h1 [class*="HeaderAction"],
.stMarkdown h2 [class*="HeaderAction"],
.stMarkdown h3 [class*="HeaderAction"],
.stMarkdown h4 [class*="HeaderAction"],
.stMarkdown h5 [class*="HeaderAction"],
.stMarkdown h6 [class*="HeaderAction"],
/* 匹配data-testid属性 */
.stMarkdown h1 [data-testid*="HeaderAction"],
.stMarkdown h2 [data-testid*="HeaderAction"],
.stMarkdown h3 [data-testid*="HeaderAction"],
.stMarkdown h4 [data-testid*="HeaderAction"],
.stMarkdown h5 [data-testid*="HeaderAction"],
.stMarkdown h6 [data-testid*="HeaderAction"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

/* 完全移除标题的链接功能 */
.stMarkdown h1 a,
.stMarkdown h2 a,
.stMarkdown h3 a,
.stMarkdown h4 a,
.stMarkdown h5 a,
.stMarkdown h6 a {
    text-decoration: none !important;
    color: inherit !important;
    pointer-events: none !important;
}

/* 隐藏所有可能的标题链接元素 */
.stMarkdown [data-testid="stHeaderActionElements"],
.stMarkdown .stHeaderActionElements,
h1 [data-testid="stHeaderActionElements"],
h2 [data-testid="stHeaderActionElements"],
h3 [data-testid="stHeaderActionElements"],
h4 [data-testid="stHeaderActionElements"],
h5 [data-testid="stHeaderActionElements"],
h6 [data-testid="stHeaderActionElements"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* 隐藏标题中的链接图标 */
.stMarkdown h1 .stHeaderActionElements svg,
.stMarkdown h2 .stHeaderActionElements svg,
.stMarkdown h3 .stHeaderActionElements svg,
.stMarkdown h4 .stHeaderActionElements svg,
.stMarkdown h5 .stHeaderActionElements svg,
.stMarkdown h6 .stHeaderActionElements svg {
    display: none !important;
}

/* 精确隐藏标题链接元素，但保留其他重要内容 */
.stMarkdown [class*="stHeader"] [class*="ActionElements"]:not([class*="badge"]):not([class*="status"]):not([class*="pro"]),
.stMarkdown [class*="ActionElements"]:not([class*="badge"]):not([class*="status"]):not([class*="pro"]) {
    display: none !important;
}

/* 保留包含重要信息的元素 */
.stMarkdown h1 span[style*="background"],
.stMarkdown h2 span[style*="background"],
.stMarkdown h3 span[style*="background"],
.stMarkdown h4 span[style*="background"],
.stMarkdown h5 span[style*="background"],
.stMarkdown h6 span[style*="background"] {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 隐藏标题旁边的任何按钮或链接 */
.stMarkdown h1 ~ .stButton,
.stMarkdown h2 ~ .stButton,
.stMarkdown h3 ~ .stButton,
.stMarkdown h4 ~ .stButton,
.stMarkdown h5 ~ .stButton,
.stMarkdown h6 ~ .stButton {
    display: none !important;
}

/* 最强力的规则：隐藏所有可能的标题链接符号 */
.stMarkdown h1::after,
.stMarkdown h2::after,
.stMarkdown h3::after,
.stMarkdown h4::after,
.stMarkdown h5::after,
.stMarkdown h6::after {
    display: none !important;
}

/* 隐藏标题悬停时的链接符号，但保留其他内容 */
.stMarkdown h1:hover::after,
.stMarkdown h2:hover::after,
.stMarkdown h3:hover::after,
.stMarkdown h4:hover::after,
.stMarkdown h5:hover::after,
.stMarkdown h6:hover::after {
    display: none !important;
}

/* 只隐藏特定的链接相关元素，保留其他内容 */
.stMarkdown h1:hover > [class*="HeaderAction"],
.stMarkdown h2:hover > [class*="HeaderAction"],
.stMarkdown h3:hover > [class*="HeaderAction"],
.stMarkdown h4:hover > [class*="HeaderAction"],
.stMarkdown h5:hover > [class*="HeaderAction"],
.stMarkdown h6:hover > [class*="HeaderAction"],
.stMarkdown h1:hover > [data-testid*="HeaderAction"],
.stMarkdown h2:hover > [data-testid*="HeaderAction"],
.stMarkdown h3:hover > [data-testid*="HeaderAction"],
.stMarkdown h4:hover > [data-testid*="HeaderAction"],
.stMarkdown h5:hover > [data-testid*="HeaderAction"],
.stMarkdown h6:hover > [data-testid*="HeaderAction"] {
    display: none !important;
}

/* 确保标题文本本身仍然可见 */
.stMarkdown h1:hover,
.stMarkdown h2:hover,
.stMarkdown h3:hover,
.stMarkdown h4:hover,
.stMarkdown h5:hover,
.stMarkdown h6:hover {
    display: block !important;
}

/* 强制显示Pro激活状态和其他重要标识 */
.stMarkdown h1:hover span[style*="linear-gradient"],
.stMarkdown h2:hover span[style*="linear-gradient"],
.stMarkdown h3:hover span[style*="linear-gradient"],
.stMarkdown h4:hover span[style*="linear-gradient"],
.stMarkdown h5:hover span[style*="linear-gradient"],
.stMarkdown h6:hover span[style*="linear-gradient"],
.stMarkdown h1:hover span[style*="background"],
.stMarkdown h2:hover span[style*="background"],
.stMarkdown h3:hover span[style*="background"],
.stMarkdown h4:hover span[style*="background"],
.stMarkdown h5:hover span[style*="background"],
.stMarkdown h6:hover span[style*="background"] {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 999 !important;
}

/* 特别保护Pro激活状态标识 */
.stMarkdown h1 span[style*="已激活"],
.stMarkdown h2 span[style*="已激活"],
.stMarkdown h3 span[style*="已激活"],
.stMarkdown h4 span[style*="已激活"],
.stMarkdown h5 span[style*="已激活"],
.stMarkdown h6 span[style*="已激活"],
.stMarkdown h1:hover span[style*="已激活"],
.stMarkdown h2:hover span[style*="已激活"],
.stMarkdown h3:hover span[style*="已激活"],
.stMarkdown h4:hover span[style*="已激活"],
.stMarkdown h5:hover span[style*="已激活"],
.stMarkdown h6:hover span[style*="已激活"] {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1000 !important;
}

/* 精确隐藏标题内的链接相关元素，但保留其他内容 */
.stMarkdown h1 [class*="HeaderAction"],
.stMarkdown h2 [class*="HeaderAction"],
.stMarkdown h3 [class*="HeaderAction"],
.stMarkdown h4 [class*="HeaderAction"],
.stMarkdown h5 [class*="HeaderAction"],
.stMarkdown h6 [class*="HeaderAction"],
.stMarkdown h1 [data-testid*="HeaderAction"],
.stMarkdown h2 [data-testid*="HeaderAction"],
.stMarkdown h3 [data-testid*="HeaderAction"],
.stMarkdown h4 [data-testid*="HeaderAction"],
.stMarkdown h5 [data-testid*="HeaderAction"],
.stMarkdown h6 [data-testid*="HeaderAction"],
.stMarkdown h1 [class*="stHeaderAction"],
.stMarkdown h2 [class*="stHeaderAction"],
.stMarkdown h3 [class*="stHeaderAction"],
.stMarkdown h4 [class*="stHeaderAction"],
.stMarkdown h5 [class*="stHeaderAction"],
.stMarkdown h6 [class*="stHeaderAction"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

/* --------- 浅色主题 --------- */
.stTabs [data-baseweb="tab-list"] { gap: 16px; }
.stTabs [data-baseweb="tab"] {
    font-size: 18px; font-weight: bold; color: #ff512f;
    padding: 8px 24px 8px 24px; border-radius: 8px 8px 0 0;
    background: #f7f7f7; margin-bottom: -2px;
}
.stTabs [aria-selected="true"] {
    background: linear-gradient(90deg, #ff512f 0%, #dd2476 100%);
    color: #fff !important;
}
.stButton>button {
    background: linear-gradient(135deg, #ff512f 0%, #dd2476 100%);
    color: white; border-radius: 8px; font-weight: 600;
    margin: 2px 0px 2px 0px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow:
        0 3px 6px rgba(255, 81, 47, 0.3),
        0 1px 3px rgba(221, 36, 118, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}
.stButton>button:hover {
    background: linear-gradient(135deg, #ff6b35 0%, #ff512f 50%, #dd2476 100%);
    transform: translateY(-2px);
    box-shadow:
        0 6px 16px rgba(255, 81, 47, 0.4),
        0 3px 8px rgba(221, 36, 118, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.15);
}
.stButton>button:active {
    transform: translateY(0px) scale(0.98);
    transition: all 0.1s ease;
    box-shadow:
        0 2px 4px rgba(255, 81, 47, 0.3),
        0 1px 2px rgba(221, 36, 118, 0.2),
        inset 0 2px 4px rgba(0, 0, 0, 0.2);
}
/* 按钮光泽效果 */
.stButton>button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}
.stButton>button:hover::before {
    left: 100%;
}
.stTextInput>div>div>input,
.stSelectbox>div>div>div>div {
    background: #fff; color: #222; border-radius: 6px;
}
.stMarkdown h3, .stMarkdown h4, .stMarkdown h5 { color: #ff512f; }
.stExpanderHeader { color: #ff512f !important; }
.stInfo { background: #f7f7f7 !important; color: #ff512f !important; }
.stProgress > div > div > div > div {
    background: linear-gradient(90deg, #ff512f 0%, #dd2476 100%);
}

/* --------- 暗色主题 --------- */
[data-theme="dark"] .stApp, [data-theme="dark"] body {
    background: #181c24 !important; color: #fff !important;
}

/* 暗色主题下隐藏标题链接符号 - 终极解决方案 */
[data-theme="dark"] .stMarkdown h1 .stHeaderActionElements,
[data-theme="dark"] .stMarkdown h2 .stHeaderActionElements,
[data-theme="dark"] .stMarkdown h3 .stHeaderActionElements,
[data-theme="dark"] .stMarkdown h4 .stHeaderActionElements,
[data-theme="dark"] .stMarkdown h5 .stHeaderActionElements,
[data-theme="dark"] .stMarkdown h6 .stHeaderActionElements,
[data-theme="dark"] .stMarkdown h1:hover .stHeaderActionElements,
[data-theme="dark"] .stMarkdown h2:hover .stHeaderActionElements,
[data-theme="dark"] .stMarkdown h3:hover .stHeaderActionElements,
[data-theme="dark"] .stMarkdown h4:hover .stHeaderActionElements,
[data-theme="dark"] .stMarkdown h5:hover .stHeaderActionElements,
[data-theme="dark"] .stMarkdown h6:hover .stHeaderActionElements,
/* 暗色主题下使用属性选择器匹配所有可能的类名 */
[data-theme="dark"] .stMarkdown h1 [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h2 [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h3 [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h4 [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h5 [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h6 [class*="HeaderAction"],
/* 暗色主题下匹配data-testid属性 */
[data-theme="dark"] .stMarkdown h1 [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h2 [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h3 [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h4 [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h5 [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h6 [data-testid*="HeaderAction"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

[data-theme="dark"] .stMarkdown h1 a,
[data-theme="dark"] .stMarkdown h2 a,
[data-theme="dark"] .stMarkdown h3 a,
[data-theme="dark"] .stMarkdown h4 a,
[data-theme="dark"] .stMarkdown h5 a,
[data-theme="dark"] .stMarkdown h6 a {
    text-decoration: none !important;
    color: inherit !important;
    pointer-events: none !important;
}

/* 暗色主题下隐藏所有可能的标题链接元素 */
[data-theme="dark"] .stMarkdown [data-testid="stHeaderActionElements"],
[data-theme="dark"] .stMarkdown .stHeaderActionElements,
[data-theme="dark"] h1 [data-testid="stHeaderActionElements"],
[data-theme="dark"] h2 [data-testid="stHeaderActionElements"],
[data-theme="dark"] h3 [data-testid="stHeaderActionElements"],
[data-theme="dark"] h4 [data-testid="stHeaderActionElements"],
[data-theme="dark"] h5 [data-testid="stHeaderActionElements"],
[data-theme="dark"] h6 [data-testid="stHeaderActionElements"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* 暗色主题下隐藏标题中的链接图标 */
[data-theme="dark"] .stMarkdown h1 .stHeaderActionElements svg,
[data-theme="dark"] .stMarkdown h2 .stHeaderActionElements svg,
[data-theme="dark"] .stMarkdown h3 .stHeaderActionElements svg,
[data-theme="dark"] .stMarkdown h4 .stHeaderActionElements svg,
[data-theme="dark"] .stMarkdown h5 .stHeaderActionElements svg,
[data-theme="dark"] .stMarkdown h6 .stHeaderActionElements svg {
    display: none !important;
}

/* 暗色主题下精确隐藏标题链接元素，但保留其他重要内容 */
[data-theme="dark"] .stMarkdown [class*="stHeader"] [class*="ActionElements"]:not([class*="badge"]):not([class*="status"]):not([class*="pro"]),
[data-theme="dark"] .stMarkdown [class*="ActionElements"]:not([class*="badge"]):not([class*="status"]):not([class*="pro"]) {
    display: none !important;
}

/* 暗色主题下保留包含重要信息的元素 */
[data-theme="dark"] .stMarkdown h1 span[style*="background"],
[data-theme="dark"] .stMarkdown h2 span[style*="background"],
[data-theme="dark"] .stMarkdown h3 span[style*="background"],
[data-theme="dark"] .stMarkdown h4 span[style*="background"],
[data-theme="dark"] .stMarkdown h5 span[style*="background"],
[data-theme="dark"] .stMarkdown h6 span[style*="background"] {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 暗色主题下隐藏标题旁边的任何按钮或链接 */
[data-theme="dark"] .stMarkdown h1 ~ .stButton,
[data-theme="dark"] .stMarkdown h2 ~ .stButton,
[data-theme="dark"] .stMarkdown h3 ~ .stButton,
[data-theme="dark"] .stMarkdown h4 ~ .stButton,
[data-theme="dark"] .stMarkdown h5 ~ .stButton,
[data-theme="dark"] .stMarkdown h6 ~ .stButton {
    display: none !important;
}

/* 暗色主题下的最强力规则：隐藏所有可能的标题链接符号 */
[data-theme="dark"] .stMarkdown h1::after,
[data-theme="dark"] .stMarkdown h2::after,
[data-theme="dark"] .stMarkdown h3::after,
[data-theme="dark"] .stMarkdown h4::after,
[data-theme="dark"] .stMarkdown h5::after,
[data-theme="dark"] .stMarkdown h6::after {
    display: none !important;
}

/* 暗色主题下隐藏标题悬停时的链接符号，但保留其他内容 */
[data-theme="dark"] .stMarkdown h1:hover::after,
[data-theme="dark"] .stMarkdown h2:hover::after,
[data-theme="dark"] .stMarkdown h3:hover::after,
[data-theme="dark"] .stMarkdown h4:hover::after,
[data-theme="dark"] .stMarkdown h5:hover::after,
[data-theme="dark"] .stMarkdown h6:hover::after {
    display: none !important;
}

/* 暗色主题下只隐藏特定的链接相关元素，保留其他内容 */
[data-theme="dark"] .stMarkdown h1:hover > [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h2:hover > [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h3:hover > [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h4:hover > [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h5:hover > [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h6:hover > [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h1:hover > [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h2:hover > [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h3:hover > [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h4:hover > [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h5:hover > [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h6:hover > [data-testid*="HeaderAction"] {
    display: none !important;
}

/* 暗色主题下确保标题文本本身仍然可见 */
[data-theme="dark"] .stMarkdown h1:hover,
[data-theme="dark"] .stMarkdown h2:hover,
[data-theme="dark"] .stMarkdown h3:hover,
[data-theme="dark"] .stMarkdown h4:hover,
[data-theme="dark"] .stMarkdown h5:hover,
[data-theme="dark"] .stMarkdown h6:hover {
    display: block !important;
}

/* 暗色主题下强制显示Pro激活状态和其他重要标识 */
[data-theme="dark"] .stMarkdown h1:hover span[style*="linear-gradient"],
[data-theme="dark"] .stMarkdown h2:hover span[style*="linear-gradient"],
[data-theme="dark"] .stMarkdown h3:hover span[style*="linear-gradient"],
[data-theme="dark"] .stMarkdown h4:hover span[style*="linear-gradient"],
[data-theme="dark"] .stMarkdown h5:hover span[style*="linear-gradient"],
[data-theme="dark"] .stMarkdown h6:hover span[style*="linear-gradient"],
[data-theme="dark"] .stMarkdown h1:hover span[style*="background"],
[data-theme="dark"] .stMarkdown h2:hover span[style*="background"],
[data-theme="dark"] .stMarkdown h3:hover span[style*="background"],
[data-theme="dark"] .stMarkdown h4:hover span[style*="background"],
[data-theme="dark"] .stMarkdown h5:hover span[style*="background"],
[data-theme="dark"] .stMarkdown h6:hover span[style*="background"] {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 999 !important;
}

/* 暗色主题下特别保护Pro激活状态标识 */
[data-theme="dark"] .stMarkdown h1 span[style*="已激活"],
[data-theme="dark"] .stMarkdown h2 span[style*="已激活"],
[data-theme="dark"] .stMarkdown h3 span[style*="已激活"],
[data-theme="dark"] .stMarkdown h4 span[style*="已激活"],
[data-theme="dark"] .stMarkdown h5 span[style*="已激活"],
[data-theme="dark"] .stMarkdown h6 span[style*="已激活"],
[data-theme="dark"] .stMarkdown h1:hover span[style*="已激活"],
[data-theme="dark"] .stMarkdown h2:hover span[style*="已激活"],
[data-theme="dark"] .stMarkdown h3:hover span[style*="已激活"],
[data-theme="dark"] .stMarkdown h4:hover span[style*="已激活"],
[data-theme="dark"] .stMarkdown h5:hover span[style*="已激活"],
[data-theme="dark"] .stMarkdown h6:hover span[style*="已激活"] {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1000 !important;
}

/* 暗色主题下精确隐藏标题内的链接相关元素，但保留其他内容 */
[data-theme="dark"] .stMarkdown h1 [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h2 [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h3 [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h4 [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h5 [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h6 [class*="HeaderAction"],
[data-theme="dark"] .stMarkdown h1 [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h2 [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h3 [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h4 [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h5 [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h6 [data-testid*="HeaderAction"],
[data-theme="dark"] .stMarkdown h1 [class*="stHeaderAction"],
[data-theme="dark"] .stMarkdown h2 [class*="stHeaderAction"],
[data-theme="dark"] .stMarkdown h3 [class*="stHeaderAction"],
[data-theme="dark"] .stMarkdown h4 [class*="stHeaderAction"],
[data-theme="dark"] .stMarkdown h5 [class*="stHeaderAction"],
[data-theme="dark"] .stMarkdown h6 [class*="stHeaderAction"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

[data-theme="dark"] .stTabs [data-baseweb="tab"] {
    background: #23272f !important; color: #ff512f !important;
}
[data-theme="dark"] .stTabs [aria-selected="true"] {
    background: linear-gradient(90deg, #ff512f 0%, #dd2476 100%) !important;
    color: #fff !important;
}
[data-theme="dark"] .stButton>button {
    background: linear-gradient(135deg, #ff512f 0%, #dd2476 100%) !important;
    color: #fff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    box-shadow:
        0 3px 6px rgba(255, 81, 47, 0.4),
        0 1px 3px rgba(221, 36, 118, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2) !important;
}
[data-theme="dark"] .stButton>button:hover {
    background: linear-gradient(135deg, #ff6b35 0%, #ff512f 50%, #dd2476 100%) !important;
    color: #fff !important;
    transform: translateY(-2px) !important;
    box-shadow:
        0 6px 16px rgba(255, 81, 47, 0.5),
        0 3px 8px rgba(221, 36, 118, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.25) !important;
}
[data-theme="dark"] .stButton>button:active {
    transform: translateY(0px) scale(0.98) !important;
    box-shadow:
        0 2px 4px rgba(255, 81, 47, 0.4),
        0 1px 2px rgba(221, 36, 118, 0.3),
        inset 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}
[data-theme="dark"] .stTextInput>div>div>input,
[data-theme="dark"] .stSelectbox>div>div>div>div {
    background: #23272f !important; color: #fff !important; border-radius: 6px;
}
[data-theme="dark"] .stMarkdown h3, 
[data-theme="dark"] .stMarkdown h4, 
[data-theme="dark"] .stMarkdown h5 { color: #ff512f !important; }
[data-theme="dark"] .stExpanderHeader { color: #ff512f !important; }
[data-theme="dark"] .stInfo { background: #23272f !important; color: #ff512f !important; }
[data-theme="dark"] .stProgress > div > div > div > div {
    background: linear-gradient(90deg, #ff512f 0%, #dd2476 100%) !important;
}

/* 侧边栏底部按钮适配 */
.sider-bottom-wrap {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 17rem;
    padding-bottom: 24px;
    background: transparent;
    z-index: 100;
}
.sider-bottom-btn {
    display: flex;
    align-items: center;
    width: 92%;
    margin: 0 0 8px 8px;
    padding: 8px 0 8px 8px;
    border: none;
    border-radius: 6px;
    background: none;
    color: #444;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    text-align: left;
    outline: none;
}
.sider-bottom-btn:hover {
    background: #f5f5f5;
    color: #ff512f;
}
.sider-bottom-btn .icon {
    margin-right: 10px;
    font-size: 18px;
    width: 22px;
    text-align: center;
    opacity: 0.85;
}
.sider-bottom-divider {
    border-top: 1px solid #eee;
    margin: 18px 0 12px 0;
    width: 90%;
    margin-left: 8px;
}
/* 适配官方暗色主题 */
[data-theme="dark"] .sider-bottom-btn {
    color: #eee;
}
[data-theme="dark"] .sider-bottom-btn:hover {
    background: #333;
    color: #ff512f;
}
</style>
""", unsafe_allow_html=True)
# ====== 全局美化CSS结束 ======

# 动态侧边栏logo和标题 - 根据激活状态显示
def render_sidebar_title():
    """渲染动态侧边栏标题"""
    try:
        is_activated, activation_info = get_current_activation_status()
        if is_activated:
            # 激活后显示 MMA Pro
            title = "MMA Pro"
            title_color = "#ff512f"
            badge_html = '<span style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 2px 6px; border-radius: 8px; font-size: 10px; margin-left: 5px;">✨</span>'
        else:
            # 未激活显示 MMA
            title = "MMA"
            title_color = "#999"
            badge_html = '<span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 8px; font-size: 10px; margin-left: 5px;">🔒</span>'
    except:
        title = "MMA"
        title_color = "#999"
        badge_html = ""

    st.sidebar.markdown(f"""
    <div style="text-align: center; margin-bottom: 0px; margin-top: -30px;">
        <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b9/Marvel_Logo.svg/960px-Marvel_Logo.svg.png?20161025051221"
             width="150" style="margin-bottom: -1px;">
        <h4 style="color: {title_color}; margin: 0; font-size: 22px; font-weight: bold; line-height: 1.0; transition: all 0.3s ease;">{title}{badge_html}</h4>
    </div>
    """, unsafe_allow_html=True)

render_sidebar_title()

# 主功能分组（已将“统计设置”移入主功能）
main_funcs = [
    "首页", "搜索详情页", "批量重命名", "高清海报替换", "文件上传", "整理文件结构", "NFO元数据修改", "STRM整理", "统计设置"
]
main_func_icons = {
    "首页": "🏠", "搜索详情页": "🔍", "批量重命名": "📝", "高清海报替换": "🖼️",
    "文件上传": "📤", "整理文件结构": "🗂️", "NFO元数据修改": "🛠️", "STRM整理": "🎬", "统计设置": "📊"
}
main_func_labels = [f"{main_func_icons.get(f, '')} {f}" for f in main_funcs]

# 系统分组（添加激活管理）
system_funcs = ["日志显示", "系统设置", "实时监控", "激活管理"]
system_func_icons = {"日志显示": "📜", "系统设置": "⚙️", "实时监控": "📡", "激活管理": "🔐"}
system_func_labels = [f"{system_func_icons.get(f, '')} {f}" for f in system_funcs]

# 初始化 active_tab
if "active_tab" not in st.session_state:
    st.session_state.active_tab = main_funcs[0]

# 初始化激活状态
if "activation_status" not in st.session_state:
    # 从文件加载激活状态
    is_activated, status = check_activation_status()
    if is_activated:
        st.session_state.activation_status = status
    else:
        st.session_state.activation_status = {"activated": False, "code": "", "type": "", "expires": "", "activated_date": ""}

# 方案2：强制容器隔离 + 延迟渲染
def clear_ui_state():
    """彻底清理状态并强制容器重建"""
    # 清理所有可能的临时状态
    keys_to_clear = [
        'is_sorting', 'scan_files', 'demo_cookie', 'batch_tags',
        'search_result', 'rename_result', 'poster_result', 'upload_result',
        'form_data', 'temp_input', 'processing_status', 'current_operation'
    ]
    for key in keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]

    # 强制重建主容器的关键：更新容器ID
    import time
    st.session_state['_container_id'] = f"container_{int(time.time() * 1000)}"

    # 设置切换状态，用于显示过渡效果
    st.session_state['_switching'] = True

# 合并所有功能选项，使用单一的导航逻辑
all_funcs = main_funcs + system_funcs
all_func_labels = main_func_labels + ["---分割线---"] + system_func_labels

# 找到当前选中项的索引
current_index = 0
if st.session_state.active_tab in main_funcs:
    current_index = main_funcs.index(st.session_state.active_tab)
elif st.session_state.active_tab in system_funcs:
    current_index = len(main_funcs) + 1 + system_funcs.index(st.session_state.active_tab)

# 主功能区域 - 清晰标题
st.sidebar.markdown('<div style="font-size: 14px; font-weight: bold; color: #ff512f; margin: 2px 0 1px 0;">主功能</div>', unsafe_allow_html=True)

# 现代化紧凑美观的导航按钮样式
st.sidebar.markdown("""
<style>
/* 侧边栏整体优化 - 极度紧凑 */
.css-1d391kg {
    padding-top: 0rem !important;
    padding-bottom: 0rem !important;
}
/* 侧边栏内容区域紧凑化 */
.css-1d391kg .element-container {
    margin-bottom: 0.05rem !important;
}
/* 侧边栏按钮容器紧凑化 */
.css-1d391kg .stButton {
    margin-bottom: 0.05rem !important;
}
/* 侧边栏markdown元素紧凑化 */
.css-1d391kg .stMarkdown {
    margin-bottom: 0.05rem !important;
}
/* 侧边栏分割线紧凑化 */
.css-1d391kg hr {
    margin: 0.1rem 0 !important;
}

/* 导航按钮基础样式 - 紧凑立体设计 */
.stButton > button {
    width: 100% !important;
    text-align: left !important;
    margin: -2px 0 0px 0 !important;
    border-radius: 5px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    font-size: 10px !important;
    font-weight: 600 !important;
    padding: 5px 8px !important;
    min-height: 28px !important;
    height: 28px !important;
    line-height: 1.2 !important;
    position: relative !important;
    overflow: hidden !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* 选中状态的按钮 - 立体渐变背景和阴影 */
.stButton > button[kind="primary"] {
    background: linear-gradient(135deg, #ff512f 0%, #dd2476 100%) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    box-shadow:
        0 4px 12px rgba(255, 81, 47, 0.4),
        0 2px 6px rgba(221, 36, 118, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-1px) !important;
}
.stButton > button[kind="primary"]:hover {
    background: linear-gradient(135deg, #ff6b35 0%, #ff512f 50%, #dd2476 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow:
        0 6px 16px rgba(255, 81, 47, 0.5),
        0 3px 8px rgba(221, 36, 118, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.15) !important;
}
.stButton > button[kind="primary"]:active {
    transform: translateY(0px) scale(0.98) !important;
    transition: all 0.1s ease !important;
    box-shadow:
        0 2px 6px rgba(255, 81, 47, 0.3),
        0 1px 3px rgba(221, 36, 118, 0.2),
        inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* 选中按钮的光泽效果 */
.stButton > button[kind="primary"]::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
    transition: left 0.5s !important;
}

.stButton > button[kind="primary"]:hover::before {
    left: 100% !important;
}

/* 未选中状态的按钮 - 立体玻璃效果 */
.stButton > button[kind="secondary"] {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8)) !important;
    color: #495057 !important;
    border: 1px solid rgba(222, 226, 230, 0.8) !important;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.08),
        0 1px 2px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.05) !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

/* 未选中按钮悬停效果 - 立体过渡 */
.stButton > button[kind="secondary"]:hover {
    background: linear-gradient(145deg, rgba(255, 81, 47, 0.1), rgba(255, 107, 53, 0.05)) !important;
    color: #ff512f !important;
    border-color: rgba(255, 81, 47, 0.4) !important;
    box-shadow:
        0 4px 8px rgba(255, 81, 47, 0.15),
        0 2px 4px rgba(255, 81, 47, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(255, 81, 47, 0.1) !important;
    transform: translateY(-1px) !important;
}

.stButton > button[kind="secondary"]:active {
    transform: translateY(1px) scale(0.98) !important;
    transition: all 0.1s ease !important;
    box-shadow:
        0 1px 2px rgba(0, 0, 0, 0.2),
        inset 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* 暗色主题适配 */
[data-theme="dark"] .stButton > button[kind="primary"] {
    background: linear-gradient(135deg, #ff512f 0%, #dd2476 100%) !important;
    color: #fff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4) !important;
    box-shadow:
        0 4px 12px rgba(255, 81, 47, 0.5),
        0 2px 6px rgba(221, 36, 118, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .stButton > button[kind="secondary"] {
    background: linear-gradient(145deg, rgba(45, 55, 72, 0.9), rgba(35, 39, 47, 0.8)) !important;
    color: #e2e8f0 !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 1px 2px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .stButton > button[kind="secondary"]:hover {
    background: linear-gradient(145deg, rgba(255, 81, 47, 0.2), rgba(255, 107, 53, 0.1)) !important;
    color: #ff6b47 !important;
    border-color: rgba(255, 81, 47, 0.4) !important;
    box-shadow:
        0 4px 8px rgba(255, 81, 47, 0.3),
        0 2px 4px rgba(255, 81, 47, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(255, 81, 47, 0.1) !important;
    transform: translateY(-1px) !important;
}

[data-theme="dark"] .stButton > button[kind="secondary"]:active {
    transform: translateY(1px) scale(0.98) !important;
    box-shadow:
        0 1px 2px rgba(0, 0, 0, 0.4),
        inset 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

/* 按钮点击效果 - 弹性动画 */
.stButton > button:active {
    transform: translateY(0px) scale(0.98) !important;
    transition: all 0.1s ease !important;
}

/* 按钮图标优化 */
.stButton > button {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
}

/* 分组标题样式优化 - 极度紧凑现代化 */
.nav-section-title {
    color: #ff512f !important;
    font-weight: 600 !important;
    font-size: 10px !important;
    margin: 3px 0 1px 0 !important;
    padding: 0 4px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.2px !important;
    opacity: 0.8 !important;
}

/* 分割线优化 - 极度紧凑渐变效果 */
.nav-divider {
    border: none !important;
    height: 1px !important;
    background: linear-gradient(90deg, transparent, #e0e0e0, transparent) !important;
    margin: 2px 0 !important;
}

[data-theme="dark"] .nav-divider {
    background: linear-gradient(90deg, transparent, #4a5568, transparent) !important;
}

/* 侧边栏背景优化 */
.css-1d391kg {
    background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%) !important;
}

[data-theme="dark"] .css-1d391kg {
    background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%) !important;
}

/* ====== 增强搜索详情页专用样式 ====== */
/* 搜索结果卡片样式 */
.search-result-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin: 10px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.search-result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .search-result-card {
    background: linear-gradient(145deg, #2d3748 0%, #1a202c 100%);
    border: 1px solid #4a5568;
    color: #e2e8f0;
}

/* 海报图片样式 */
.poster-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.poster-container:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

/* 功能按钮组样式 */
.function-buttons {
    display: flex;
    gap: 10px;
    margin: 15px 0;
}

.function-buttons .stButton > button {
    border-radius: 8px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

/* 状态指示器样式 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin: 2px 4px;
}

.status-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.status-error {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
}

.status-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    color: #212529;
}

.status-info {
    background: linear-gradient(45deg, #17a2b8, #6f42c1);
    color: white;
}

/* 进度条样式增强 */
.stProgress > div > div > div > div {
    background: linear-gradient(90deg, #ff512f 0%, #dd2476 100%) !important;
    border-radius: 4px !important;
    height: 8px !important;
}

/* 输入框聚焦效果 */
.stTextInput > div > div > input:focus {
    border-color: #ff512f !important;
    box-shadow: 0 0 0 2px rgba(255, 81, 47, 0.2) !important;
}

[data-theme="dark"] .stTextInput > div > div > input:focus {
    border-color: #ff6b47 !important;
    box-shadow: 0 0 0 2px rgba(255, 107, 71, 0.3) !important;
}

/* 复选框样式增强 */
.stCheckbox > label > div[data-testid="stCheckbox"] > div {
    border-color: #ff512f !important;
}

.stCheckbox > label > div[data-testid="stCheckbox"] > div[data-checked="true"] {
    background-color: #ff512f !important;
    border-color: #ff512f !important;
}

/* 展开器样式增强 */
.stExpander > div > div > div > div {
    border: 1px solid #ff512f !important;
    border-radius: 8px !important;
}

.stExpander > div > div > div > div > div {
    background: linear-gradient(145deg, #fff5f5 0%, #ffe4e1 100%) !important;
}

[data-theme="dark"] .stExpander > div > div > div > div > div {
    background: linear-gradient(145deg, #2d1b1b 0%, #1a1111 100%) !important;
}

/* 信息框样式增强 */
.stInfo {
    border-left: 4px solid #ff512f !important;
    background: linear-gradient(145deg, #fff5f5 0%, #ffe4e1 100%) !important;
    border-radius: 8px !important;
}

[data-theme="dark"] .stInfo {
    background: linear-gradient(145deg, #2d1b1b 0%, #1a1111 100%) !important;
}

/* 成功消息样式 */
.stSuccess {
    border-left: 4px solid #28a745 !important;
    background: linear-gradient(145deg, #f8fff8 0%, #e8f5e8 100%) !important;
    border-radius: 8px !important;
}

[data-theme="dark"] .stSuccess {
    background: linear-gradient(145deg, #1b2d1b 0%, #111a11 100%) !important;
}

/* 错误消息样式 */
.stError {
    border-left: 4px solid #dc3545 !important;
    background: linear-gradient(145deg, #fff8f8 0%, #ffe8e8 100%) !important;
    border-radius: 8px !important;
}

[data-theme="dark"] .stError {
    background: linear-gradient(145deg, #2d1b1b 0%, #1a1111 100%) !important;
}

/* 警告消息样式 */
.stWarning {
    border-left: 4px solid #ffc107 !important;
    background: linear-gradient(145deg, #fffef8 0%, #fef9e8 100%) !important;
    border-radius: 8px !important;
}

[data-theme="dark"] .stWarning {
    background: linear-gradient(145deg, #2d2b1b 0%, #1a1911 100%) !important;
}

/* 加载动画增强 */
.stSpinner > div {
    border-color: #ff512f transparent transparent transparent !important;
}

/* 文本区域样式 */
.stTextArea > div > div > textarea {
    border-radius: 8px !important;
    border: 1px solid #e9ecef !important;
    font-family: 'Consolas', 'Monaco', monospace !important;
}

.stTextArea > div > div > textarea:focus {
    border-color: #ff512f !important;
    box-shadow: 0 0 0 2px rgba(255, 81, 47, 0.2) !important;
}

[data-theme="dark"] .stTextArea > div > div > textarea {
    background: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e2e8f0 !important;
}

[data-theme="dark"] .stTextArea > div > div > textarea:focus {
    border-color: #ff6b47 !important;
    box-shadow: 0 0 0 2px rgba(255, 107, 71, 0.3) !important;
}
</style>
""", unsafe_allow_html=True)

for i, (func, label) in enumerate(zip(main_funcs, main_func_labels)):
    is_selected = st.session_state.active_tab == func
    if st.sidebar.button(
        label,
        key=f"main_btn_{i}",
        use_container_width=True,
        type="primary" if is_selected else "secondary"
    ):
        # 方案1：简化的切换逻辑，只在必要时清理和重新渲染
        if st.session_state.active_tab != func:
            clear_ui_state()
            st.session_state.active_tab = func
            st.rerun()

# 分割线和系统功能标题 - 简洁样式
st.sidebar.markdown('<hr style="margin: 10px 0 1px 0; border: none; border-top: 1px solid #ddd;">', unsafe_allow_html=True)
st.sidebar.markdown('<div style="font-size: 15px; font-weight: bold; color: #ff512f; margin: 1px 0 1px 0;">系统</div>', unsafe_allow_html=True)

# 系统功能区域
for i, (func, label) in enumerate(zip(system_funcs, system_func_labels)):
    is_selected = st.session_state.active_tab == func
    if st.sidebar.button(
        label,
        key=f"system_btn_{i}",
        use_container_width=True,
        type="primary" if is_selected else "secondary"
    ):
        # 方案1：简化的切换逻辑
        if st.session_state.active_tab != func:
            clear_ui_state()
            st.session_state.active_tab = func
            st.rerun()

# 先初始化
if "show_help" not in st.session_state:
    st.session_state.show_help = False
if "safe_mode" not in st.session_state:
    st.session_state.safe_mode = False

# 工具功能区域 - 紧凑布局的底部工具
st.sidebar.markdown('<hr style="margin: 75px 0 1px 0; border: none; border-top: 1px solid #ddd;">', unsafe_allow_html=True)
st.sidebar.markdown('<div style="font-size: 15px; font-weight: bold; color: #ff512f; margin: 1px 0 -40px 0;">工具</div>', unsafe_allow_html=True)

# 工具按钮专用样式 - 向下移动
st.sidebar.markdown("""
<style>
/* 工具区域按钮向下移动 */
div[data-testid="stSidebar"] .stButton:has(button[data-testid*="safe_mode_btn"]),
div[data-testid="stSidebar"] .stButton:has(button[data-testid*="help_btn"]) {
    margin-top: 10px !important;  /* 向下移动10px，可以调整这个数值 */
}
</style>
""", unsafe_allow_html=True)

# 安全模式按钮 - 紧凑版本
safe_mode_label = "🔓 关闭安全模式" if st.session_state.get("safe_mode", False) else "🔒 开启安全模式"
if st.sidebar.button(safe_mode_label, key="safe_mode_btn", use_container_width=True, type="secondary"):
    st.session_state.safe_mode = not st.session_state.get("safe_mode", False)
    st.rerun()

# 帮助按钮 - 紧凑版本
if st.sidebar.button("❓ 使用帮助", key="help_btn", use_container_width=True, type="secondary"):
    st.session_state.show_help = not st.session_state.get("show_help", False)
    st.rerun()

# 帮助弹窗 - 紧凑显示
if st.session_state.get("show_help", False):
    st.sidebar.markdown(
        """
        <div style="background:#ffe4e1;padding:8px;border-radius:6px;margin:4px 0;border-left:3px solid #ff512f;">
        <h5 style="color:#ff512f;margin:0 0 6px 0;font-size:14px;">📖 使用帮助</h5>
        <div style="font-size:11px;line-height:1.3;">
        <p style="margin:2px 0;"><strong>🏠 首页：</strong>系统统计和活动记录</p>
        <p style="margin:2px 0;"><strong>🔍 搜索：</strong>番号搜索DMM详情页</p>
        <p style="margin:2px 0;"><strong>📝 重命名：</strong>批量重命名文件</p>
        <p style="margin:2px 0;"><strong>🖼️ 海报：</strong>获取高清海报图片</p>
        <p style="margin:2px 0;"><strong>🗂️ 整理：</strong>自动归档文件结构</p>
        <p style="margin:2px 0;"><strong>📊 设置：</strong>管理配置文件</p>
        </div>
        </div>
        """, unsafe_allow_html=True
    )
        
# 获取当前激活的标签页
active_tab = st.session_state.active_tab

# 方案2：强制容器隔离系统
def create_isolated_container():
    """创建完全隔离的内容容器"""
    # 获取或创建容器ID
    if '_container_id' not in st.session_state:
        import time
        st.session_state['_container_id'] = f"container_{int(time.time() * 1000)}"

    container_id = st.session_state['_container_id']

    # 如果正在切换，显示过渡效果
    if st.session_state.get('_switching', False):
        # 清除切换状态
        st.session_state['_switching'] = False

        # 强制清空页面内容的CSS
        st.markdown(f"""
        <style>
        /* 立即隐藏所有内容 */
        .main .block-container > div:not([data-container-id="{container_id}"]) {{
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }}

        /* 确保新容器立即显示 */
        [data-container-id="{container_id}"] {{
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }}
        </style>
        """, unsafe_allow_html=True)

    return container_id

# 创建隔离容器
container_id = create_isolated_container()

# 移除状态指示器以节省空间


# ====== 增强的搜索详情页功能模块 ======

def search_dmm_enhanced(code):
    """
    增强的DMM搜索功能
    返回详细的搜索结果，包括基本信息和状态
    """
    print(f"🔍 开始搜索番号: {code}")
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        print(f"🔍 调用dmm_tools.search_dmm_detail({code})")
        result = loop.run_until_complete(dmm_tools.search_dmm_detail(code))
        print(f"🔍 dmm_tools.search_dmm_detail返回结果: {result}")

        if result:
            success_result = {
                "success": True,
                "code": code,
                "cid": result['cid'],
                "label": result['label'],
                "url": result['url'],
                "message": f"✅ 成功找到详情页"
            }
            print(f"🔍 返回成功结果: {success_result}")
            return success_result
        else:
            fail_result = {
                "success": False,
                "code": code,
                "cid": None,
                "label": None,
                "url": None,
                "message": f"❌ 未能找到有效的DMM详情页，请检查番号或稍后重试"
            }
            print(f"🔍 返回失败结果: {fail_result}")
            return fail_result
    except Exception as e:
        error_result = {
            "success": False,
            "code": code,
            "cid": None,
            "label": None,
            "url": None,
            "message": f"❌ 搜索异常: {str(e)}"
        }
        print(f"🔍 搜索异常: {e}")
        import traceback
        traceback.print_exc()
        return error_result

def get_poster_for_search(code, need_4k=False, need_subtitle=False):
    """
    为搜索功能获取海报图片
    使用DMM的海报URL构建机制
    """
    try:
        # 首先尝试从搜索结果中获取CID
        search_result = search_dmm_enhanced(code)
        if not search_result["success"]:
            return {
                "success": False,
                "poster_bytes": None,
                "thumb_bytes": None,
                "message": "无法获取CID，搜索失败"
            }

        cid = search_result["cid"]
        if not cid:
            return {
                "success": False,
                "poster_bytes": None,
                "thumb_bytes": None,
                "message": "CID为空，无法构建海报URL"
            }

        # 构建DMM海报URL
        thumb_url = f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{cid}/{cid}pl.jpg'
        poster_url = f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{cid}/{cid}ps.jpg'

        print(f"尝试获取海报: thumb_url={thumb_url}, poster_url={poster_url}")

        # 设置请求头
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
            "Referer": "https://www.dmm.co.jp/",
            "Accept": "image/webp,image/apng,image/*,*/*;q=0.8"
        }

        poster_bytes = None
        thumb_bytes = None
        messages = []

        # 获取海报图片
        try:
            poster_resp = requests.get(poster_url, headers=headers, timeout=10)
            print(f"海报请求状态: {poster_resp.status_code}")
            if poster_resp.status_code == 200:
                poster_bytes = poster_resp.content
                messages.append(f"✅ 海报获取成功 ({len(poster_bytes)} bytes)")

                # 添加水印处理
                if need_subtitle or need_4k:
                    try:
                        poster_bytes = add_watermarks_to_image(poster_bytes, need_4k, need_subtitle)
                        messages.append("✅ 水印添加成功")
                    except Exception as e:
                        messages.append(f"⚠️ 水印添加失败: {str(e)}")
            else:
                messages.append(f"❌ 海报获取失败，状态码: {poster_resp.status_code}")
        except Exception as e:
            messages.append(f"❌ 海报请求异常: {str(e)}")

        # 获取缩略图
        try:
            thumb_resp = requests.get(thumb_url, headers=headers, timeout=10)
            print(f"缩略图请求状态: {thumb_resp.status_code}")
            if thumb_resp.status_code == 200:
                thumb_bytes = thumb_resp.content
                messages.append(f"✅ 缩略图获取成功 ({len(thumb_bytes)} bytes)")
            else:
                messages.append(f"❌ 缩略图获取失败，状态码: {thumb_resp.status_code}")
        except Exception as e:
            messages.append(f"❌ 缩略图请求异常: {str(e)}")

        success = poster_bytes is not None or thumb_bytes is not None
        message = "\n".join(messages) if messages else "未获取到任何图片"

        return {
            "success": success,
            "poster_bytes": poster_bytes,
            "thumb_bytes": thumb_bytes,
            "message": message,
            "cid": cid,
            "poster_url": poster_url,
            "thumb_url": thumb_url
        }

    except Exception as e:
        return {
            "success": False,
            "poster_bytes": None,
            "thumb_bytes": None,
            "message": f"获取海报失败: {str(e)}"
        }

def get_poster_with_cid(cid, need_4k=False, need_subtitle=False):
    """
    直接使用CID获取海报图片
    """
    try:
        if not cid:
            return {
                "success": False,
                "poster_bytes": None,
                "thumb_bytes": None,
                "message": "CID为空，无法构建海报URL"
            }

        # 构建DMM海报URL
        thumb_url = f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{cid}/{cid}pl.jpg'
        poster_url = f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{cid}/{cid}ps.jpg'

        print(f"尝试获取海报: thumb_url={thumb_url}, poster_url={poster_url}")

        # 设置请求头
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
            "Referer": "https://www.dmm.co.jp/",
            "Accept": "image/webp,image/apng,image/*,*/*;q=0.8"
        }

        poster_bytes = None
        thumb_bytes = None
        messages = []

        # 获取海报图片
        try:
            poster_resp = requests.get(poster_url, headers=headers, timeout=10)
            print(f"海报请求状态: {poster_resp.status_code}")
            if poster_resp.status_code == 200:
                poster_bytes = poster_resp.content
                messages.append(f"✅ 海报获取成功 ({len(poster_bytes)} bytes)")

                # 添加水印处理
                if need_subtitle or need_4k:
                    try:
                        poster_bytes = add_watermarks_to_image(poster_bytes, need_4k, need_subtitle)
                        messages.append("✅ 水印添加成功")
                    except Exception as e:
                        messages.append(f"⚠️ 水印添加失败: {str(e)}")
            else:
                messages.append(f"❌ 海报获取失败，状态码: {poster_resp.status_code}")
        except Exception as e:
            messages.append(f"❌ 海报请求异常: {str(e)}")

        # 获取缩略图
        try:
            thumb_resp = requests.get(thumb_url, headers=headers, timeout=10)
            print(f"缩略图请求状态: {thumb_resp.status_code}")
            if thumb_resp.status_code == 200:
                thumb_bytes = thumb_resp.content
                messages.append(f"✅ 缩略图获取成功 ({len(thumb_bytes)} bytes)")
            else:
                messages.append(f"❌ 缩略图获取失败，状态码: {thumb_resp.status_code}")
        except Exception as e:
            messages.append(f"❌ 缩略图请求异常: {str(e)}")

        success = poster_bytes is not None or thumb_bytes is not None
        message = "\n".join(messages) if messages else "未获取到任何图片"

        return {
            "success": success,
            "poster_bytes": poster_bytes,
            "thumb_bytes": thumb_bytes,
            "message": message,
            "cid": cid,
            "poster_url": poster_url,
            "thumb_url": thumb_url
        }

    except Exception as e:
        return {
            "success": False,
            "poster_bytes": None,
            "thumb_bytes": None,
            "message": f"获取海报失败: {str(e)}"
        }

def get_image_info(image_bytes):
    """获取图片信息（尺寸、大小等）"""
    try:
        from PIL import Image
        import io

        # 获取图片尺寸
        image = Image.open(io.BytesIO(image_bytes))
        width, height = image.size

        # 获取文件大小
        size_kb = len(image_bytes) / 1024
        size_mb = size_kb / 1024

        if size_mb >= 1:
            size_str = f"{size_mb:.1f} MB"
        else:
            size_str = f"{size_kb:.1f} KB"

        return {
            "width": width,
            "height": height,
            "size": size_str,
            "format": image.format or "Unknown"
        }
    except Exception as e:
        return {
            "width": "Unknown",
            "height": "Unknown",
            "size": "Unknown",
            "format": "Unknown"
        }

def display_image_with_preview(image_bytes, title, image_type="poster", max_width=300):
    """
    显示带预览功能的图片 - 简化版本
    """
    if not image_bytes:
        return

    try:
        # 获取图片信息
        img_info = get_image_info(image_bytes)

        # 显示图片信息
        info_text = f"📏 {img_info['width']}×{img_info['height']} | 📦 {img_info['size']}"
        st.caption(info_text)

        # 显示缩略图
        st.image(image_bytes, caption=f"🖼️ {title}", width=max_width)

        # 操作按钮行
        col1, col2, col3 = st.columns(3)

        with col1:
            # 查看大图按钮 - 使用expander实现
            with st.expander("🔍 查看大图"):
                st.image(image_bytes, caption=f"{title} - 原始尺寸", use_container_width=True)
                st.caption(f"原始尺寸: {info_text}")

        with col2:
            # 下载按钮
            st.download_button(
                label="💾 下载",
                data=image_bytes,
                file_name=f"{title.replace('🖼️ ', '').replace(' ', '_')}.jpg",
                mime="image/jpeg",
                key=f"download_{image_type}_{id(image_bytes)}",
                use_container_width=True
            )

        with col3:
            # 显示详细信息按钮
            with st.expander("📋 详细信息"):
                st.write(f"**尺寸**: {img_info['width']} × {img_info['height']} 像素")
                st.write(f"**大小**: {img_info['size']}")
                st.write(f"**格式**: {img_info['format']}")

                # 提供复制用的文本
                info_for_copy = f"尺寸: {img_info['width']}×{img_info['height']}\n大小: {img_info['size']}\n格式: {img_info['format']}"
                st.text_area("复制以下信息:", value=info_for_copy, height=80, key=f"info_copy_{image_type}_{id(image_bytes)}")

    except Exception as e:
        st.error(f"❌ 图片显示异常: {str(e)}")

def learn_from_successful_cid(cid):
    """
    从成功的CID中学习新的前缀规则
    """
    try:
        print(f"🎓 开始从成功的CID学习: {cid}")

        # 检测数字前缀格式 (如: 1240milk00251)
        # 这种格式实际上应该对应 h_1240milk00251
        number_prefix_match = re.match(r"^(\d{1,4})([a-z]+)", cid.lower())
        if number_prefix_match:
            h_num = int(number_prefix_match.group(1))
            prefix = number_prefix_match.group(2)
            print(f"🎓 检测到数字前缀格式，应该对应h_prefix: h_{h_num}, prefix={prefix}")

            # 更新 h_prefix_numbers.json
            try:
                with open("h_prefix_numbers.json", "r", encoding="utf-8") as f:
                    h_numbers = json.load(f)
            except Exception:
                h_numbers = []

            if h_num not in h_numbers:
                h_numbers.append(h_num)
                h_numbers.sort()
                with open("h_prefix_numbers.json", "w", encoding="utf-8") as f:
                    json.dump(h_numbers, f, ensure_ascii=False, indent=2)
                print(f"🎓 成功添加h_prefix: {h_num}")

            # 更新 prefix_h_prefix_map.json
            try:
                with open("prefix_h_prefix_map.json", "r", encoding="utf-8") as f:
                    prefix_h_map = json.load(f)
            except Exception:
                prefix_h_map = {}

            if prefix not in prefix_h_map:
                prefix_h_map[prefix] = []
            if h_num not in prefix_h_map[prefix]:
                prefix_h_map[prefix].append(h_num)
                prefix_h_map[prefix].sort()
                with open("prefix_h_prefix_map.json", "w", encoding="utf-8") as f:
                    json.dump(prefix_h_map, f, ensure_ascii=False, indent=2)
                print(f"🎓 成功添加前缀映射: {prefix} -> {h_num}")
                return f"✅ 已学习新的h_prefix映射: {prefix} -> h_{h_num}"
            else:
                print(f"🎓 前缀映射已存在: {prefix} -> {h_num}")
                return f"ℹ️ 前缀映射已存在: {prefix} -> h_{h_num}"

        # 检测h_prefix格式 (如: h_1240milk00251)
        h_prefix_match = re.match(r"h_(\d{3,4})(.+)", cid.lower())
        if h_prefix_match:
            h_num = int(h_prefix_match.group(1))
            remaining_part = h_prefix_match.group(2)
            print(f"🎓 检测到h_prefix: h_{h_num}, remaining={remaining_part}")

            # 更新 h_prefix_numbers.json
            try:
                with open("h_prefix_numbers.json", "r", encoding="utf-8") as f:
                    h_numbers = json.load(f)
            except Exception:
                h_numbers = []

            if h_num not in h_numbers:
                h_numbers.append(h_num)
                h_numbers.sort()
                with open("h_prefix_numbers.json", "w", encoding="utf-8") as f:
                    json.dump(h_numbers, f, ensure_ascii=False, indent=2)
                print(f"🎓 成功添加h_prefix: {h_num}")

            # 更新 prefix_h_prefix_map.json
            try:
                with open("prefix_h_prefix_map.json", "r", encoding="utf-8") as f:
                    prefix_h_map = json.load(f)
            except Exception:
                prefix_h_map = {}

            # 提取实际前缀 (如: milk)
            actual_prefix_match = re.match(r"([a-z]+)", remaining_part)
            if actual_prefix_match:
                actual_prefix = actual_prefix_match.group(1)
                if actual_prefix not in prefix_h_map:
                    prefix_h_map[actual_prefix] = []
                if h_num not in prefix_h_map[actual_prefix]:
                    prefix_h_map[actual_prefix].append(h_num)
                    prefix_h_map[actual_prefix].sort()
                    with open("prefix_h_prefix_map.json", "w", encoding="utf-8") as f:
                        json.dump(prefix_h_map, f, ensure_ascii=False, indent=2)
                    print(f"🎓 成功添加前缀映射: {actual_prefix} -> {h_num}")
                    return f"✅ 已学习新的h_prefix映射: {actual_prefix} -> h_{h_num}"

        return "ℹ️ 未检测到新的前缀规则"

    except Exception as e:
        error_msg = f"学习前缀规则失败: {str(e)}"
        print(f"🎓 {error_msg}")
        return f"❌ {error_msg}"

def add_watermarks_to_image(image_bytes, need_4k=False, need_subtitle=False):
    """
    为图片添加水印
    """
    try:
        from PIL import Image
        import io

        # 打开图片
        image = Image.open(io.BytesIO(image_bytes)).convert("RGBA")

        # 水印文件路径
        watermark_paths = {
            "4k": "./fonts/4k.png",
            "subtitle": "./fonts/sub.png"
        }

        # 添加字幕水印（左上角）
        if need_subtitle and os.path.exists(watermark_paths["subtitle"]):
            try:
                watermark = Image.open(watermark_paths["subtitle"]).convert("RGBA")
                # 缩放水印
                iw, ih = image.size
                ww, wh = watermark.size
                scale = 0.18
                new_ww = int(iw * scale)
                new_wh = int(wh * (new_ww / ww))
                watermark = watermark.resize((new_ww, new_wh), Image.LANCZOS)
                # 左上角位置
                x, y = 10, 10
                image.alpha_composite(watermark, (x, y))
            except Exception as e:
                print(f"添加字幕水印失败: {e}")

        # 添加4K水印（右下角）
        if need_4k and os.path.exists(watermark_paths["4k"]):
            try:
                watermark = Image.open(watermark_paths["4k"]).convert("RGBA")
                # 缩放水印
                iw, ih = image.size
                ww, wh = watermark.size
                scale = 0.18
                new_ww = int(iw * scale)
                new_wh = int(wh * (new_ww / ww))
                watermark = watermark.resize((new_ww, new_wh), Image.LANCZOS)
                # 右下角位置
                x, y = iw - new_ww - 10, ih - new_wh - 10
                image.alpha_composite(watermark, (x, y))
            except Exception as e:
                print(f"添加4K水印失败: {e}")

        # 转换回字节
        output = io.BytesIO()
        image.convert("RGB").save(output, format="JPEG", quality=95)
        return output.getvalue()

    except Exception as e:
        print(f"水印处理失败: {e}")
        return image_bytes  # 返回原图

def auto_rename_files_for_code(code, target_dir, dmm_result):
    """
    根据搜索结果自动重命名指定目录下的对应文件
    复用批量重命名的逻辑
    """
    if not dmm_result or not dmm_result.get("success"):
        return {"success": False, "message": "无有效的DMM信息进行重命名"}

    if not os.path.exists(target_dir):
        return {"success": False, "message": f"目录不存在: {target_dir}"}

    try:
        # 查找匹配的文件
        matched_files = []
        base_code = code.upper().replace(" ", "").replace("-", "-")

        for root, dirs, files in os.walk(target_dir):
            for file in files:
                if file.lower().endswith('.mp4'):
                    # 检查文件名是否包含番号
                    file_upper = file.upper()
                    if base_code in file_upper or code.upper() in file_upper:
                        matched_files.append(os.path.join(root, file))

        if not matched_files:
            return {"success": False, "message": f"在目录 {target_dir} 中未找到匹配番号 {code} 的文件"}

        # 重命名文件
        renamed_files = []
        cid = dmm_result.get("cid", "")

        for file_path in matched_files:
            try:
                dir_name = os.path.dirname(file_path)
                old_name = os.path.basename(file_path)

                # 提取基础番号和后缀
                match = re.match(r'^([A-Z0-9]+-\d+)(.*?)\.mp4$', old_name, re.I)
                if match:
                    base_code = match.group(1)
                    suffix = match.group(2)

                    # 生成新文件名
                    if cid:
                        new_name = f"{base_code} ({cid}){suffix}.mp4"
                    else:
                        new_name = old_name  # 如果没有CID，保持原名

                    new_path = os.path.join(dir_name, new_name)

                    if file_path != new_path:
                        os.rename(file_path, new_path)
                        renamed_files.append(f"✅ {old_name} → {new_name}")
                    else:
                        renamed_files.append(f"⚪ {old_name} (无需重命名)")
                else:
                    renamed_files.append(f"⚠️ {old_name} (格式不匹配，跳过)")

            except Exception as e:
                renamed_files.append(f"❌ {os.path.basename(file_path)} (重命名失败: {str(e)})")

        return {
            "success": True,
            "message": f"处理完成，共处理 {len(matched_files)} 个文件",
            "details": renamed_files
        }

    except Exception as e:
        return {"success": False, "message": f"重命名过程出错: {str(e)}"}

def save_dmm_data_to_json(dmm_result):
    """
    将DMM搜索结果保存到JSON配置文件
    完全复用dmm_tools.py中的逻辑，保持数据结构的一致性
    """
    if not dmm_result or not dmm_result.get("success"):
        return {"success": False, "message": "无有效数据可保存"}

    try:
        code = dmm_result.get("code", "")
        cid = dmm_result.get("cid", "")

        if not code or not cid:
            return {"success": False, "message": "缺少必要的番号或CID信息"}

        print(f"开始保存数据: code={code}, cid={cid}")

        # 解析CID格式
        cid_lower = cid.lower()
        saved_items = []

        # 检查是否是h_prefix格式 (如: h_1462cawd00849)
        h_prefix_match = re.match(r"h_(\d{3,4})(.+)", cid_lower)
        if h_prefix_match:
            h_num = int(h_prefix_match.group(1))
            remaining_part = h_prefix_match.group(2)

            print(f"检测到h_prefix格式: h_num={h_num}, remaining={remaining_part}")

            # 更新 h_prefix_numbers.json
            try:
                h_numbers = load_json("h_prefix_numbers.json")
                if not isinstance(h_numbers, list):
                    h_numbers = []

                if h_num not in h_numbers:
                    h_numbers.append(h_num)
                    h_numbers.sort()
                    save_result = save_json("h_prefix_numbers.json", h_numbers)
                    if "成功" in save_result:
                        saved_items.append(f"h_prefix_numbers.json: 添加 {h_num}")
                        print(f"成功添加h_prefix: {h_num}")
                    else:
                        print(f"保存h_prefix失败: {save_result}")
                else:
                    print(f"h_prefix {h_num} 已存在")

            except Exception as e:
                print(f"更新h_prefix_numbers.json失败: {e}")

            # 更新 prefix_h_prefix_map.json
            try:
                # 提取实际前缀（去掉数字部分）
                prefix_match = re.match(r"^([a-z]+)", remaining_part)
                if prefix_match:
                    actual_prefix = prefix_match.group(1)

                    print(f"提取到实际前缀: {actual_prefix}")

                    try:
                        with open("prefix_h_prefix_map.json", "r", encoding="utf-8") as f:
                            prefix_h_map = json.load(f)
                    except:
                        prefix_h_map = {}

                    if actual_prefix not in prefix_h_map:
                        prefix_h_map[actual_prefix] = []

                    if h_num not in prefix_h_map[actual_prefix]:
                        prefix_h_map[actual_prefix].append(h_num)
                        prefix_h_map[actual_prefix].sort()
                        save_result = save_json("prefix_h_prefix_map.json", prefix_h_map)
                        if "成功" in save_result:
                            saved_items.append(f"prefix_h_prefix_map.json: {actual_prefix} -> {h_num}")
                            print(f"成功添加前缀映射: {actual_prefix} -> {h_num}")
                        else:
                            print(f"保存前缀映射失败: {save_result}")
                    else:
                        print(f"前缀映射 {actual_prefix} -> {h_num} 已存在")

            except Exception as e:
                print(f"更新prefix_h_prefix_map.json失败: {e}")

        # 检查是否是数字前缀格式 (如: 1cawd00849, 140cawd00849)
        number_prefix_match = re.match(r"^(\d{1,4})([a-z]+)", cid_lower)
        if number_prefix_match:
            num_prefix = number_prefix_match.group(1)

            print(f"检测到数字前缀格式: num_prefix={num_prefix}")

            try:
                number_prefixes = load_json("number_prefixes.json")
                if not isinstance(number_prefixes, list):
                    number_prefixes = []

                if num_prefix not in number_prefixes:
                    number_prefixes.append(num_prefix)
                    # 按数字大小排序
                    number_prefixes.sort(key=lambda x: int(x) if x.isdigit() else float('inf'))
                    save_result = save_json("number_prefixes.json", number_prefixes)
                    if "成功" in save_result:
                        saved_items.append(f"number_prefixes.json: 添加 {num_prefix}")
                        print(f"成功添加数字前缀: {num_prefix}")
                    else:
                        print(f"保存数字前缀失败: {save_result}")
                else:
                    print(f"数字前缀 {num_prefix} 已存在")

            except Exception as e:
                print(f"更新number_prefixes.json失败: {e}")

        # 检查是否是普通前缀格式，需要添加到基础前缀列表
        simple_prefix_match = re.match(r"^([a-z]+)\d+$", cid_lower)
        if simple_prefix_match and not h_prefix_match and not number_prefix_match:
            simple_prefix = simple_prefix_match.group(1)

            print(f"检测到普通前缀格式: simple_prefix={simple_prefix}")

            # 这种情况下，我们可能需要记录这个前缀，但当前的JSON结构中没有专门的地方
            # 可以考虑在未来扩展一个基础前缀列表
            saved_items.append(f"识别到基础前缀: {simple_prefix}")

        if saved_items:
            message = f"✅ 数据保存成功\n番号: {code}\nCID: {cid}\n\n更新项目:\n" + "\n".join(saved_items)
        else:
            message = f"ℹ️ 数据已存在，无需更新\n番号: {code}\nCID: {cid}"

        return {
            "success": True,
            "message": message
        }

    except Exception as e:
        error_msg = f"保存数据失败: {str(e)}"
        print(error_msg)
        return {"success": False, "message": error_msg}

def parse_manual_dmm_url(url):
    """
    解析手动输入的DMM详情页URL
    提取CID和其他信息
    """
    try:
        # 从URL中提取CID
        cid_match = re.search(r'/cid=([^/?&]+)', url)
        if not cid_match:
            return {"success": False, "message": "无法从URL中提取CID"}

        cid = cid_match.group(1)

        # 尝试获取页面信息（简化版本）
        try:
            import requests
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
                "Cookie": "age_check_done=1; cklg=ja"
            }

            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, "html.parser")

                # 尝试提取厂牌信息
                label_tag = soup.find("a", href=re.compile(r"/digital/videoa/-/list/=/article=maker/id="))
                label = label_tag.text.strip() if label_tag else "未知厂牌"

                return {
                    "success": True,
                    "cid": cid,
                    "label": label,
                    "url": url,
                    "message": "✅ 成功解析手动输入的详情页"
                }
            else:
                return {
                    "success": False,
                    "message": f"无法访问详情页，状态码: {response.status_code}"
                }

        except Exception as e:
            # 即使无法获取页面信息，也返回基本的CID信息
            return {
                "success": True,
                "cid": cid,
                "label": "未知厂牌",
                "url": url,
                "message": f"✅ 提取到CID: {cid} (无法获取详细信息: {str(e)})"
            }

    except Exception as e:
        return {"success": False, "message": f"解析URL失败: {str(e)}"}

# 保持向后兼容的原始函数
def search_dmm(code):
    """保持向后兼容的原始搜索函数"""
    result = search_dmm_enhanced(code)
    if result["success"]:
        return f"番号: {result['code']}\nCID: {result['cid']}\n厂牌: {result['label']}\n详情页: {result['url']}"
    else:
        return result["message"]

def batch_rename(dir_path):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    msg_lines = loop.run_until_complete(dmm_tools.rename_files_with_dmm_info(dir_path))
    return "\n".join(msg_lines)

def upload_file(file):
    save_path = os.path.join("./", file.name)
    with open(save_path, "wb") as f:
        f.write(file.read())
    return f"已上传: {save_path}"

def get_poster(code, need_4k=False, need_subtitle=False):
    try:
        from 小姐姐整理入库2 import extract_code, build_urls, add_image_watermark, get_save_dir
    except ImportError:
        # 如果导入失败，尝试从当前目录导入
        try:
            import sys
            import os
            sys.path.append(os.path.dirname(__file__))
            from 小姐姐整理入库2 import extract_code, build_urls, add_image_watermark, get_save_dir
        except ImportError:
            # 如果仍然失败，提供备用实现
            def extract_code(text):
                match = re.search(r'\(([^)]+)\)', text)
                if match:
                    return match.group(1).strip().lower()
                if re.match(r'^[a-zA-Z0-9_]+$', text.strip()):
                    return text.strip().lower()
                return None

            def build_urls(code):
                url1 = f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}pl.jpg'
                url2 = f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}ps.jpg'
                return url1, url2

            def add_image_watermark(image_bytes, watermark_path, position="left_top", scale=0.18):
                # 简化的水印实现
                return image_bytes

            def get_save_dir(folder_name):
                # 简化的保存目录实现
                base_dir = "./downloads"
                os.makedirs(base_dir, exist_ok=True)
                return base_dir
    # 新增：只用前两段作为目录
    def extract_base_code(code):
        m = re.match(r"^([A-Z0-9]+-\d+)", code, re.I)
        return m.group(1) if m else code

    cid = extract_code(code)
    if not cid:
        return None, None, "未识别到有效番号"
    url1, url2 = build_urls(cid)
    try:
        poster_resp = requests.get(url2, timeout=8)
        thumb_resp = requests.get(url1, timeout=8)
        if poster_resp.status_code != 200:
            return None, None, "未找到高清海报"
        poster_bytes = poster_resp.content
        thumb_bytes = thumb_resp.content if thumb_resp.status_code == 200 else None
        watermark_scale = 0.18
        status_msg = ""
        folder_name = code.strip()
        dir_name = extract_dir_name(folder_name)
        save_dir = get_save_dir(dir_name)
        # 文件名保留完整番号
        poster_path = os.path.join(save_dir, f"{folder_name}-poster.jpg")
        thumb_path = os.path.join(save_dir, f"{folder_name}-thumb.jpg")
        fanart_path = os.path.join(save_dir, f"{folder_name}-fanart.jpg")

        # 先加水印（poster）
        poster_bytes_with_watermark = poster_bytes
        if need_subtitle:
            poster_bytes_with_watermark = add_image_watermark(
                poster_bytes_with_watermark,
                "/vol1/1000/脚本项目/多功能TGbot脚本/fonts/sub.png",
                position="left_top",
                scale=watermark_scale
            )
        if need_4k:
            poster_bytes_with_watermark = add_image_watermark(
                poster_bytes_with_watermark,
                "/vol1/1000/脚本项目/多功能TGbot脚本/fonts/4k.png",
                position="right_bottom",
                scale=watermark_scale
            )

        # 判断是否需要替换 poster
        need_save_poster = True
        if os.path.exists(poster_path):
            old_size = os.path.getsize(poster_path)
            new_size = len(poster_bytes_with_watermark)
            if new_size <= old_size:
                status_msg = f"目录已有海报，且新图片({new_size//1024}KB)小于等于原图({old_size//1024}KB)，未做替换。"
                need_save_poster = False
            else:
                status_msg = f"目录已有海报，且新图片({new_size//1024}KB)大于原图({old_size//1024}KB)，已替换。"
        else:
            status_msg = "海报已保存到项目目录。"

        # 保存 poster（加水印后的）
        if need_save_poster:
            with open(poster_path, "wb") as f:
                f.write(poster_bytes_with_watermark)

        # 保存 thumb（原图）
        if thumb_bytes:
            with open(thumb_path, "wb") as f:
                f.write(thumb_bytes)

            # 复制 thumb 为 fanart，并根据参数决定是否加水印
            fanart_bytes = thumb_bytes
            if need_subtitle:
                fanart_bytes = add_image_watermark(
                    fanart_bytes,
                    "/vol1/1000/脚本项目/多功能TGbot脚本/fonts/sub.png",
                    position="left_top",
                    scale=watermark_scale
                )
            if need_4k:
                fanart_bytes = add_image_watermark(
                    fanart_bytes,
                    "/vol1/1000/脚本项目/多功能TGbot脚本/fonts/4k.png",
                    position="right_bottom",
                    scale=watermark_scale
                )
            # 判断是否需要替换 fanart
            need_save_fanart = True
            if os.path.exists(fanart_path):
                old_size = os.path.getsize(fanart_path)
                new_size = len(fanart_bytes)
                if new_size <= old_size:
                    need_save_fanart = False
            if need_save_fanart:
                with open(fanart_path, "wb") as f:
                    f.write(fanart_bytes)

        return poster_bytes_with_watermark, thumb_bytes, status_msg
    except Exception as e:
        return None, None, f"请求失败: {e}"

@st.cache_data(show_spinner=False)
def load_json(path):
    try:
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return []

def save_json(path, data):
    try:
        with open(path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return "保存成功"
    except Exception as e:
        return f"保存失败: {e}"

# 背景设置配置文件路径
BG_CONFIG_FILE = os.path.join(BG_UPLOAD_DIR, "bg_config.json")

def load_bg_config():
    """从配置文件加载背景设置"""
    try:
        if os.path.exists(BG_CONFIG_FILE):
            with open(BG_CONFIG_FILE, "r", encoding="utf-8") as f:
                config = json.load(f)
                return config.get("bg_img_path", ""), config.get("bg_img_url", "")
    except Exception as e:
        print(f"加载背景配置失败: {e}")
    return "", ""

def save_bg_config(bg_img_path="", bg_img_url=""):
    """保存背景设置到配置文件"""
    try:
        os.makedirs(BG_UPLOAD_DIR, exist_ok=True)
        config = {
            "bg_img_path": bg_img_path,
            "bg_img_url": bg_img_url
        }
        with open(BG_CONFIG_FILE, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存背景配置失败: {e}")
        return False

# ====== STRM整理功能 ======
# 支持的视频后缀
VIDEO_EXTS = ['.mp4', '.mkv', '.avi', '.mov']
# 支持的元数据后缀
META_EXTS = ['.jpg', '.png', '.nfo', '.srt', '.ass', '.sub']

def ensure_dir(path):
    """确保目录存在"""
    if not os.path.exists(path):
        os.makedirs(path)

def create_strm_file(video_path, strm_path, url_prefix):
    """为单个视频文件创建strm文件"""
    try:
        # 获取相对路径
        video_dir = os.path.dirname(video_path)
        video_name = os.path.basename(video_path)
        strm_name = os.path.splitext(video_name)[0] + '.strm'
        strm_file = os.path.join(strm_path, strm_name)

        # 构建URL
        rel_path = os.path.relpath(video_path, os.path.dirname(video_path))
        url = f"{url_prefix}/{rel_path}".replace("\\", "/")

        # 创建strm文件
        ensure_dir(strm_path)
        with open(strm_file, 'w', encoding='utf-8') as f:
            f.write(url)

        return strm_file, url
    except Exception as e:
        return None, str(e)

def batch_create_strm(src_dir, strm_dir, url_prefix):
    """批量创建strm文件"""
    results = []
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            ext = os.path.splitext(file)[1].lower()
            if ext in VIDEO_EXTS:
                video_path = os.path.join(root, file)
                rel_path = os.path.relpath(root, src_dir)
                strm_path = os.path.join(strm_dir, rel_path)

                strm_file, result = create_strm_file(video_path, strm_path, url_prefix)
                if strm_file:
                    results.append(f"✅ 创建: {strm_file}")
                else:
                    results.append(f"❌ 失败: {video_path} - {result}")
    return results

def update_strm_urls(strm_dir, old_prefix, new_prefix):
    """批量修改strm文件中的URL前缀"""
    results = []
    for root, dirs, files in os.walk(strm_dir):
        for file in files:
            if file.endswith('.strm'):
                strm_file = os.path.join(root, file)
                try:
                    with open(strm_file, 'r', encoding='utf-8') as f:
                        content = f.read().strip()

                    if old_prefix in content:
                        new_content = content.replace(old_prefix, new_prefix)
                        with open(strm_file, 'w', encoding='utf-8') as f:
                            f.write(new_content)
                        results.append(f"✅ 更新: {strm_file}")
                    else:
                        results.append(f"⏭️ 跳过: {strm_file} (未包含旧前缀)")
                except Exception as e:
                    results.append(f"❌ 失败: {strm_file} - {str(e)}")
    return results

def download_metadata(video_dir, code, download_types):
    """下载nfo、海报、字幕等元数据文件"""
    results = []
    try:
        if 'nfo' in download_types:
            # 这里可以调用现有的nfo生成功能
            results.append(f"✅ NFO文件下载功能需要集成现有的元数据生成逻辑")

        if 'poster' in download_types:
            # 这里可以调用现有的海报下载功能
            results.append(f"✅ 海报下载功能需要集成现有的海报获取逻辑")

        if 'subtitle' in download_types:
            results.append(f"✅ 字幕下载功能需要开发")

    except Exception as e:
        results.append(f"❌ 元数据下载失败: {str(e)}")

    return results

# ====== 115云端管理功能 ======
class Cloud115Auth:
    """115云盘认证管理类 - 基于p115client优化"""

    def __init__(self):
        self.base_url = "https://webapi.115.com"
        self.config_file = os.path.join(BG_UPLOAD_DIR, "115_config.json")
        self.cookies_file = os.path.join(BG_UPLOAD_DIR, "115_cookies.txt")
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.p115_client = None
        self.p115_open_client = None

        # 115开放接口配置
        self.app_id = "100196115"
        self.app_key = "4b01dfb0335fd6c16b2f8422eff27e13"
        self.app_secret = "c4b7a247956907246c2492ff924347d00b0a55301ec3cff0ffff5cf6b51c17be"

        self._init_p115_client()

    def _init_p115_client(self):
        """初始化p115client和开放接口客户端"""
        try:
            from p115client import P115Client, P115OpenClient
            from pathlib import Path

            # 如果cookies文件存在，使用它初始化客户端
            if os.path.exists(self.cookies_file):
                self.p115_client = P115Client(
                    Path(self.cookies_file),
                    check_for_relogin=True
                )

                # 如果已登录，尝试初始化开放接口客户端
                try:
                    self.p115_open_client = self.p115_client.login_another_open(self.app_id)
                    print("✅ 115开放接口客户端初始化成功")
                except Exception as e:
                    error_msg = str(e)
                    print(f"⚠️ 115开放接口客户端初始化失败: {e}")

                    # 如果是登录过期错误，自动清理cookies文件
                    if "99" in error_msg or "请重新登录" in error_msg or "重新登录" in error_msg:
                        print("🔄 检测到登录过期，正在清理过期的cookies文件...")
                        try:
                            os.remove(self.cookies_file)
                            print("✅ 已清理过期的cookies文件，请重新登录")
                        except Exception as cleanup_e:
                            print(f"⚠️ 清理cookies文件失败: {cleanup_e}")

                    self.p115_open_client = None
            else:
                # 如果没有cookies文件，创建一个空的客户端用于登录
                self.p115_client = None
                self.p115_open_client = None

        except ImportError:
            print("⚠️ p115client未安装，将使用基础登录功能")
            self.p115_client = None
            self.p115_open_client = None
        except Exception as e:
            print(f"⚠️ 初始化p115client失败: {e}")
            self.p115_client = None
            self.p115_open_client = None

    def _parse_cookies(self, cookies_str):
        """解析cookies字符串为字典"""
        cookies_dict = {}
        try:
            for item in cookies_str.split(';'):
                if '=' in item:
                    key, value = item.strip().split('=', 1)
                    cookies_dict[key.strip()] = value.strip()
        except Exception as e:
            print(f"解析cookies失败: {e}")
        return cookies_dict

    def save_cookies_from_dict(self, cookies_dict):
        """从字典保存cookies到文件"""
        try:
            cookies_str = '; '.join([f"{k}={v}" for k, v in cookies_dict.items()])
            with open(self.cookies_file, 'w', encoding='utf-8') as f:
                f.write(cookies_str)

            # 重新初始化客户端
            self._init_p115_client()
            return True
        except Exception as e:
            print(f"保存cookies失败: {e}")
            return False

    def login_with_cookies(self, cookies_str):
        """使用cookies字符串登录 - p115client方式"""
        try:
            print(f"🔄 开始使用cookies登录...")
            print(f"📝 Cookies长度: {len(cookies_str)} 字符")

            # 保存cookies到文件
            os.makedirs(os.path.dirname(self.cookies_file), exist_ok=True)
            with open(self.cookies_file, 'w', encoding='utf-8') as f:
                f.write(cookies_str)
            print(f"✅ Cookies已保存到: {self.cookies_file}")

            # 使用p115client方式登录
            from p115client import P115Client
            from pathlib import Path

            # 创建客户端实例
            try:
                print("🔧 正在创建p115client实例...")
                client = P115Client(Path(self.cookies_file), check_for_relogin=True)
                self.p115_client = client
                print("✅ p115client实例创建成功")

                # 获取用户信息
                print("📊 正在获取用户信息...")
                user_info = client.user_info()
                print(f"🔍 原始用户信息: {user_info}")

                if user_info:
                    # 检查是否是115 API的标准响应格式
                    actual_data = user_info
                    if 'data' in user_info and isinstance(user_info['data'], dict):
                        actual_data = user_info['data']

                    # 尝试多种可能的字段名进行解析
                    user_id = (actual_data.get('user_id') or
                             actual_data.get('uid') or
                             actual_data.get('id') or
                             actual_data.get('userid') or '')

                    username = (actual_data.get('username') or
                              actual_data.get('user_name') or
                              actual_data.get('name') or
                              actual_data.get('nickname') or
                              actual_data.get('nick') or '')

                    email = (actual_data.get('email') or
                           actual_data.get('mail') or
                           actual_data.get('user_email') or
                           actual_data.get('pub_email') or '')

                    # VIP状态检查
                    vip_status = actual_data.get('vip', actual_data.get('is_vip', 0))
                    is_vip = (vip_status and vip_status > 0) or actual_data.get('is_vip', False)
                    vip_level = 'VIP专属用户' if is_vip else '普通用户'

                    # 存储空间信息
                    space_used = (actual_data.get('space_used') or
                                actual_data.get('used_space') or
                                actual_data.get('used') or 0)

                    space_total = (actual_data.get('space_total') or
                                 actual_data.get('total_space') or
                                 actual_data.get('total') or 0)

                    # 格式化用户信息
                    formatted_info = {
                        'user_id': str(user_id),
                        'username': str(username),
                        'email': str(email),
                        'vip_level': vip_level,
                        'space_used': self._format_size(space_used),
                        'space_total': self._format_size(space_total),
                        'raw_data': user_info  # 保留原始数据用于调试
                    }

                    print(f"✅ 格式化用户信息: {formatted_info}")

                    # 保存配置
                    config = {
                        'cookies': self._parse_cookies(cookies_str),
                        'user_info': formatted_info,
                        'login_time': datetime.now().isoformat(),
                        'status': 'logged_in'
                    }
                    self.save_config(config)
                    print("✅ 配置已保存")

                    # 尝试初始化开放接口客户端
                    try:
                        print("🔗 正在初始化115开放接口客户端...")
                        self.p115_open_client = client.login_another_open(self.app_id)
                        print("✅ 115开放接口客户端初始化成功")
                    except Exception as open_e:
                        print(f"⚠️ 115开放接口客户端初始化失败: {open_e}")
                        self.p115_open_client = None

                    return {
                        'success': True,
                        'user_info': formatted_info,
                        'is_real_client': True
                    }
                else:
                    print("❌ 用户信息为空")
                    return {'success': False, 'error': '无法获取用户信息', 'is_real_client': True}

            except Exception as e:
                print(f"❌ p115client登录失败: {e}")
                import traceback
                print(f"详细错误: {traceback.format_exc()}")
                return {
                    'success': False,
                    'error': f'Cookie登录失败: {str(e)}',
                    'details': '请检查Cookie是否正确或已过期'
                }

        except Exception as e:
            import traceback
            error_detail = traceback.format_exc()
            print(f"❌ 登录异常: {e}")
            print(f"详细错误: {error_detail}")
            return {
                'success': False,
                'error': f'登录异常: {str(e)}',
                'details': error_detail
            }

    def load_config(self):
        """加载115认证配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载115配置失败: {e}")
        return {}

    def save_config(self, config):
        """保存115认证配置"""
        try:
            os.makedirs(BG_UPLOAD_DIR, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存115配置失败: {e}")
            return False







    def is_logged_in(self):
        """检查是否已登录"""
        config = self.load_config()
        return config.get('status') == 'logged_in' and config.get('cookies')

    def get_user_info(self):
        """获取用户信息 - 使用p115client"""
        try:
            # 优先使用p115client获取真实用户信息
            if self.p115_client:
                try:
                    user_info = self.p115_client.user_info()
                    print(f"🔍 p115client返回的原始用户信息: {user_info}")

                    if user_info and isinstance(user_info, dict):
                        # 检查是否是115 API的标准响应格式
                        actual_data = user_info
                        if 'data' in user_info and isinstance(user_info['data'], dict):
                            actual_data = user_info['data']

                        # 尝试多种可能的字段名进行解析
                        user_id = ''
                        for field in ['user_id', 'uid', 'id', 'userid']:
                            value = actual_data.get(field)
                            if value:
                                user_id = str(value)
                                print(f"✅ 找到用户ID: {user_id} (字段: {field})")
                                break

                        username = ''
                        for field in ['username', 'user_name', 'name', 'nickname', 'nick']:
                            value = actual_data.get(field)
                            if value:
                                username = str(value)
                                print(f"✅ 找到用户名: {username} (字段: {field})")
                                break

                        email = ''
                        for field in ['email', 'mail', 'user_email', 'pub_email']:
                            value = actual_data.get(field)
                            if value:
                                email = str(value)
                                print(f"✅ 找到邮箱: {email} (字段: {field})")
                                break

                        # VIP状态检查 - 更全面的检查
                        vip_status = actual_data.get('is_vip', actual_data.get('vip', 0))
                        is_vip = False
                        if isinstance(vip_status, (int, float)) and vip_status > 0:
                            is_vip = True
                            print(f"✅ VIP状态: {vip_status} -> VIP用户")
                        elif actual_data.get('is_vip', False):
                            is_vip = True
                            print(f"✅ VIP状态: is_vip=True -> VIP用户")
                        elif actual_data.get('vip_level', 0) > 0:
                            is_vip = True
                            print(f"✅ VIP状态: vip_level > 0 -> VIP用户")
                        else:
                            print("⚠️ 检测为普通用户")

                        vip_level = 'VIP专属用户' if is_vip else '普通用户'

                        # 存储空间信息 - 尝试多种字段名
                        space_used = 0
                        for field in ['space_used', 'used_space', 'used', 'size_used']:
                            value = actual_data.get(field)
                            if value:
                                try:
                                    space_used = int(value)
                                    print(f"✅ 找到已用空间: {space_used} bytes (字段: {field})")
                                    break
                                except (ValueError, TypeError):
                                    continue

                        space_total = 0
                        for field in ['space_total', 'total_space', 'total', 'size_total']:
                            value = actual_data.get(field)
                            if value:
                                try:
                                    space_total = int(value)
                                    print(f"✅ 找到总空间: {space_total} bytes (字段: {field})")
                                    break
                                except (ValueError, TypeError):
                                    continue

                        # 如果没有找到存储空间信息，尝试获取
                        if space_used == 0 and space_total == 0:
                            print("⚠️ 用户信息中没有存储空间数据，尝试获取存储空间信息...")
                            try:
                                space_info = self._get_space_info()
                                if space_info:
                                    # 处理不同格式的存储空间数据
                                    if 'used' in space_info and 'total' in space_info:
                                        # fs_space_info解析后的格式
                                        space_used = space_info.get('used', 0)
                                        space_total = space_info.get('total', 0)
                                        print(f"✅ 从fs_space_info获取: 已用={space_info.get('used_format', '')}, 总计={space_info.get('total_format', '')}, 剩余={space_info.get('remain_format', '')}")

                                        # 保存存储空间API数据到用户信息中
                                        if 'source' in space_info and space_info['source'] == 'fs_space_info':
                                            # 将存储空间API数据保存到用户信息中，供UI使用
                                            space_api_data = {
                                                'used': space_used,
                                                'total': space_total,
                                                'remain': space_info.get('remain', 0),
                                                'used_format': space_info.get('used_format', ''),
                                                'total_format': space_info.get('total_format', ''),
                                                'remain_format': space_info.get('remain_format', ''),
                                                'source': 'fs_space_info'
                                            }
                                            print(f"✅ 保存存储空间API数据: {space_api_data}")

                                    elif 'used_format' in space_info and 'total_format' in space_info:
                                        # fs_space_info格式，使用原始字节数
                                        space_used = space_info.get('used', 0)
                                        space_total = space_info.get('total', 0)
                                        print(f"✅ 从fs_space_info获取: 已用={space_info.get('used_format', '')}, 总计={space_info.get('total_format', '')}, 剩余={space_info.get('remain_format', '')}")
                                    else:
                                        # 尝试其他可能的字段名
                                        space_used = (space_info.get('space_used') or
                                                    space_info.get('used_space') or
                                                    space_info.get('used') or 0)
                                        space_total = (space_info.get('space_total') or
                                                     space_info.get('total_space') or
                                                     space_info.get('total') or 0)

                                    print(f"✅ 从存储空间API获取: 已用={space_used}, 总计={space_total}")
                            except Exception as e:
                                print(f"⚠️ 获取存储空间信息失败: {e}")

                        # 格式化用户信息
                        formatted_info = {
                            'user_id': user_id,
                            'username': username,
                            'email': email,
                            'vip_level': vip_level,
                            'space_used': self._format_size(space_used),
                            'space_total': self._format_size(space_total),
                            'raw_data': user_info  # 保留原始数据用于调试和进度条计算
                        }

                        # 如果有存储空间API数据，添加到格式化信息中
                        if 'space_api_data' in locals():
                            formatted_info['space_api_data'] = space_api_data
                            print(f"✅ 添加存储空间API数据到用户信息: {space_api_data}")

                        print(f"✅ 格式化后的用户信息: {formatted_info}")

                        # 更新缓存配置
                        config = self.load_config()
                        config['user_info'] = formatted_info
                        config['last_update'] = datetime.now().isoformat()
                        self.save_config(config)

                        return {'success': True, 'data': formatted_info}
                    else:
                        print("❌ p115client返回空的用户信息或格式错误")
                        return {'success': False, 'error': 'p115client返回空的用户信息或格式错误'}

                except Exception as e:
                    print(f"❌ p115client获取用户信息失败: {e}")
                    import traceback
                    print(f"详细错误信息: {traceback.format_exc()}")

                    # 如果是登录过期错误，清理cookies
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in ['99', '登录', 'login', 'auth']):
                        print("🔄 检测到可能的登录过期，建议重新登录")
                        return {'success': False, 'error': f'登录可能已过期: {str(e)}', 'need_relogin': True}

            # 如果p115client失败，使用配置文件中的信息
            config = self.load_config()
            if config.get('status') == 'logged_in' and config.get('user_info'):
                cached_info = config.get('user_info', {})
                print(f"🔄 使用缓存的用户信息: {cached_info}")
                return {'success': True, 'data': cached_info, 'from_cache': True}
            else:
                return {'success': False, 'error': '未登录或无缓存信息'}

        except Exception as e:
            import traceback
            error_detail = traceback.format_exc()
            print(f"❌ 获取用户信息异常: {e}")
            print(f"详细错误: {error_detail}")
            return {'success': False, 'error': f'获取用户信息失败: {str(e)}', 'details': error_detail}

    def _get_space_info(self):
        """获取存储空间信息"""
        try:
            if self.p115_client:
                print("🔍 尝试获取存储空间信息...")

                # 方法1: 尝试p115client的空间相关方法
                space_methods = ['fs_space_info', 'fs_space_report', 'fs_space_summury', 'tool_space', 'space_info']
                for method_name in space_methods:
                    try:
                        if hasattr(self.p115_client, method_name):
                            method = getattr(self.p115_client, method_name)
                            space_info = method()
                            if space_info:
                                print(f"✅ 通过{method_name}获取: {space_info}")

                                # 解析fs_space_info的特殊格式
                                if method_name == 'fs_space_info' and isinstance(space_info, dict):
                                    if 'data' in space_info and isinstance(space_info['data'], dict):
                                        data = space_info['data']
                                        parsed_info = {}

                                        # 提取总容量
                                        if 'all_total' in data and isinstance(data['all_total'], dict):
                                            if 'size' in data['all_total']:
                                                parsed_info['total'] = data['all_total']['size']
                                                parsed_info['total_format'] = data['all_total'].get('size_format', '')

                                        # 提取已使用
                                        if 'all_use' in data and isinstance(data['all_use'], dict):
                                            if 'size' in data['all_use']:
                                                parsed_info['used'] = data['all_use']['size']
                                                parsed_info['used_format'] = data['all_use'].get('size_format', '')

                                        # 提取剩余空间
                                        if 'all_remain' in data and isinstance(data['all_remain'], dict):
                                            if 'size' in data['all_remain']:
                                                parsed_info['remain'] = data['all_remain']['size']
                                                parsed_info['remain_format'] = data['all_remain'].get('size_format', '')

                                        if parsed_info:
                                            print(f"✅ 解析fs_space_info数据: {parsed_info}")
                                            print(f"  总容量: {parsed_info.get('total_format', 'N/A')} ({parsed_info.get('total', 0)} bytes)")
                                            print(f"  已使用: {parsed_info.get('used_format', 'N/A')} ({parsed_info.get('used', 0)} bytes)")
                                            print(f"  剩余空间: {parsed_info.get('remain_format', 'N/A')} ({parsed_info.get('remain', 0)} bytes)")

                                            # 标记这是fs_space_info格式的数据
                                            parsed_info['source'] = 'fs_space_info'
                                            return parsed_info

                                return space_info
                    except Exception as e:
                        print(f"⚠️ {method_name}方法失败: {e}")

                # 方法2: 尝试获取用户配额信息
                try:
                    if hasattr(self.p115_client, 'quota_info'):
                        quota_info = self.p115_client.quota_info()
                        if quota_info:
                            print(f"✅ 通过quota_info获取: {quota_info}")
                            return quota_info
                except Exception as e:
                    print(f"⚠️ quota_info方法失败: {e}")

                # 方法3: 尝试通过文件列表API获取空间信息
                try:
                    if hasattr(self.p115_client, 'list_files'):
                        files_info = self.p115_client.list_files("0")
                        if files_info and isinstance(files_info, dict):
                            print(f"🔍 文件列表响应: {files_info}")
                            # 有些API会在文件列表响应中包含空间信息
                            if 'space_info' in files_info:
                                print(f"✅ 从文件列表获取space_info: {files_info['space_info']}")
                                return files_info['space_info']
                            if 'quota' in files_info:
                                print(f"✅ 从文件列表获取quota: {files_info['quota']}")
                                return files_info['quota']
                            if 'data' in files_info and isinstance(files_info['data'], dict):
                                data = files_info['data']
                                if 'space_info' in data:
                                    print(f"✅ 从文件列表data获取space_info: {data['space_info']}")
                                    return data['space_info']
                except Exception as e:
                    print(f"⚠️ 通过文件列表获取空间信息失败: {e}")

                # 方法4: 尝试通过其他可能的方法
                try:
                    # 检查p115_client有哪些可用的方法
                    available_methods = [method for method in dir(self.p115_client)
                                       if not method.startswith('_') and 'space' in method.lower()]
                    print(f"🔍 可用的空间相关方法: {available_methods}")

                    # 尝试其他可能的方法
                    for method_name in ['get_space', 'space', 'storage_info', 'disk_info']:
                        if hasattr(self.p115_client, method_name):
                            try:
                                method = getattr(self.p115_client, method_name)
                                result = method()
                                if result:
                                    print(f"✅ 通过{method_name}获取: {result}")
                                    return result
                            except Exception as e:
                                print(f"⚠️ {method_name}方法失败: {e}")

                except Exception as e:
                    print(f"⚠️ 检查其他方法失败: {e}")

        except Exception as e:
            print(f"❌ 获取存储空间信息异常: {e}")

        print("⚠️ 所有存储空间获取方法都失败了，尝试直接调用Web API...")
        return self._get_space_info_via_web_api()

    def _get_space_info_via_web_api(self):
        """通过Web API直接获取存储空间信息"""
        try:
            import requests
            from pathlib import Path

            # 读取cookies
            cookies_file = Path("bg_upload/115_cookies.txt")
            if not cookies_file.exists():
                print("❌ cookies文件不存在")
                return None

            with open(cookies_file, 'r', encoding='utf-8') as f:
                cookies_str = f.read().strip()

            # 解析cookies
            cookies_dict = {}
            for item in cookies_str.split(';'):
                if '=' in item:
                    key, value = item.strip().split('=', 1)
                    cookies_dict[key.strip()] = value.strip()

            # 创建session
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })

            # 设置cookies
            for key, value in cookies_dict.items():
                session.cookies.set(key, value)

            # 尝试多个可能的存储空间API端点
            space_api_urls = [
                "https://webapi.115.com/user/space_info",
                "https://webapi.115.com/user/quota",
                "https://webapi.115.com/user/storage",
                "https://webapi.115.com/files?aid=1&cid=0&o=user_ptime&asc=0&offset=0&show_dir=1&limit=1",
                "https://115.com/web/1.0/user/space",
                "https://proapi.115.com/user/space"
            ]

            for url in space_api_urls:
                try:
                    print(f"🔍 尝试存储空间API: {url}")
                    response = session.get(url, timeout=10)

                    if response.status_code == 200:
                        try:
                            data = response.json()
                            print(f"✅ 获取到响应: {data}")

                            # 检查响应中是否包含存储空间信息
                            if isinstance(data, dict):
                                # 直接检查顶级字段
                                space_fields = ['space_used', 'space_total', 'used_space', 'total_space',
                                              'used', 'total', 'quota_used', 'quota_total']

                                space_info = {}
                                for field in space_fields:
                                    if field in data and data[field]:
                                        space_info[field] = data[field]

                                if space_info:
                                    print(f"✅ 找到存储空间信息: {space_info}")
                                    return space_info

                                # 检查嵌套的data字段
                                if 'data' in data and isinstance(data['data'], dict):
                                    nested_data = data['data']
                                    for field in space_fields:
                                        if field in nested_data and nested_data[field]:
                                            space_info[field] = nested_data[field]

                                    if space_info:
                                        print(f"✅ 从嵌套data找到存储空间信息: {space_info}")
                                        return space_info

                                # 检查是否有其他可能的空间信息字段
                                if 'count' in data:
                                    count_data = data['count']
                                    if isinstance(count_data, dict):
                                        for field in space_fields:
                                            if field in count_data and count_data[field]:
                                                space_info[field] = count_data[field]

                                        if space_info:
                                            print(f"✅ 从count字段找到存储空间信息: {space_info}")
                                            return space_info

                        except Exception as e:
                            print(f"⚠️ 解析响应失败: {e}")
                            print(f"响应内容: {response.text[:200]}...")
                    else:
                        print(f"❌ API请求失败: {response.status_code}")

                except Exception as e:
                    print(f"❌ 请求异常: {e}")

            print("⚠️ 所有存储空间API都无法获取到有效数据")
            return None

        except Exception as e:
            print(f"❌ Web API获取存储空间信息异常: {e}")
            return None

    def _format_size(self, size_bytes):
        """格式化文件大小"""
        try:
            if size_bytes == 0:
                return "0B"

            size_names = ["B", "KB", "MB", "GB", "TB"]
            i = 0
            while size_bytes >= 1024 and i < len(size_names) - 1:
                size_bytes /= 1024.0
                i += 1

            return f"{size_bytes:.1f}{size_names[i]}"
        except:
            return "未知"

    def logout(self):
        """退出登录"""
        try:
            if os.path.exists(self.config_file):
                os.remove(self.config_file)
            if os.path.exists(self.cookies_file):
                os.remove(self.cookies_file)
            self.session.cookies.clear()
            # 重置客户端
            self.p115_client = None
            self.p115_open_client = None
            return True
        except Exception as e:
            print(f"退出登录失败: {e}")
            return False

    def force_relogin(self):
        """强制重新登录 - 清理所有登录状态"""
        try:
            print("🔄 开始强制重新登录...")

            # 清理所有相关文件
            files_to_clean = [self.config_file, self.cookies_file]
            for file_path in files_to_clean:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"✅ 已清理文件: {file_path}")

            # 清理session
            self.session.cookies.clear()

            # 重置客户端
            self.p115_client = None
            self.p115_open_client = None

            print("✅ 强制重新登录完成，请使用cookies重新登录")
            return {'success': True, 'message': '已清理所有登录状态，请重新登录'}

        except Exception as e:
            error_msg = f"强制重新登录失败: {e}"
            print(f"❌ {error_msg}")
            return {'success': False, 'error': error_msg}

class Cloud115FileManager:
    """115云盘文件管理类"""

    def __init__(self, auth):
        self.auth = auth
        self.base_url = "https://webapi.115.com"

    def _get_file_type(self, filename):
        """根据文件名获取文件类型"""
        if not filename:
            return 'unknown'

        ext = filename.lower().split('.')[-1] if '.' in filename else ''

        video_exts = ['mp4', 'mkv', 'avi', 'mov', 'wmv', 'flv', 'webm', 'm4v']
        image_exts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
        audio_exts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg']
        document_exts = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'xls', 'xlsx', 'ppt', 'pptx']
        archive_exts = ['zip', 'rar', '7z', 'tar', 'gz', 'bz2']

        if ext in video_exts:
            return 'video'
        elif ext in image_exts:
            return 'image'
        elif ext in audio_exts:
            return 'audio'
        elif ext in document_exts:
            return 'document'
        elif ext in archive_exts:
            return 'archive'
        else:
            return 'file'

    def _format_time(self, timestamp):
        """格式化时间戳"""
        try:
            if isinstance(timestamp, (int, float)):
                from datetime import datetime
                return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(timestamp, str):
                return timestamp
            else:
                return '未知时间'
        except:
            return '未知时间'

    def list_files(self, folder_id="0", page=1, limit=50):
        """获取文件列表"""
        try:
            if not self.auth.is_logged_in():
                return {'success': False, 'error': '未登录'}

            # 优先使用p115client
            if hasattr(self.auth, 'p115_client') and self.auth.p115_client:
                try:
                    # 使用p115client获取真实文件列表
                    client = self.auth.p115_client

                    # 获取文件列表
                    files_data = client.fs_files(folder_id, limit=limit, offset=(page-1)*limit)

                    if files_data and 'data' in files_data:
                        files = []
                        for item in files_data['data']:
                            # 转换为统一格式
                            file_info = {
                                'file_id': str(item.get('fid', item.get('cid', ''))),
                                'name': item.get('n', item.get('name', '')),
                                'size': int(item.get('s', item.get('size', 0))),
                                'type': 'folder' if item.get('ico') == 'folder' else self._get_file_type(item.get('n', '')),
                                'created_time': self._format_time(item.get('t', item.get('time', ''))),
                                'is_folder': item.get('ico') == 'folder'
                            }
                            files.append(file_info)

                        return {
                            'success': True,
                            'data': {
                                'files': files,
                                'total': files_data.get('count', len(files)),
                                'page': page,
                                'limit': limit,
                                'is_real_data': True
                            }
                        }
                    else:
                        print(f"p115client返回数据格式异常: {files_data}")

                except Exception as e:
                    print(f"使用p115client获取文件列表失败: {e}")
                    # 继续使用模拟数据

            # 回退到模拟文件列表数据
            mock_files = [
                {
                    'file_id': '001',
                    'name': 'CAWD-849.mp4',
                    'size': 1024*1024*500,  # 500MB
                    'type': 'video',
                    'created_time': '2024-01-01 12:00:00',
                    'is_folder': False
                },
                {
                    'file_id': '002',
                    'name': 'IPX-123.mkv',
                    'size': 1024*1024*800,  # 800MB
                    'type': 'video',
                    'created_time': '2024-01-02 12:00:00',
                    'is_folder': False
                },
                {
                    'file_id': '003',
                    'name': 'SSIS-456.mp4',
                    'size': 1024*1024*650,  # 650MB
                    'type': 'video',
                    'created_time': '2024-01-03 15:30:00',
                    'is_folder': False
                },
                {
                    'file_id': '004',
                    'name': 'MIDV-789.mkv',
                    'size': 1024*1024*720,  # 720MB
                    'type': 'video',
                    'created_time': '2024-01-04 09:15:00',
                    'is_folder': False
                },
                {
                    'file_id': '005',
                    'name': 'FSDSS-321.mp4',
                    'size': 1024*1024*580,  # 580MB
                    'type': 'video',
                    'created_time': '2024-01-05 14:20:00',
                    'is_folder': False
                },
                {
                    'file_id': '006',
                    'name': 'JAV高清合集',
                    'size': 0,
                    'type': 'folder',
                    'created_time': '2024-01-01 10:00:00',
                    'is_folder': True
                },
                {
                    'file_id': '007',
                    'name': '2024新片',
                    'size': 0,
                    'type': 'folder',
                    'created_time': '2024-01-01 11:00:00',
                    'is_folder': True
                },
                {
                    'file_id': '008',
                    'name': 'CAWD-849-poster.jpg',
                    'size': 1024*150,  # 150KB
                    'type': 'image',
                    'created_time': '2024-01-01 12:01:00',
                    'is_folder': False
                },
                {
                    'file_id': '009',
                    'name': 'IPX-123.nfo',
                    'size': 1024*2,  # 2KB
                    'type': 'metadata',
                    'created_time': '2024-01-02 12:01:00',
                    'is_folder': False
                }
            ]

            return {
                'success': True,
                'data': {
                    'files': mock_files,
                    'total': len(mock_files),
                    'page': page,
                    'limit': limit,
                    'is_real_data': False
                }
            }

        except Exception as e:
            return {'success': False, 'error': f'获取文件列表失败: {str(e)}'}

    def create_folder(self, parent_id, folder_name):
        """创建文件夹"""
        try:
            if not self.auth.is_logged_in():
                return {'success': False, 'error': '未登录'}

            # 模拟创建文件夹
            new_folder = {
                'file_id': str(uuid.uuid4()),
                'name': folder_name,
                'parent_id': parent_id,
                'created_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return {'success': True, 'data': new_folder}

        except Exception as e:
            return {'success': False, 'error': f'创建文件夹失败: {str(e)}'}

    def upload_file(self, file_path, parent_id="0"):
        """上传文件"""
        try:
            if not self.auth.is_logged_in():
                return {'success': False, 'error': '未登录'}

            if not os.path.exists(file_path):
                return {'success': False, 'error': '文件不存在'}

            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)

            # 模拟上传过程
            return {
                'success': True,
                'data': {
                    'file_id': str(uuid.uuid4()),
                    'name': file_name,
                    'size': file_size,
                    'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }

        except Exception as e:
            return {'success': False, 'error': f'上传文件失败: {str(e)}'}

    def download_file(self, file_id, save_path):
        """下载文件"""
        try:
            if not self.auth.is_logged_in():
                return {'success': False, 'error': '未登录'}

            # 模拟下载过程
            return {
                'success': True,
                'data': {
                    'file_id': file_id,
                    'save_path': save_path,
                    'download_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }

        except Exception as e:
            return {'success': False, 'error': f'下载文件失败: {str(e)}'}

    def get_download_url(self, file_id):
        """获取文件下载链接"""
        try:
            if not self.auth.is_logged_in():
                return {'success': False, 'error': '未登录'}

            # 优先使用p115client获取真实下载链接
            if hasattr(self.auth, 'p115_client') and self.auth.p115_client:
                try:
                    client = self.auth.p115_client
                    # 尝试获取下载链接
                    if hasattr(client, 'download_url'):
                        download_info = client.download_url(file_id)
                        if download_info and isinstance(download_info, dict):
                            if 'data' in download_info and download_info['data']:
                                download_url = download_info['data'].get('url', '')
                                if download_url:
                                    return {
                                        'success': True,
                                        'data': {
                                            'download_url': download_url,
                                            'expire_time': (datetime.now().timestamp() + 3600),
                                            'source': 'p115client'
                                        }
                                    }

                    # 尝试其他可能的下载方法
                    if hasattr(client, 'get_download_url'):
                        download_url = client.get_download_url(file_id)
                        if download_url:
                            return {
                                'success': True,
                                'data': {
                                    'download_url': download_url,
                                    'expire_time': (datetime.now().timestamp() + 3600),
                                    'source': 'p115client_alt'
                                }
                            }

                except Exception as e:
                    print(f"⚠️ p115client获取下载链接失败: {e}")

            # 模拟获取下载链接（备用方案）
            download_url = f"https://download.115.com/file/{file_id}"

            return {
                'success': True,
                'data': {
                    'download_url': download_url,
                    'expire_time': (datetime.now().timestamp() + 3600),  # 1小时后过期
                    'source': 'mock'
                }
            }

        except Exception as e:
            return {'success': False, 'error': f'获取下载链接失败: {str(e)}'}

class Cloud115MetadataDownloader:
    """115云盘元数据下载管理器"""

    def __init__(self, auth, file_manager):
        self.auth = auth
        self.file_manager = file_manager

        # 支持的元数据文件扩展名（参考move_and_generate_strm.py）
        self.META_EXTS = ['.jpg', '.png', '.nfo', '.srt', '.ass', '.sub', '.jpeg', '.webp', '.txt']
        # 支持的视频文件扩展名
        self.VIDEO_EXTS = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v']

    def scan_metadata_files(self, folder_id="0", recursive=True, max_depth=10):
        """扫描指定文件夹中的元数据文件"""
        try:
            if not self.auth.is_logged_in():
                return {'success': False, 'error': '未登录'}

            metadata_files = []

            if recursive:
                # 递归扫描
                result = self._scan_metadata_recursive(folder_id, "", metadata_files, max_depth)
                if not result['success']:
                    return result
            else:
                # 单层扫描
                result = self.file_manager.list_files(folder_id)
                if not result['success']:
                    return result

                for file_info in result['data']['files']:
                    if not file_info['is_folder']:
                        file_ext = os.path.splitext(file_info['name'])[1].lower()
                        if file_ext in self.META_EXTS:
                            file_info['path'] = ""
                            file_info['metadata_type'] = self._get_metadata_type(file_ext)
                            metadata_files.append(file_info)

            return {
                'success': True,
                'data': {
                    'metadata_files': metadata_files,
                    'total_count': len(metadata_files)
                }
            }

        except Exception as e:
            return {'success': False, 'error': f'扫描元数据文件失败: {str(e)}'}

    def _scan_metadata_recursive(self, folder_id, current_path, metadata_files, max_depth):
        """递归扫描元数据文件"""
        try:
            if max_depth <= 0:
                return {'success': True}

            result = self.file_manager.list_files(folder_id)
            if not result['success']:
                return result

            folders = []

            # 处理当前文件夹中的文件
            for file_info in result['data']['files']:
                if file_info['is_folder']:
                    folders.append(file_info)
                else:
                    file_ext = os.path.splitext(file_info['name'])[1].lower()
                    if file_ext in self.META_EXTS:
                        file_info['path'] = current_path
                        file_info['metadata_type'] = self._get_metadata_type(file_ext)
                        metadata_files.append(file_info)

            # 递归处理子文件夹
            for folder in folders:
                folder_path = os.path.join(current_path, folder['name']) if current_path else folder['name']
                sub_result = self._scan_metadata_recursive(
                    folder['file_id'],
                    folder_path,
                    metadata_files,
                    max_depth - 1
                )
                if not sub_result['success']:
                    return sub_result

            return {'success': True}

        except Exception as e:
            return {'success': False, 'error': f'递归扫描失败: {str(e)}'}

    def _get_metadata_type(self, file_ext):
        """根据文件扩展名确定元数据类型"""
        type_mapping = {
            '.jpg': 'poster', '.jpeg': 'poster', '.png': 'poster', '.webp': 'poster',
            '.nfo': 'info',
            '.srt': 'subtitle', '.ass': 'subtitle', '.sub': 'subtitle',
            '.txt': 'description'
        }
        return type_mapping.get(file_ext.lower(), 'other')

    def download_metadata_with_structure(self, metadata_files, local_base_dir, preserve_structure=True):
        """下载元数据文件并保持目录结构"""
        try:
            if not metadata_files:
                return {'success': False, 'error': '没有要下载的元数据文件'}

            # 确保本地基础目录存在
            os.makedirs(local_base_dir, exist_ok=True)

            download_results = []
            success_count = 0
            failed_count = 0

            for file_info in metadata_files:
                try:
                    # 确定本地保存路径
                    if preserve_structure and file_info.get('path'):
                        local_dir = os.path.join(local_base_dir, file_info['path'])
                    else:
                        local_dir = local_base_dir

                    os.makedirs(local_dir, exist_ok=True)
                    local_file_path = os.path.join(local_dir, file_info['name'])

                    # 获取下载链接
                    url_result = self.file_manager.get_download_url(file_info['file_id'])
                    if not url_result['success']:
                        download_results.append({
                            'file_name': file_info['name'],
                            'file_path': file_info.get('path', ''),
                            'status': 'failed',
                            'error': url_result['error']
                        })
                        failed_count += 1
                        continue

                    download_url = url_result['data']['download_url']

                    # 下载文件
                    download_result = self._download_file_from_url(download_url, local_file_path)

                    if download_result['success']:
                        download_results.append({
                            'file_name': file_info['name'],
                            'file_path': file_info.get('path', ''),
                            'local_path': local_file_path,
                            'metadata_type': file_info.get('metadata_type', 'other'),
                            'size': file_info.get('size', 0),
                            'status': 'success'
                        })
                        success_count += 1
                    else:
                        download_results.append({
                            'file_name': file_info['name'],
                            'file_path': file_info.get('path', ''),
                            'status': 'failed',
                            'error': download_result['error']
                        })
                        failed_count += 1

                except Exception as e:
                    download_results.append({
                        'file_name': file_info['name'],
                        'file_path': file_info.get('path', ''),
                        'status': 'failed',
                        'error': str(e)
                    })
                    failed_count += 1

            return {
                'success': True,
                'data': {
                    'download_results': download_results,
                    'summary': {
                        'total_files': len(metadata_files),
                        'success_count': success_count,
                        'failed_count': failed_count,
                        'local_base_dir': local_base_dir
                    }
                }
            }

        except Exception as e:
            return {'success': False, 'error': f'下载元数据文件失败: {str(e)}'}

    def _download_file_from_url(self, download_url, local_file_path):
        """从URL下载文件到本地"""
        try:
            import requests
            from urllib.parse import urlparse

            # 发送下载请求
            response = requests.get(download_url, stream=True, timeout=30)
            response.raise_for_status()

            # 保存文件
            with open(local_file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            # 验证文件是否下载成功
            if os.path.exists(local_file_path) and os.path.getsize(local_file_path) > 0:
                return {
                    'success': True,
                    'data': {
                        'local_path': local_file_path,
                        'file_size': os.path.getsize(local_file_path)
                    }
                }
            else:
                return {'success': False, 'error': '文件下载后验证失败'}

        except requests.exceptions.RequestException as e:
            return {'success': False, 'error': f'网络请求失败: {str(e)}'}
        except Exception as e:
            return {'success': False, 'error': f'下载文件失败: {str(e)}'}

class RealTimeMonitorManager:
    """实时监控管理器"""

    def __init__(self):
        self.config_file = "monitor_config.json"
        self.default_config = {
            "cd2_monitor": {
                "enabled": False,
                "source_dir": "/vol1/1000/CloudDrive/115open/R+18/JAV/更新/",
                "strm_dir": "/vol1/1000/媒体库/115/R+18/JAV/",
                "library_dir": "/vol1/1000/CloudDrive/115open/R+18/已整理/更新",
                "cloud_url": "http://192.168.1.2:5244/d/115/R+18/已整理/更新",
                "docker_dir": "/Media/115/R+18/JAV/",
                "scan_interval": 5,
                "max_workers": 8
            },
            "cloud115_monitor": {
                "enabled": False,
                "folder_id": "0",
                "scan_interval": 30,
                "auto_download": False,
                "download_types": ["video", "metadata"],
                "local_sync_dir": "./sync/115/"
            },
            "storage_monitor": {
                "enabled": False,
                "scan_interval": 60,
                "auto_organize": False,
                "organize_rules": {
                    "by_date": True,
                    "by_type": True,
                    "by_size": False
                }
            },
            "notifications": {
                "telegram": {
                    "enabled": False,
                    "bot_token": "",
                    "chat_id": "",
                    "message_template": "📁 新增媒体：\n{media_list}\n\n✅ 处理完成"
                },
                "emby": {
                    "enabled": False,
                    "server_url": "http://192.168.1.2:8096",
                    "api_key": "",
                    "auto_refresh": True
                }
            }
        }
        self.monitor_status = {
            "cd2_monitor": {"running": False, "last_scan": None, "files_processed": 0},
            "cloud115_monitor": {"running": False, "last_scan": None, "files_synced": 0},
            "storage_monitor": {"running": False, "last_scan": None, "folders_organized": 0}
        }

    def load_config(self):
        """加载监控配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return self._merge_config(config, self.default_config)
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"❌ 加载监控配置失败: {e}")
            return self.default_config.copy()

    def save_config(self, config):
        """保存监控配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"❌ 保存监控配置失败: {e}")
            return False

    def _merge_config(self, user_config, default_config):
        """合并用户配置和默认配置"""
        merged = default_config.copy()
        for key, value in user_config.items():
            if key in merged:
                if isinstance(value, dict) and isinstance(merged[key], dict):
                    merged[key].update(value)
                else:
                    merged[key] = value
            else:
                merged[key] = value
        return merged

    def start_cd2_monitor(self, config):
        """启动CD2实时监控"""
        try:
            # 这里实现CD2监控逻辑，基于av_storage.py的monitor_folder函数
            self.monitor_status["cd2_monitor"]["running"] = True
            self.monitor_status["cd2_monitor"]["last_scan"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            return {"success": True, "message": "CD2监控已启动"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def stop_cd2_monitor(self):
        """停止CD2实时监控"""
        try:
            self.monitor_status["cd2_monitor"]["running"] = False
            return {"success": True, "message": "CD2监控已停止"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def start_115_monitor(self, config, p115_client=None):
        """启动115云盘实时监控"""
        try:
            if not p115_client:
                return {"success": False, "error": "115客户端未初始化"}

            self.monitor_status["cloud115_monitor"]["running"] = True
            self.monitor_status["cloud115_monitor"]["last_scan"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            return {"success": True, "message": "115监控已启动"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def stop_115_monitor(self):
        """停止115云盘实时监控"""
        try:
            self.monitor_status["cloud115_monitor"]["running"] = False
            return {"success": True, "message": "115监控已停止"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def start_storage_monitor(self, config):
        """启动网盘整理监控"""
        try:
            self.monitor_status["storage_monitor"]["running"] = True
            self.monitor_status["storage_monitor"]["last_scan"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            return {"success": True, "message": "网盘整理监控已启动"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def stop_storage_monitor(self):
        """停止网盘整理监控"""
        try:
            self.monitor_status["storage_monitor"]["running"] = False
            return {"success": True, "message": "网盘整理监控已停止"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_monitor_status(self):
        """获取所有监控状态"""
        return self.monitor_status.copy()

    def send_telegram_notification(self, message, config):
        """发送Telegram通知"""
        try:
            if not config["notifications"]["telegram"]["enabled"]:
                return {"success": False, "error": "Telegram通知未启用"}

            bot_token = config["notifications"]["telegram"]["bot_token"]
            chat_id = config["notifications"]["telegram"]["chat_id"]

            if not bot_token or not chat_id:
                return {"success": False, "error": "Telegram配置不完整"}

            import requests
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage'

            # 分割长消息
            max_length = 4000
            for i in range(0, len(message), max_length):
                chunk = message[i:i + max_length]
                payload = {
                    'chat_id': chat_id,
                    'text': chunk
                }

                response = requests.post(url, data=payload, timeout=10)
                if response.status_code != 200:
                    return {"success": False, "error": f"发送失败: {response.text}"}

                time.sleep(1)  # 防止被限流

            return {"success": True, "message": "Telegram通知发送成功"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def refresh_emby_library(self, config):
        """刷新Emby媒体库"""
        try:
            if not config["notifications"]["emby"]["enabled"]:
                return {"success": False, "error": "Emby刷新未启用"}

            server_url = config["notifications"]["emby"]["server_url"]
            api_key = config["notifications"]["emby"]["api_key"]

            if not server_url or not api_key:
                return {"success": False, "error": "Emby配置不完整"}

            import requests

            # 获取媒体库列表
            url = f"{server_url}/Items"
            headers = {
                "X-Emby-Token": api_key,
                "Content-Type": "application/json"
            }
            params = {
                "Recursive": True,
                "Fields": "Path",
                "IncludeItemTypes": "Folder"
            }

            response = requests.get(url, headers=headers, params=params)
            if response.status_code != 200:
                return {"success": False, "error": f"获取媒体库失败: {response.text}"}

            data = response.json()
            refreshed_count = 0

            # 刷新每个媒体库
            for item in data.get("Items", []):
                item_id = item.get("Id")
                refresh_url = f"{server_url}/Items/{item_id}/Refresh"
                refresh_params = {"Recursive": True}

                refresh_response = requests.post(refresh_url, headers=headers, params=refresh_params)
                if refresh_response.status_code == 204:
                    refreshed_count += 1

                time.sleep(2)  # 避免请求过快

            return {"success": True, "message": f"成功刷新 {refreshed_count} 个媒体库"}
        except Exception as e:
            return {"success": False, "error": str(e)}

class STRMConfigManager:
    """STRM配置管理器"""

    def __init__(self, config_file="strm_config.json"):
        self.config_file = config_file
        self.default_config = {
            "paths": {
                "source_dir": "/vol1/1000/CloudDrive/115open/R+18/已整理/更新/",
                "strm_dir": "/vol1/1000/媒体库/115/R+18/JAV/",
                "url_prefix": "http://192.168.1.2:5244/d/115/R+18/已整理/更新",
                "download_dir": "./downloads/metadata/"
            },
            "file_formats": {
                "video_exts": [".mp4", ".mkv", ".avi", ".mov", ".wmv", ".flv", ".webm", ".m4v"],
                "meta_exts": [".jpg", ".png", ".nfo", ".srt", ".ass", ".sub", ".jpeg", ".webp", ".txt"]
            },
            "scan_settings": {
                "max_depth": 10,
                "recursive_scan": True,
                "preserve_structure": True
            },
            "advanced": {
                "concurrent_downloads": 3,
                "timeout_seconds": 30,
                "retry_attempts": 3
            }
        }

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保所有字段都存在
                return self._merge_config(config, self.default_config)
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"❌ 加载配置失败: {e}")
            return self.default_config.copy()

    def save_config(self, config):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            return False

    def _merge_config(self, user_config, default_config):
        """合并用户配置和默认配置"""
        merged = default_config.copy()
        for key, value in user_config.items():
            if key in merged:
                if isinstance(value, dict) and isinstance(merged[key], dict):
                    merged[key].update(value)
                else:
                    merged[key] = value
            else:
                merged[key] = value
        return merged

    def get_path(self, path_key):
        """获取路径配置"""
        config = self.load_config()
        return config["paths"].get(path_key, "")

    def get_video_exts(self):
        """获取支持的视频格式"""
        config = self.load_config()
        return config["file_formats"]["video_exts"]

    def get_meta_exts(self):
        """获取支持的元数据格式"""
        config = self.load_config()
        return config["file_formats"]["meta_exts"]

    def get_scan_settings(self):
        """获取扫描设置"""
        config = self.load_config()
        return config["scan_settings"]

    def get_advanced_settings(self):
        """获取高级设置"""
        config = self.load_config()
        return config["advanced"]

class Cloud115STRMManager:
    """115云盘STRM管理类 - 增强版"""

    def __init__(self, auth, file_manager):
        self.auth = auth
        self.file_manager = file_manager

    def scan_cloud_videos_recursive(self, folder_id="0", current_path="", max_depth=10):
        """递归扫描云端视频文件，保持文件夹结构"""
        try:
            if max_depth <= 0:
                return {'success': True, 'data': []}

            result = self.file_manager.list_files(folder_id)
            if not result['success']:
                return result

            video_files = []
            folders = []

            # 分离文件和文件夹
            for file_info in result['data']['files']:
                file_info['path'] = current_path  # 添加路径信息

                if file_info['is_folder']:
                    folders.append(file_info)
                else:
                    file_ext = os.path.splitext(file_info['name'])[1].lower()
                    if file_ext in VIDEO_EXTS:
                        video_files.append(file_info)

            # 递归处理子文件夹
            for folder in folders:
                folder_path = os.path.join(current_path, folder['name']) if current_path else folder['name']
                sub_result = self.scan_cloud_videos_recursive(
                    folder['file_id'],
                    folder_path,
                    max_depth - 1
                )
                if sub_result['success']:
                    video_files.extend(sub_result['data'])

            return {'success': True, 'data': video_files}

        except Exception as e:
            return {'success': False, 'error': f'递归扫描视频文件失败: {str(e)}'}

    def scan_cloud_videos(self, folder_id="0"):
        """扫描云端视频文件（单层）"""
        try:
            result = self.file_manager.list_files(folder_id)
            if not result['success']:
                return result

            video_files = []
            for file_info in result['data']['files']:
                if not file_info['is_folder']:
                    file_ext = os.path.splitext(file_info['name'])[1].lower()
                    if file_ext in VIDEO_EXTS:
                        file_info['path'] = ""  # 单层扫描，路径为空
                        video_files.append(file_info)

            return {'success': True, 'data': video_files}

        except Exception as e:
            return {'success': False, 'error': f'扫描视频文件失败: {str(e)}'}

    def generate_cloud_strm_with_structure(self, video_files, strm_base_dir, url_prefix="", use_direct_url=True):
        """为云端视频生成带文件夹结构的STRM文件"""
        try:
            results = []
            ensure_dir(strm_base_dir)

            for video in video_files:
                try:
                    # 构建STRM文件路径（保持文件夹结构）
                    video_path = video.get('path', '')
                    if video_path:
                        strm_dir = os.path.join(strm_base_dir, video_path)
                        ensure_dir(strm_dir)
                    else:
                        strm_dir = strm_base_dir

                    strm_name = os.path.splitext(video['name'])[0] + '.strm'
                    strm_path = os.path.join(strm_dir, strm_name)

                    # 获取STRM内容
                    if use_direct_url:
                        # 使用115直链
                        url_result = self.file_manager.get_download_url(video['file_id'])
                        if url_result['success']:
                            strm_content = url_result['data']['download_url']
                        else:
                            results.append(f"❌ 获取直链失败: {video['name']} - {url_result['error']}")
                            continue
                    else:
                        # 使用自定义URL前缀
                        if not url_prefix:
                            results.append(f"❌ 未设置URL前缀: {video['name']}")
                            continue

                        # 构建相对路径
                        if video_path:
                            relative_path = f"{video_path}/{video['name']}".replace("\\", "/")
                        else:
                            relative_path = video['name']

                        strm_content = f"{url_prefix.rstrip('/')}/{relative_path}"

                    # 创建STRM文件
                    with open(strm_path, 'w', encoding='utf-8') as f:
                        f.write(strm_content)

                    results.append(f"✅ 创建: {strm_path}")

                except Exception as e:
                    results.append(f"❌ 处理失败: {video['name']} - {str(e)}")

            return results

        except Exception as e:
            return [f"❌ 生成STRM失败: {str(e)}"]

    def generate_cloud_strm(self, video_files, strm_dir):
        """为云端视频生成STRM文件（兼容旧接口）"""
        return self.generate_cloud_strm_with_structure(video_files, strm_dir, use_direct_url=True)



if "events" not in st.session_state:
    st.session_state.events = []  # [{type, time, status, detail, progress, result}]

def add_event(event_type, status, detail="", progress=0, result=None):
    import datetime
    st.session_state.events.append({
        "type": event_type,
        "time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "status": status,  # 运行中、完成、失败
        "detail": detail,
        "progress": progress,
        "result": result
    })
    # 只保留最近100条
    if len(st.session_state.events) > 100:
        st.session_state.events = st.session_state.events[-100:]

def update_last_event(status=None, progress=None, result=None):
    if st.session_state.events:
        if status is not None:
            st.session_state.events[-1]["status"] = status
        if progress is not None:
            st.session_state.events[-1]["progress"] = progress
        if result is not None:
            st.session_state.events[-1]["result"] = result

# ====== 首页统计缓存优化 ======
@st.cache_data(show_spinner=False)
def count_records(path):
    return len(os.listdir(path)) if os.path.exists(path) else 0

@st.cache_data(show_spinner=False)
def count_success(path):
    return len(os.listdir(path)) if os.path.exists(path) else 0

@st.cache_data(show_spinner=False)
def count_failed(path):
    return len(os.listdir(path)) if os.path.exists(path) else 0

@st.cache_data(show_spinner=False)
def count_actors(file):
    data = load_json(file)
    return len(data)

def calc_progress(success, failed):
    total = success + failed
    return "{:.2f}%".format(success / total * 100 if total else 0)

# 添加页面切换时的内容清理标记和JavaScript清理脚本
# 页面标识符（用于区分不同页面内容）
content_id = active_tab.replace(' ', '-')
st.markdown(f'<div id="content-{content_id}" style="min-height: 10px;"></div>', unsafe_allow_html=True)

# 添加基础页面样式（使用安全的CSS注入方式）
if 'css_injected' not in st.session_state:
    st.markdown("""
    <style>
    /* 基础页面优化样式 */
    .main .block-container {
        padding-top: 1rem;
        padding-bottom: 1rem;
        max-width: 1200px;
    }

    /* 标签页样式优化 */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }

    .stTabs [data-baseweb="tab"] {
        height: 50px;
        padding-left: 20px;
        padding-right: 20px;
        border-radius: 8px 8px 0 0;
    }

    /* 隐藏不必要的元素 */
    .stDeployButton {
        display: none;
    }

    /* 优化按钮样式 */
    .stButton > button {
        border-radius: 6px;
        border: 1px solid #ddd;
        transition: all 0.2s ease;
    }

    .stButton > button:hover {
        border-color: #ff512f;
        color: #ff512f;
    }

    /* 激活状态动画效果 */
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    @keyframes glow {
        0% { box-shadow: 0 0 5px rgba(255, 81, 47, 0.5); }
        50% { box-shadow: 0 0 20px rgba(255, 81, 47, 0.8); }
        100% { box-shadow: 0 0 5px rgba(255, 81, 47, 0.5); }
    }

    /* 试用版进度条样式 */
    .trial-progress {
        background: linear-gradient(90deg, #28a745, #20c997);
        border-radius: 10px;
        height: 8px;
        transition: all 0.3s ease;
    }

    /* 激活卡片样式 */
    .activation-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .activation-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    /* 隐藏标题后的锚点链接图标 */
    .element-container h1 .stMarkdown a,
    .element-container h2 .stMarkdown a,
    .element-container h3 .stMarkdown a,
    .element-container h4 .stMarkdown a,
    .element-container h5 .stMarkdown a,
    .element-container h6 .stMarkdown a {
        display: none !important;
    }

    /* 更精确的选择器隐藏锚点链接 */
    [data-testid="stMarkdownContainer"] h1 a[href^="#"],
    [data-testid="stMarkdownContainer"] h2 a[href^="#"],
    [data-testid="stMarkdownContainer"] h3 a[href^="#"],
    [data-testid="stMarkdownContainer"] h4 a[href^="#"],
    [data-testid="stMarkdownContainer"] h5 a[href^="#"],
    [data-testid="stMarkdownContainer"] h6 a[href^="#"] {
        display: none !important;
    }

    /* Streamlit界面中文化 */
    /* 右上角菜单中文化 */
    [data-testid="stAppViewContainer"] [data-testid="stHeader"] button[title="Settings"] {
        position: relative;
    }

    [data-testid="stAppViewContainer"] [data-testid="stHeader"] button[title="Settings"]:after {
        content: "设置";
        position: absolute;
        top: -25px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s;
    }

    [data-testid="stAppViewContainer"] [data-testid="stHeader"] button[title="Settings"]:hover:after {
        opacity: 1;
    }

    /* 侧边栏中文化 */
    [data-testid="stSidebar"] .css-1d391kg {
        font-family: "Microsoft YaHei", "SimHei", sans-serif;
    }

    /* 主内容区域中文字体 */
    .stApp {
        font-family: "Microsoft YaHei", "SimHei", "PingFang SC", "Hiragino Sans GB", sans-serif;
    }

    /* 按钮中文字体 */
    .stButton button {
        font-family: "Microsoft YaHei", "SimHei", sans-serif;
    }

    /* 输入框中文字体 */
    .stTextInput input, .stTextArea textarea {
        font-family: "Microsoft YaHei", "SimHei", sans-serif;
    }

    /* 强制中文化右上角菜单 - 使用CSS伪元素覆盖 */
    [data-testid="stHeader"] [role="menu"] [role="menuitem"] {
        position: relative;
        font-family: "Microsoft YaHei", "SimHei", sans-serif;
    }

    /* 隐藏原始英文文本并显示中文 */
    [data-testid="stHeader"] [role="menu"] [role="menuitem"]:first-child {
        font-size: 0;
    }
    [data-testid="stHeader"] [role="menu"] [role="menuitem"]:first-child::before {
        content: "重新运行";
        font-size: 14px;
        font-family: "Microsoft YaHei", "SimHei", sans-serif;
    }

    [data-testid="stHeader"] [role="menu"] [role="menuitem"]:nth-child(2) {
        font-size: 0;
    }
    [data-testid="stHeader"] [role="menu"] [role="menuitem"]:nth-child(2)::before {
        content: "设置";
        font-size: 14px;
        font-family: "Microsoft YaHei", "SimHei", sans-serif;
    }

    [data-testid="stHeader"] [role="menu"] [role="menuitem"]:nth-child(3) {
        font-size: 0;
    }
    [data-testid="stHeader"] [role="menu"] [role="menuitem"]:nth-child(3)::before {
        content: "打印";
        font-size: 14px;
        font-family: "Microsoft YaHei", "SimHei", sans-serif;
    }

    /* 通用菜单项中文化 */
    [role="menu"] [role="menuitem"] {
        font-family: "Microsoft YaHei", "SimHei", sans-serif !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # 添加强化版JavaScript来中文化界面元素
    st.markdown("""
    <script>
    // 强化版中文化界面元素
    function chineseUI() {
        try {
            // 右上角菜单中文化 - 多种选择器尝试
            const menuSelectors = [
                '[data-testid="stHeader"] [role="menu"] [role="menuitem"]',
                '[data-testid="stHeader"] .css-1dp5vir [role="menuitem"]',
                '[data-testid="stHeader"] [data-baseweb="menu"] [role="option"]',
                '[data-testid="stHeader"] [data-baseweb="menu"] li',
                '[data-testid="stHeader"] ul li',
                'header [role="menu"] [role="menuitem"]',
                'header [role="menu"] li'
            ];

            menuSelectors.forEach(selector => {
                const menuItems = document.querySelectorAll(selector);
                menuItems.forEach(item => {
                    const text = item.textContent.trim();
                    if (text === 'Rerun' || text === 'R') {
                        item.textContent = '重新运行';
                    } else if (text === 'Settings') {
                        item.textContent = '设置';
                    } else if (text === 'Print') {
                        item.textContent = '打印';
                    } else if (text === 'Record a screencast') {
                        item.textContent = '录制屏幕';
                    } else if (text === 'Report a bug') {
                        item.textContent = '报告错误';
                    } else if (text === 'Get help') {
                        item.textContent = '获取帮助';
                    } else if (text === 'About') {
                        item.textContent = '关于';
                    }
                });
            });

            // 右上角按钮title中文化
            const settingsBtn = document.querySelector('[data-testid="stHeader"] button[title="Settings"]');
            if (settingsBtn) {
                settingsBtn.setAttribute('title', '设置');
            }

            const fullscreenBtn = document.querySelector('[data-testid="stHeader"] button[title="View fullscreen"]');
            if (fullscreenBtn) {
                fullscreenBtn.setAttribute('title', '全屏显示');
            }

            // 侧边栏折叠按钮
            const collapseBtn = document.querySelector('[data-testid="collapsedControl"]');
            if (collapseBtn) {
                collapseBtn.setAttribute('title', '展开侧边栏');
            }

            // 文件上传器中文化
            const fileUploaders = document.querySelectorAll('[data-testid="stFileUploader"] small');
            fileUploaders.forEach(uploader => {
                let text = uploader.textContent;
                if (text.includes('Drag and drop file here')) {
                    uploader.textContent = text.replace('Drag and drop file here', '拖拽文件到此处');
                }
                if (text.includes('Browse files')) {
                    uploader.textContent = text.replace('Browse files', '浏览文件');
                }
                if (text.includes('Limit')) {
                    uploader.textContent = text.replace('Limit', '限制');
                }
            });

            // 数据表格搜索框中文化
            const searchInputs = document.querySelectorAll('input[placeholder="Search"]');
            searchInputs.forEach(input => {
                input.setAttribute('placeholder', '搜索');
            });

            // 下载按钮中文化
            const downloadBtns = document.querySelectorAll('[data-testid="stDownloadButton"] button');
            downloadBtns.forEach(btn => {
                if (btn.textContent.includes('Download')) {
                    btn.textContent = btn.textContent.replace('Download', '下载');
                }
            });

            // 通用文本替换 - 更广泛的中文化
            const allElements = document.querySelectorAll('*');
            allElements.forEach(element => {
                // 只处理文本节点，避免破坏HTML结构
                if (element.childNodes.length === 1 && element.childNodes[0].nodeType === Node.TEXT_NODE) {
                    const text = element.textContent.trim();
                    if (text === 'Rerun') element.textContent = '重新运行';
                    else if (text === 'Settings') element.textContent = '设置';
                    else if (text === 'Print') element.textContent = '打印';
                    else if (text === 'Loading...') element.textContent = '加载中...';
                    else if (text === 'Running...') element.textContent = '运行中...';
                }
            });

        } catch (error) {
            console.log('中文化处理出错:', error);
        }
    }

    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', chineseUI);
    } else {
        chineseUI();
    }

    // 定期检查并更新（因为Streamlit是动态加载的）
    setInterval(chineseUI, 1000);

    // 监听DOM变化，实时中文化
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                setTimeout(chineseUI, 100);
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    </script>
    """, unsafe_allow_html=True)

    st.session_state.css_injected = True

    # 注入中文化界面
    inject_chinese_ui()

# 首页
if active_tab == "首页":
    # 优化后的首页标题和描述（包含激活状态）
    is_activated, activation_info = get_current_activation_status()

    # 强制刷新激活状态（解决缓存问题）
    if hasattr(st, 'cache_data'):
        st.cache_data.clear()

    # 动态更新浏览器标题
    update_browser_title()



    # 动态项目名称和激活状态标识
    if is_activated:
        # 激活后显示 MMA Pro
        project_name = "🎬 MMA Pro"
        project_subtitle = "多功能媒体管理系统"
        title_color = "#ff512f"

        activation_type = activation_info.get("type", "").upper()
        if activation_type == "TRIAL":
            # 试用版显示剩余时间
            try:
                if activation_info.get('expires_datetime'):
                    expire_datetime = datetime.strptime(activation_info['expires_datetime'], "%Y-%m-%d %H:%M:%S")
                else:
                    expire_date = datetime.strptime(activation_info.get('expires', ''), "%Y-%m-%d")
                    expire_datetime = expire_date.replace(hour=23, minute=59, second=59)

                time_left = expire_datetime - datetime.now()
                if time_left.total_seconds() > 0:
                    days_left = time_left.days
                    hours_left = time_left.seconds // 3600
                    if days_left > 0:
                        time_display = f"{days_left}天{hours_left}小时"
                    else:
                        time_display = f"{hours_left}小时"
                    activation_badge = f'<span style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; margin-left: 15px; box-shadow: 0 4px 12px rgba(40,167,69,0.4); animation: glow 2s ease-in-out infinite alternate;">✨ 试用版 ({time_display})</span>'
                else:
                    activation_badge = f'<span style="background: linear-gradient(45deg, #dc3545, #c82333); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; margin-left: 15px; animation: pulse 2s infinite;">⏰ 试用已过期</span>'
            except:
                activation_badge = f'<span style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; margin-left: 15px; box-shadow: 0 4px 12px rgba(40,167,69,0.4); animation: glow 2s ease-in-out infinite alternate;">✅ 试用版</span>'
        else:
            activation_badge = f'<span style="background: linear-gradient(45deg, #007bff, #0056b3); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; margin-left: 15px; box-shadow: 0 4px 12px rgba(0,123,255,0.4); animation: glow 2s ease-in-out infinite alternate;">👑 专业版</span>'
    else:
        # 未激活显示 MMA
        project_name = "🎬 MMA"
        project_subtitle = "媒体管理系统"
        title_color = "#999"
        activation_badge = '<span style="background: linear-gradient(45deg, #dc3545, #c82333); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; margin-left: 15px; animation: pulse 2s infinite;">🔒 未激活</span>'

    # 添加CSS动画和样式
    st.markdown("""
    <style>
    @keyframes glow {
        from {
            box-shadow: 0 4px 12px rgba(40,167,69,0.4);
            transform: scale(1);
        }
        to {
            box-shadow: 0 6px 20px rgba(40,167,69,0.7);
            transform: scale(1.02);
        }
    }
    @keyframes pulse {
        0% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.8; transform: scale(0.98); }
        100% { opacity: 1; transform: scale(1); }
    }
    .project-title {
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    .project-title:hover {
        transform: scale(1.02);
    }
    </style>
    """, unsafe_allow_html=True)

    st.markdown(f"""
    <div style="text-align: center; padding: 20px 0;">
        <h1 style="color: {title_color}; margin-bottom: 10px;" class="project-title">{project_name} - {project_subtitle}{activation_badge}</h1>
        <p style="font-size: 18px; color: #666; margin-bottom: 20px;">
            集成STRM管理、115云盘、实时监控等功能的综合媒体管理平台
        </p>
    </div>
    """, unsafe_allow_html=True)

    # 未激活时显示优化提示
    if not is_activated:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #ff6b6b, #ee5a24); padding: 20px; border-radius: 15px; margin: 20px 0; text-align: center; color: white; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
            <h3 style="margin: 0 0 10px 0; color: white;">🚀 立即激活，解锁完整功能！</h3>
            <p style="margin: 0; opacity: 0.9; font-size: 16px;">使用激活码 <strong>MMA-TRIAL-7DAYS</strong> 获得7天完整功能试用</p>
        </div>
        """, unsafe_allow_html=True)

        # 快速激活按钮
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🎯 立即激活 (7天试用)", type="primary", use_container_width=True):
                st.session_state.active_tab = "激活管理"
                st.rerun()

        # 功能预览
        st.markdown("### 🎁 激活后解锁功能")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            <div style="text-align: center; padding: 15px; border: 1px solid #ddd; border-radius: 10px; background: #f8f9fa;">
                <h4 style="color: #ff512f; margin: 0 0 10px 0;">🎬 STRM管理</h4>
                <p style="margin: 0; color: #666; font-size: 14px;">本地CD2管理<br>115云端管理<br>批量文件处理</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div style="text-align: center; padding: 15px; border: 1px solid #ddd; border-radius: 10px; background: #f8f9fa;">
                <h4 style="color: #ff512f; margin: 0 0 10px 0;">📡 实时监控</h4>
                <p style="margin: 0; color: #666; font-size: 14px;">CD2目录监控<br>115云盘同步<br>自动文件整理</p>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown("""
            <div style="text-align: center; padding: 15px; border: 1px solid #ddd; border-radius: 10px; background: #f8f9fa;">
                <h4 style="color: #ff512f; margin: 0 0 10px 0;">🛠️ 高级工具</h4>
                <p style="margin: 0; color: #666; font-size: 14px;">NFO批量编辑<br>海报替换<br>智能重命名</p>
            </div>
            """, unsafe_allow_html=True)

    # 功能快捷入口
    st.markdown("### 🚀 快捷功能")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🎬 STRM整理", use_container_width=True, type="primary"):
            st.session_state.active_tab = "STRM整理"
            st.rerun()

    with col2:
        if st.button("📡 实时监控", use_container_width=True):
            st.session_state.active_tab = "实时监控"
            st.rerun()

    with col3:
        if st.button("📊 统计设置", use_container_width=True):
            st.session_state.active_tab = "统计设置"
            st.rerun()

    with col4:
        if st.button("📜 日志显示", use_container_width=True):
            st.session_state.active_tab = "日志显示"
            st.rerun()

    st.markdown("---")

    # 系统统计信息
    st.markdown("### 📊 系统统计")

    # 配置统计目录（使用更通用的路径）
    records_dir = "./data/records/"
    success_dir = "./data/success/"
    failed_dir = "./data/failed/"
    actors_file = "./data/actors.json"

    # 确保目录存在
    for dir_path in [records_dir, success_dir, failed_dir]:
        os.makedirs(dir_path, exist_ok=True)

    # 刷新按钮
    col_refresh, col_empty = st.columns([1, 4])
    with col_refresh:
        if st.button("🔄 刷新统计", use_container_width=True):
            st.cache_data.clear()
            st.rerun()

    try:
        stat = {
            "入库记录": count_records(records_dir),
            "演员数量": count_actors(actors_file),
            "成功处理": count_success(success_dir),
            "失败处理": count_failed(failed_dir),
            "处理进度": calc_progress(count_success(success_dir), count_failed(failed_dir)),
        }
    except Exception as e:
        # 如果统计出错，使用默认值
        stat = {
            "入库记录": 0,
            "演员数量": 0,
            "成功处理": 0,
            "失败处理": 0,
            "处理进度": "0.00%",
        }

    col1, col2, col3, col4, col5 = st.columns(5)
    col1.metric("📚 入库记录", stat["入库记录"])
    col2.metric("👥 演员数量", stat["演员数量"])
    col3.metric("✅ 成功处理", stat["成功处理"])
    col4.metric("❌ 失败处理", stat["失败处理"])
    col5.metric("📊 处理进度", stat["处理进度"])
    st.markdown("---")

    # 当前任务进度
    st.markdown("### 🔄 当前任务进度")
    running_events = [
        e for e in st.session_state.events
        if e["status"] == "运行中"
    ]

    if running_events:
        for e in running_events:
            # 进度条+百分比
            progress_val = 0
            try:
                progress_val = float(e.get("progress", 0)) / 100
            except Exception:
                pass

            # 任务类型图标映射
            task_icons = {
                "整理文件结构": "🗂️",
                "STRM批量创建": "🎬",
                "STRM路径修改": "🔧",
                "元数据下载": "📥",
                "文件上传": "📤",
                "文件下载": "📥"
            }

            task_icon = task_icons.get(e['type'], "⚙️")
            st.markdown(f"**{task_icon} {e['type']}：{e['detail']}**")
            st.progress(progress_val, text=f"{e.get('progress', 0)}%")
    else:
        st.info("💤 当前没有正在运行的任务")

    st.markdown("---")

    # 最近活动记录
    st.markdown("### 📋 最近活动记录")

    if st.session_state.events:
        # 显示最近10条记录
        recent_events = list(reversed(st.session_state.events[-10:]))

        for idx, e in enumerate(recent_events):
            status_icon = {"运行中": "🔄", "完成": "✅", "失败": "❌"}.get(e['status'], "ℹ️")

            # 根据状态设置不同的颜色
            status_color = {
                "运行中": "#1f77b4",
                "完成": "#2ca02c",
                "失败": "#d62728"
            }.get(e['status'], "#666666")

            with st.expander(f"{status_icon} {e['time']} | {e['type']} | {e['status']}", expanded=(idx == 0 and e['status'] == "运行中")):
                col1, col2 = st.columns([3, 1])

                with col1:
                    st.write(f"**📝 详细信息：** {e['detail']}")
                    if e.get("result"):
                        st.write(f"**📊 执行结果：**")
                        st.code(e["result"], language="text")

                with col2:
                    st.metric("进度", f"{e.get('progress', 0)}%")
                    st.write(f"**状态：** <span style='color: {status_color}'>{e['status']}</span>", unsafe_allow_html=True)
    else:
        st.info("📝 暂无活动记录，开始使用功能后将显示操作历史。")

        # 显示功能介绍
        st.markdown("""
        #### 💡 功能介绍

        **🎬 STRM整理**
        - CD2本地管理：批量创建STRM、修改路径、下载元数据
        - 115云端管理：云盘文件浏览、上传下载、STRM生成

        **📡 实时监控**
        - CD2实时监控：自动处理新增文件
        - 115实时监控：云盘变化同步
        - 网盘整理监控：智能文件整理

        **📊 统计设置**
        - 系统配置管理
        - 数据统计分析
        - 性能监控
        """)

elif active_tab == "搜索详情页":
    # 模块化版本控制
    if "use_modular_search" not in st.session_state:
        st.session_state.use_modular_search = USE_MODULAR_SEARCH

    # 侧边栏版本选择
    with st.sidebar:
        st.markdown("---")
        st.markdown("#### 🔧 搜索详情页版本")

        version_choice = st.radio(
            "选择版本:",
            ["模块化版本", "原版功能"],
            index=0 if st.session_state.use_modular_search else 1,
            key="search_version_choice"
        )

        st.session_state.use_modular_search = (version_choice == "模块化版本")

        if st.session_state.use_modular_search:
            st.success("✅ 使用模块化版本")
        else:
            st.info("📜 使用原版功能")

    # 根据选择渲染对应版本
    if st.session_state.use_modular_search:
        try:
            from modules.search_detail_ui import SearchDetailUI

            # 缓存UI实例，避免重复创建
            if "search_detail_ui" not in st.session_state:
                print("🔧 首次初始化搜索详情UI...")
                st.session_state.search_detail_ui = SearchDetailUI()
                print("✅ 搜索详情UI已缓存到session_state")
            else:
                print("♻️ 从session_state获取已缓存的搜索详情UI")

            search_ui = st.session_state.search_detail_ui
            search_ui.render()
            # 模块化版本成功渲染，直接返回，不执行原版功能
            st.stop()
        except ImportError as e:
            st.error(f"❌ 模块化版本加载失败: {e}")
            st.warning("🔄 自动切换到原版功能")
            st.session_state.use_modular_search = False
            # 继续执行原版功能
        except Exception as e:
            st.error(f"❌ 模块化版本运行异常: {e}")
            st.warning("🔄 自动切换到原版功能")
            st.session_state.use_modular_search = False
            # 继续执行原版功能

    # 原版功能（仅在模块化版本失败或用户选择原版时执行）
    if not st.session_state.use_modular_search:
        st.markdown("### 🔍 增强搜索详情页 (原版)")
        st.info("🚀 输入番号搜索DMM详情页，支持海报显示、自动重命名、手动输入等功能")

    # 初始化session state
    if "search_result" not in st.session_state:
        st.session_state.search_result = None
    if "poster_result" not in st.session_state:
        st.session_state.poster_result = None
    if "manual_url_mode" not in st.session_state:
        st.session_state.manual_url_mode = False

    # 主要输入区域
    col1, col2 = st.columns([3, 1])

    with col1:
        code = st.text_input("📝 请输入番号（例如：IPX-123）", placeholder="IPX-123", key="original_search_code_input")

    with col2:
        st.markdown("**功能选项：**")
        show_poster = st.checkbox("🖼️ 显示海报", value=True, key="original_show_poster_option")
        auto_save_data = st.checkbox("💾 自动保存数据", value=True, key="original_auto_save_option")

    # 搜索按钮区域
    col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 1])

    with col_btn1:
        search_clicked = st.button("🔍 搜索DMM详情页", use_container_width=True, type="primary", key="original_search_btn")

    with col_btn2:
        manual_mode_clicked = st.button("✏️ 手动输入URL", use_container_width=True, key="original_manual_btn")

    with col_btn3:
        clear_clicked = st.button("🗑️ 清空结果", use_container_width=True, key="original_clear_btn")

    # 处理按钮点击
    if clear_clicked:
        st.session_state.search_result = None
        st.session_state.poster_result = None
        st.session_state.manual_url_mode = False
        st.rerun()

    if manual_mode_clicked:
        st.session_state.manual_url_mode = not st.session_state.manual_url_mode
        st.rerun()

    # 手动输入URL模式
    if st.session_state.manual_url_mode:
        st.markdown("---")
        st.markdown("#### ✏️ 手动输入详情页URL")
        st.info("当自动搜索失败时，您可以手动输入DMM详情页URL")

        manual_url = st.text_input(
            "🔗 请输入完整的DMM详情页URL",
            placeholder="https://www.dmm.co.jp/digital/videoa/-/detail/=/cid=xxxxx/",
            key="manual_url_input"
        )

        if st.button("🔍 解析手动URL", use_container_width=True):
            if manual_url.strip():
                with st.spinner("正在解析URL..."):
                    manual_result = parse_manual_dmm_url(manual_url.strip())
                    if manual_result["success"]:
                        # 从成功的CID中学习新的前缀规则
                        cid = manual_result["cid"]
                        learn_result = learn_from_successful_cid(cid)
                        print(f"🎓 学习结果: {learn_result}")

                        # 转换为标准格式
                        st.session_state.search_result = {
                            "success": True,
                            "code": code.strip() if code.strip() else "手动输入",
                            "cid": manual_result["cid"],
                            "label": manual_result["label"],
                            "url": manual_result["url"],
                            "message": manual_result["message"],
                            "learn_message": learn_result
                        }
                        st.success("✅ URL解析成功！")
                        if "已学习新的" in learn_result:
                            st.info(f"🎓 {learn_result}")
                        st.rerun()
                    else:
                        st.error(f"❌ {manual_result['message']}")
            else:
                st.warning("请输入有效的URL！")

    # 执行搜索
    if search_clicked:
        if code.strip():
            add_event("搜索详情页", "运行中", f"番号：{code}", progress=0)

            with st.spinner("🔍 正在搜索DMM详情页..."):
                # 执行搜索
                search_result = search_dmm_enhanced(code.strip())
                print(f"搜索结果: {search_result}")

                # 只有搜索成功时才更新结果，失败时保留原有结果并给出提示
                if search_result["success"]:
                    st.session_state.search_result = search_result

                    # 保存选项状态到不同的键
                    st.session_state.current_show_poster = show_poster
                    st.session_state.current_auto_save = auto_save_data

                    # 如果搜索成功且需要显示海报
                    print(f"检查海报获取条件: search_result['success']={search_result['success']}, show_poster={show_poster}")
                    if show_poster:
                        with st.spinner("🖼️ 正在获取海报..."):
                            try:
                                cid = search_result.get("cid", "")
                                print(f"准备获取海报，CID: {cid}")
                                # 直接使用搜索结果中的CID获取海报，避免重复搜索
                                poster_result = get_poster_with_cid(cid)
                                st.session_state.poster_result = poster_result
                                print(f"海报获取结果: {poster_result}")
                            except Exception as e:
                                error_msg = f"海报获取异常: {str(e)}"
                                print(error_msg)
                                import traceback
                                traceback.print_exc()
                                st.session_state.poster_result = {
                                    "success": False,
                                    "message": error_msg,
                                    "poster_bytes": None,
                                    "thumb_bytes": None
                                }

                    # 自动保存数据
                    print(f"检查JSON保存条件: search_result['success']={search_result['success']}, auto_save_data={auto_save_data}")
                    if auto_save_data:
                        try:
                            print(f"准备保存JSON数据: {search_result}")
                            save_result = save_dmm_data_to_json(search_result)
                            print(f"JSON保存结果: {save_result}")
                            if save_result["success"]:
                                st.session_state.search_result["save_message"] = save_result["message"]
                            else:
                                st.session_state.search_result["save_message"] = f"❌ 保存失败: {save_result['message']}"
                        except Exception as e:
                            error_msg = f"JSON保存异常: {str(e)}"
                            print(error_msg)
                            import traceback
                            traceback.print_exc()
                            st.session_state.search_result["save_message"] = f"❌ {error_msg}"

                    update_last_event(status="完成", progress=100, result="✅ 搜索成功，海报和数据处理完成")
                else:
                    # 搜索失败，显示错误但不覆盖已有结果
                    st.error(f"❌ 自动搜索失败: {search_result['message']}")
                    update_last_event(status="失败", progress=100, result=search_result["message"])

                    # 如果已有成功的结果，询问是否使用现有结果进行海报获取和数据保存
                    if st.session_state.get("search_result") and st.session_state.search_result.get("success"):
                        st.info("💡 检测到已有成功的搜索结果，是否使用现有结果进行海报获取和数据保存？")

                        col_yes, col_no = st.columns(2)
                        with col_yes:
                            if st.button("✅ 使用现有结果", use_container_width=True):
                                existing_result = st.session_state.search_result

                                # 使用现有结果进行海报获取
                                if show_poster:
                                    with st.spinner("🖼️ 正在获取海报..."):
                                        try:
                                            cid = existing_result.get("cid", "")
                                            print(f"使用现有结果获取海报，CID: {cid}")
                                            poster_result = get_poster_with_cid(cid)
                                            st.session_state.poster_result = poster_result
                                            print(f"海报获取结果: {poster_result}")
                                        except Exception as e:
                                            error_msg = f"海报获取异常: {str(e)}"
                                            print(error_msg)
                                            st.session_state.poster_result = {
                                                "success": False,
                                                "message": error_msg,
                                                "poster_bytes": None,
                                                "thumb_bytes": None
                                            }

                                # 使用现有结果进行数据保存
                                if auto_save_data:
                                    try:
                                        print(f"使用现有结果保存JSON数据: {existing_result}")
                                        save_result = save_dmm_data_to_json(existing_result)
                                        print(f"JSON保存结果: {save_result}")
                                        if save_result["success"]:
                                            st.session_state.search_result["save_message"] = save_result["message"]
                                        else:
                                            st.session_state.search_result["save_message"] = f"❌ 保存失败: {save_result['message']}"
                                    except Exception as e:
                                        error_msg = f"JSON保存异常: {str(e)}"
                                        print(error_msg)
                                        st.session_state.search_result["save_message"] = f"❌ {error_msg}"

                                st.success("✅ 已使用现有结果完成处理")
                                st.rerun()

                        with col_no:
                            if st.button("❌ 不使用", use_container_width=True):
                                st.info("已取消操作")

            st.rerun()
        else:
            st.warning("请先输入番号！")

    # 显示搜索结果
    if st.session_state.search_result:
        result = st.session_state.search_result

        st.markdown("---")
        st.markdown("#### 📋 搜索结果")

        if result["success"]:
            # 成功结果显示
            col_info, col_poster = st.columns([1, 1])

            with col_info:
                st.success("✅ 搜索成功！")

                # 基本信息
                info_data = {
                    "番号": result["code"],
                    "CID": result["cid"],
                    "厂牌": result["label"],
                    "详情页": result["url"]
                }

                for key, value in info_data.items():
                    if value:
                        if key == "详情页":
                            st.markdown(f"**{key}:** [点击访问]({value})")
                        else:
                            st.markdown(f"**{key}:** {value}")

                # 显示保存状态
                if result.get("save_message"):
                    st.info(result["save_message"])

            with col_poster:
                # 显示海报
                if st.session_state.get("poster_result"):
                    poster_data = st.session_state.poster_result
                    if poster_data.get("success"):
                        # 优先显示高清海报
                        if poster_data.get("poster_bytes"):
                            st.markdown("#### 🖼️ 高清海报")
                            display_image_with_preview(
                                poster_data["poster_bytes"],
                                "高清海报",
                                "poster",
                                max_width=250
                            )

                        # 显示缩略图（如果有且没有高清海报）
                        elif poster_data.get("thumb_bytes"):
                            st.markdown("#### 🖼️ 缩略图")
                            display_image_with_preview(
                                poster_data["thumb_bytes"],
                                "缩略图",
                                "thumbnail",
                                max_width=200
                            )

                        # 如果都有，显示缩略图选项
                        if poster_data.get("poster_bytes") and poster_data.get("thumb_bytes"):
                            st.markdown("---")
                            st.markdown("#### 📱 缩略图版本")
                            display_image_with_preview(
                                poster_data["thumb_bytes"],
                                "缩略图",
                                "thumbnail_alt",
                                max_width=180
                            )

                        if not poster_data.get("poster_bytes") and not poster_data.get("thumb_bytes"):
                            st.warning("🖼️ 海报数据为空")
                    else:
                        st.error(f"🖼️ 海报获取失败: {poster_data.get('message', '未知错误')}")
                elif st.session_state.get("current_show_poster", True):
                    st.info("🖼️ 未获取到海报图片")
        else:
            # 失败结果显示
            st.error(result["message"])

            # 提供手动输入建议
            if not st.session_state.manual_url_mode:
                st.info("💡 提示：如果自动搜索失败，您可以点击 '✏️ 手动输入URL' 按钮手动输入详情页链接")

    # 文件重命名功能
    if st.session_state.search_result and st.session_state.search_result["success"]:
        st.markdown("---")
        st.markdown("#### 📝 自动文件重命名")
        st.info("根据搜索结果自动重命名指定目录下的对应文件")

        col_dir, col_rename = st.columns([2, 1])

        with col_dir:
            target_dir = st.text_input(
                "📂 目标目录",
                value="/vol1/1000/CloudDrive/115open/R+18/待整理/刮削中/搬运测试",
                placeholder="输入包含视频文件的目录路径",
                key="rename_target_dir"
            )

        with col_rename:
            st.markdown("<br>", unsafe_allow_html=True)  # 对齐按钮
            if st.button("🚀 执行重命名", use_container_width=True):
                if target_dir.strip():
                    with st.spinner("🔄 正在重命名文件..."):
                        rename_result = auto_rename_files_for_code(
                            st.session_state.search_result["code"],
                            target_dir.strip(),
                            st.session_state.search_result
                        )

                        if rename_result["success"]:
                            st.success(f"✅ {rename_result['message']}")
                            if rename_result.get("details"):
                                with st.expander("📋 详细重命名结果", expanded=True):
                                    for detail in rename_result["details"]:
                                        st.text(detail)
                        else:
                            st.error(f"❌ {rename_result['message']}")
                else:
                    st.warning("请输入目标目录路径！")

elif active_tab == "批量重命名":
    st.markdown("### 📝 批量重命名")
    st.info("输入目录后点击按钮，自动批量重命名所有文件。")
    dir_path = st.text_input("📂 输入待重命名目录", value="./")
    if st.button("🚀 批量重命名"):
        add_event("批量重命名", "运行中", f"目录：{dir_path}", progress=0)
        result = batch_rename(dir_path)
        update_last_event(status="完成", progress=100, result=result)
        st.success("批量重命名完成！")
        st.text_area("重命名结果", result, height=400)

elif active_tab == "高清海报替换":
    st.markdown("### 🖼️ 高清海报替换")
    st.info("输入番号获取高清海报，支持添加字幕和4K水印")

    col1, col2 = st.columns([2, 1])
    with col1:
        code = st.text_input("📝 请输入番号（例如：IPX-123）", placeholder="IPX-123")
    with col2:
        st.markdown("**水印选项：**")
        need_subtitle = st.checkbox("🎬 添加字幕水印")
        need_4k = st.checkbox("📺 添加4K水印")

    if st.button("🖼️ 获取高清海报"):
        if code.strip():
            add_event("高清海报替换", "运行中", f"番号：{code}", progress=0)
            with st.spinner("正在获取高清海报..."):
                poster_bytes, thumb_bytes, msg = get_poster(code, need_4k, need_subtitle)
                update_last_event(status="完成", progress=100, result=msg)

            poster_status = ""
            thumb_status = ""

            if poster_bytes:
                st.success("🎉 高清海报获取成功！")
                st.markdown("#### 🖼️ 高清海报")
                display_image_with_preview(
                    poster_bytes,
                    "高清海报",
                    "poster_replace",
                    max_width=280
                )
                if "已存在" in msg:
                    poster_status = "✅ 高清海报已存在，无需替换。"
                elif "已保存" in msg:
                    poster_status = "✅ 高清海报已成功保存。"
                else:
                    poster_status = msg
            else:
                poster_status = f"❌ {msg or '未获取到高清海报。'}"

            if thumb_bytes:
                if poster_bytes:
                    st.markdown("---")
                st.markdown("#### 🖼️ 缩略图")
                display_image_with_preview(
                    thumb_bytes,
                    "缩略图",
                    "thumb_replace",
                    max_width=200
                )
                thumb_status = "✅ 缩略图已成功保存。"
            else:
                thumb_status = "❌ 未获取到缩略图。"

            # 显示状态信息
            if poster_status or thumb_status:
                st.info(f"{poster_status}\n{thumb_status}")
        else:
            st.warning("请先输入番号！")

elif active_tab == "文件上传":
    st.markdown("### 📤 文件上传")
    st.info("选择文件并指定保存目录，支持多种文件格式上传")

    col1, col2 = st.columns([2, 1])
    with col1:
        upload_dir = st.text_input("📂 上传保存目录", value="./",
                                  help="指定文件保存的目录路径")
    with col2:
        st.markdown("**目录操作：**")
        if st.button("📁 创建目录"):
            if upload_dir and not os.path.exists(upload_dir):
                try:
                    os.makedirs(upload_dir, exist_ok=True)
                    st.success(f"✅ 目录创建成功：{upload_dir}")
                except Exception as e:
                    st.error(f"❌ 目录创建失败：{e}")
            else:
                st.info("目录已存在或路径无效")

    file = st.file_uploader("📎 选择要上传的文件",
                           help="支持各种文件格式，单个文件最大200MB",
                           key="file_upload_main")

    if file:
        # 显示文件信息
        st.markdown("#### 📋 文件信息")
        col1, col2, col3 = st.columns(3)
        col1.metric("📄 文件名", file.name)
        col2.metric("📊 文件大小", f"{file.size / 1024:.1f} KB")
        col3.metric("📝 文件类型", file.type or "未知")

        if st.button("⬆️ 开始上传"):
            if not upload_dir.strip():
                st.warning("请指定上传目录！")
            else:
                try:
                    # 确保目录存在
                    os.makedirs(upload_dir, exist_ok=True)
                    save_path = os.path.join(upload_dir, file.name)

                    with st.spinner("正在上传文件..."):
                        with open(save_path, "wb") as f:
                            f.write(file.read())

                    st.success(f"✅ 文件上传成功！")
                    st.info(f"📁 保存路径：{save_path}")

                except Exception as e:
                    st.error(f"❌ 上传失败：{e}")
    else:
        st.info("💡 请选择要上传的文件")

elif active_tab == "整理文件结构":
    st.markdown("### 🗂️ 整理文件结构")
    st.markdown("#### 📁 自动归档和规范化文件夹结构")
    st.info("扫描指定目录中的文件，自动整理并归档到规范的文件夹结构中")

    if "is_sorting" not in st.session_state:
        st.session_state.is_sorting = False
    if "scan_files" not in st.session_state:
        st.session_state.scan_files = []

    search_dir = st.text_input("📂 待整理目录路径", value="/vol1/1000/CloudDrive/115open/R+18/待整理/刮削中/搬运测试/",
                              help="输入需要整理的文件夹路径")

    # 只在未整理时显示按钮
    if not st.session_state.is_sorting:
        if st.button("🔍 扫描待整理文件"):
            if not search_dir.strip():
                st.warning("请先输入待整理目录路径！")
            elif not os.path.exists(search_dir):
                st.error("指定的目录不存在，请检查路径是否正确！")
            else:
                with st.spinner("正在扫描文件..."):
                    files = []
                    for root, dirs, fs in os.walk(search_dir):
                        for file in fs:
                            file_path = os.path.join(root, file)
                            files.append({"文件名": file, "路径": file_path})
                    st.session_state.scan_files = files

                if files:
                    df_files = pd.DataFrame(files)
                    st.dataframe(df_files, use_container_width=True)
                    st.success(f"✅ 扫描完成！共找到 {len(files)} 个文件。")
                else:
                    st.info("📁 该目录下未找到待整理文件。")
        # 只有扫描到文件后才显示“开始整理”
        if st.session_state.scan_files:
            st.markdown("---")
            st.markdown("**📋 扫描结果：**")
            st.write(f"共找到 {len(st.session_state.scan_files)} 个文件待整理")
            if st.button("🚀 开始整理文件"):
                add_event("整理文件结构", "运行中", f"目录：{search_dir}", progress=0)
                st.session_state.is_sorting = True
                st.rerun()
        elif not st.session_state.scan_files:
            st.info("💡 请先扫描待整理文件，然后开始整理操作。")
    else:
        # 整理中只显示进度条
        st.markdown("### 🔄 正在整理文件结构...")
        st.info("系统正在自动整理文件，请耐心等待...")

        with st.spinner("正在整理文件结构，请稍候..."):
            try:
                import av_storage
                av_storage.main(search_dir)
                update_last_event(status="完成", progress=100, result="整理完成")
                st.success("🎉 文件结构整理完成！")
                st.balloons()  # 添加庆祝动画
            except Exception as e:
                update_last_event(status="失败", result=str(e))
                st.error(f"❌ 整理失败: {e}")
            # 整理完成后重置状态
            st.session_state.is_sorting = False
            st.session_state.scan_files = []
            if st.button("🔙 返回"):
                st.rerun()

elif active_tab == "STRM整理":
    st.markdown("### 🎬 STRM整理功能")

    # 激活验证检查
    if not is_feature_enabled("strm"):
        st.error("🔒 此功能需要激活后使用")
        st.warning("⚠️ STRM整理功能仅对已激活用户开放，请前往【激活管理】输入激活码。")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🔐 前往激活", type="primary"):
                st.session_state.active_tab = "激活管理"
                st.rerun()
        with col2:
            if st.button("🏠 返回首页"):
                st.session_state.active_tab = "首页"
                st.rerun()

        st.markdown("---")
        st.markdown("#### 💡 功能说明")
        st.info("""
        **STRM整理功能包括：**
        - 📁 本地CD2媒体文件管理
        - ☁️ 115云盘STRM文件生成
        - 🔄 批量文件处理和整理
        - 📊 媒体库统计和分析

        激活后即可使用完整功能！
        """)
        st.stop()

    st.info("本地CD2管理和115云端管理的综合STRM解决方案")

    # 创建标签页 - 只保留CD2本地管理和115云端管理
    tab1, tab2 = st.tabs(["💻 CD2本地管理", "☁️ 115云端管理"])

    with tab1:
        st.markdown("#### 💻 CD2本地管理")
        st.info("本地文件的STRM创建、路径修改、元数据下载等功能")

        # 初始化配置管理器
        if 'strm_config_manager' not in st.session_state:
            st.session_state.strm_config_manager = STRMConfigManager()

        config_manager = st.session_state.strm_config_manager
        config = config_manager.load_config()

        # 创建CD2本地管理的子标签页，模仿115云端管理的布局
        subtab1, subtab2, subtab3 = st.tabs([
            "📁 批量创建STRM", "🔧 修改STRM路径", "📥 下载元数据"
        ])

        with subtab1:
            st.markdown("##### 📁 批量创建STRM文件")
            st.info("扫描视频目录，为所有视频文件批量创建对应的STRM文件")

            col1, col2 = st.columns(2)
            with col1:
                src_dir = st.text_input("🎥 视频源目录",
                                      value=config["paths"]["source_dir"],
                                      help="包含视频文件的源目录路径")
            with col2:
                strm_dir = st.text_input("📂 STRM输出目录",
                                       value=config["paths"]["strm_dir"],
                                       help="STRM文件的输出目录路径")

            url_prefix = st.text_input("🌐 URL前缀",
                                     value=config["paths"]["url_prefix"],
                                     help="STRM文件中使用的URL前缀")

            # 显示支持的文件格式
            st.markdown("**📄 支持的视频格式:**")
            video_exts = config["file_formats"]["video_exts"]
            st.write(f"`{', '.join(video_exts)}`")

            if st.button("🚀 开始批量创建STRM", type="primary"):
                if not src_dir or not strm_dir or not url_prefix:
                    st.error("❌ 请填写所有必要的路径信息！")
                elif not os.path.exists(src_dir):
                    st.error(f"❌ 源目录不存在: {src_dir}")
                else:
                    add_event("STRM批量创建", "运行中", f"源目录：{src_dir}", progress=0)
                    with st.spinner("正在批量创建STRM文件..."):
                        results = batch_create_strm(src_dir, strm_dir, url_prefix)
                        update_last_event(status="完成", progress=100, result=f"处理完成，共{len(results)}个结果")

                    st.success(f"✅ 批量创建完成！共处理 {len(results)} 个文件")

                    # 显示详细结果
                    with st.expander("📋 查看详细结果", expanded=True):
                        for result in results:
                            if result.startswith("✅"):
                                st.success(result)
                            elif result.startswith("❌"):
                                st.error(result)
                            else:
                                st.info(result)

        with subtab2:
            st.markdown("##### 🔧 修改STRM文件路径")
            st.info("批量修改现有STRM文件中的URL前缀，用于服务器迁移或路径变更")

            strm_dir_modify = st.text_input("📂 STRM文件目录",
                                          value="/vol1/1000/媒体库/115/R+18/JAV/",
                                          help="包含STRM文件的目录路径",
                                          key="strm_modify_dir")

            col1, col2 = st.columns(2)
            with col1:
                old_prefix = st.text_input("🔗 旧URL前缀",
                                         placeholder="http://old-server:5244/d/path",
                                         help="要替换的旧URL前缀")
            with col2:
                new_prefix = st.text_input("🆕 新URL前缀",
                                         placeholder="http://new-server:5244/d/path",
                                         help="新的URL前缀")

            if st.button("🔄 开始批量修改", type="primary"):
                if not strm_dir_modify or not old_prefix or not new_prefix:
                    st.error("❌ 请填写所有必要的信息！")
                elif not os.path.exists(strm_dir_modify):
                    st.error(f"❌ STRM目录不存在: {strm_dir_modify}")
                else:
                    add_event("STRM路径修改", "运行中", f"目录：{strm_dir_modify}", progress=0)
                    with st.spinner("正在批量修改STRM文件..."):
                        results = update_strm_urls(strm_dir_modify, old_prefix, new_prefix)
                        update_last_event(status="完成", progress=100, result=f"修改完成，共{len(results)}个结果")

                    st.success(f"✅ 批量修改完成！共处理 {len(results)} 个文件")

                    # 显示详细结果
                    with st.expander("📋 查看详细结果", expanded=True):
                        for result in results:
                            if result.startswith("✅"):
                                st.success(result)
                            elif result.startswith("❌"):
                                st.error(result)
                            elif result.startswith("⏭️"):
                                st.info(result)

        with subtab3:
            st.markdown("##### 📥 下载元数据文件")
            st.info("为视频文件下载NFO、海报、字幕等元数据文件")

            video_dir = st.text_input("🎥 视频目录",
                                    value="/vol1/1000/媒体库/115/R+18/JAV/",
                                    help="视频文件所在目录")

            code_input = st.text_input("🏷️ 番号",
                                     placeholder="例如: CAWD-849",
                                     help="视频番号，用于搜索元数据")

            st.markdown("**选择要下载的元数据类型：**")
            col1, col2, col3 = st.columns(3)
            with col1:
                download_nfo = st.checkbox("📄 NFO文件", value=True)
            with col2:
                download_poster = st.checkbox("🖼️ 海报图片", value=True)
            with col3:
                download_subtitle = st.checkbox("📝 字幕文件", value=False)

            if st.button("📥 开始下载元数据", type="primary"):
                if not video_dir or not code_input:
                    st.error("❌ 请填写视频目录和番号！")
                else:
                    download_types = []
                    if download_nfo:
                        download_types.append('nfo')
                    if download_poster:
                        download_types.append('poster')
                    if download_subtitle:
                        download_types.append('subtitle')

                    if not download_types:
                        st.warning("⚠️ 请至少选择一种元数据类型！")
                    else:
                        add_event("元数据下载", "运行中", f"番号：{code_input}", progress=0)
                        with st.spinner("正在下载元数据文件..."):
                            results = download_metadata(video_dir, code_input, download_types)
                            update_last_event(status="完成", progress=100, result=f"下载完成，共{len(results)}个结果")

                        st.success(f"✅ 元数据下载完成！")

                        # 显示详细结果
                        with st.expander("📋 查看详细结果", expanded=True):
                            for result in results:
                                if result.startswith("✅"):
                                    st.success(result)
                                elif result.startswith("❌"):
                                    st.error(result)
                                else:
                                    st.info(result)

    with tab2:
        st.markdown("#### ☁️ 115云端管理")
        st.info("115云盘扫码登录、文件浏览、上传下载等功能")

        # 初始化115管理器
        if 'cloud115_auth' not in st.session_state:
            st.session_state.cloud115_auth = Cloud115Auth()
        if 'cloud115_file_manager' not in st.session_state:
            st.session_state.cloud115_file_manager = Cloud115FileManager(st.session_state.cloud115_auth)
        if 'cloud115_strm_manager' not in st.session_state:
            st.session_state.cloud115_strm_manager = Cloud115STRMManager(
                st.session_state.cloud115_auth,
                st.session_state.cloud115_file_manager
            )
        if 'cloud115_metadata_downloader' not in st.session_state:
            st.session_state.cloud115_metadata_downloader = Cloud115MetadataDownloader(
                st.session_state.cloud115_auth,
                st.session_state.cloud115_file_manager
            )

        auth = st.session_state.cloud115_auth
        file_manager = st.session_state.cloud115_file_manager
        strm_manager = st.session_state.cloud115_strm_manager
        metadata_downloader = st.session_state.cloud115_metadata_downloader

        # 创建子标签页
        subtab1, subtab2, subtab3, subtab4, subtab5, subtab6 = st.tabs([
            "🔐 扫码登录", "📂 云端文件浏览", "🎬 云端STRM生成",
            "⬆️ 文件上传", "⬇️ 文件下载", "📋 元数据下载"
        ])

        with subtab1:
            st.markdown("##### 🔐 115云盘登录")

            # 检查登录状态
            if auth.is_logged_in():
                user_info_result = auth.get_user_info()
                if user_info_result['success']:
                    user_info = user_info_result['data']

                    # 显示登录成功状态
                    st.success("✅ 已登录115云盘")

                    # 用户基本信息 - 简化显示
                    col1, col2 = st.columns(2)
                    with col1:
                        # 获取原始数据
                        raw_data = user_info.get('raw_data', {})
                        actual_data = raw_data.get('data', {}) if raw_data else {}

                        # 用户ID显示 - 优先从原始数据获取
                        user_id = user_info.get('user_id', '')
                        if not user_id or user_id == 'N/A' or user_id == '':
                            user_id = actual_data.get('user_id', 'N/A')

                        if user_id and user_id != 'N/A' and user_id != '':
                            st.info(f"**用户ID:** {user_id}")

                        # 用户名显示 - 优先从原始数据获取
                        username = user_info.get('username', '')
                        if not username or username == 'N/A' or username == '':
                            username = actual_data.get('user_name', 'VIP专属用户')

                        st.info(f"**用户名:** {username}")

                    with col2:
                        # VIP等级显示 - 优先从原始数据获取
                        vip_level = user_info.get('vip_level', '普通用户')
                        is_vip = actual_data.get('is_vip', 0)

                        if is_vip > 0:
                            vip_level = f"VIP专属用户 (等级: {is_vip})"

                        st.info(f"**等级:** {vip_level}")

                        # 邮箱显示（如果有的话）
                        email = user_info.get('email', '')
                        if email and email != 'N/A' and email != '':
                            st.info(f"**邮箱:** {email}")

                    # 存储空间进度条显示
                    st.markdown("---")
                    st.markdown("##### 📊 存储空间使用情况")

                    # 获取存储空间信息
                    space_used_str = user_info.get('space_used', '0B')
                    space_total_str = user_info.get('space_total', '0B')

                    # 尝试从原始数据和存储空间API获取数值
                    raw_data = user_info.get('raw_data', {})
                    space_used_bytes = 0
                    space_total_bytes = 0
                    space_remain_bytes = 0

                    # 首先尝试从raw_data获取
                    space_used_bytes = (raw_data.get('space_used') or
                                      raw_data.get('used_space') or
                                      raw_data.get('used') or 0)
                    space_total_bytes = (raw_data.get('space_total') or
                                       raw_data.get('total_space') or
                                       raw_data.get('total') or 0)

                    # 如果raw_data中没有，尝试从存储空间API数据获取
                    if space_used_bytes == 0 and space_total_bytes == 0:
                        # 检查是否有存储空间API的数据（从fs_space_info等获取）
                        space_api_data = user_info.get('space_api_data', {})
                        if space_api_data:
                            space_used_bytes = space_api_data.get('used', 0)
                            space_total_bytes = space_api_data.get('total', 0)
                            space_remain_bytes = space_api_data.get('remain', 0)

                            # 更新格式化字符串
                            if 'used_format' in space_api_data:
                                space_used_str = space_api_data['used_format']
                            if 'total_format' in space_api_data:
                                space_total_str = space_api_data['total_format']

                    # 如果有数值数据，显示美化的存储空间界面
                    if space_total_bytes > 0:
                        usage_percent = (space_used_bytes / space_total_bytes) * 100
                        space_free_bytes = space_total_bytes - space_used_bytes if space_remain_bytes == 0 else space_remain_bytes
                        space_free_str = auth._format_size(space_free_bytes)

                        # 美化的进度条显示
                        st.markdown("**📈 使用情况概览**")

                        # 根据使用率选择颜色
                        if usage_percent < 50:
                            progress_color = "🟢"
                            status_text = "充足"
                        elif usage_percent < 80:
                            progress_color = "🟡"
                            status_text = "适中"
                        else:
                            progress_color = "🔴"
                            status_text = "紧张"

                        # 显示进度条和状态
                        col_progress, col_status = st.columns([4, 1])
                        with col_progress:
                            st.progress(usage_percent / 100, text=f"已使用 {usage_percent:.1f}%")
                        with col_status:
                            st.markdown(f"{progress_color} **{status_text}**")

                        # 美化的详细信息卡片
                        st.markdown("**📋 详细信息**")
                        col1, col2, col3 = st.columns(3)

                        with col1:
                            st.markdown("""
                            <div style="background-color: #f0f2f6; padding: 1rem; border-radius: 0.5rem; text-align: center;">
                                <h4 style="color: #ff6b6b; margin: 0;">📤 已使用</h4>
                                <h3 style="margin: 0.5rem 0;">{}</h3>
                                <p style="margin: 0; color: #666;">占总容量 {:.1f}%</p>
                            </div>
                            """.format(space_used_str, usage_percent), unsafe_allow_html=True)

                        with col2:
                            st.markdown("""
                            <div style="background-color: #f0f2f6; padding: 1rem; border-radius: 0.5rem; text-align: center;">
                                <h4 style="color: #4ecdc4; margin: 0;">💾 剩余空间</h4>
                                <h3 style="margin: 0.5rem 0;">{}</h3>
                                <p style="margin: 0; color: #666;">可用空间</p>
                            </div>
                            """.format(space_free_str), unsafe_allow_html=True)

                        with col3:
                            st.markdown("""
                            <div style="background-color: #f0f2f6; padding: 1rem; border-radius: 0.5rem; text-align: center;">
                                <h4 style="color: #45b7d1; margin: 0;">🗄️ 总容量</h4>
                                <h3 style="margin: 0.5rem 0;">{}</h3>
                                <p style="margin: 0; color: #666;">VIP专属</p>
                            </div>
                            """.format(space_total_str), unsafe_allow_html=True)
                    else:
                        # 如果没有数值数据，显示美化的预估界面
                        if space_used_str == '0B' and space_total_str == '0B':
                            # 美化的VIP信息展示
                            st.markdown("**🎯 存储空间预估**")

                            # 根据VIP等级显示预估信息
                            if is_vip > 0:
                                # VIP用户的美化展示
                                vip_colors = {
                                    1: "#ff9999", 2: "#ffb366", 3: "#ffcc66", 4: "#ccff66",
                                    5: "#66ff99", 6: "#66ffcc", 7: "#66ccff", 8: "#9966ff"
                                }
                                vip_space_info = {
                                    1: ("1TB", "适合轻度使用"),
                                    2: ("2TB", "适合中度使用"),
                                    3: ("5TB", "适合重度使用"),
                                    4: ("10TB", "适合专业用户"),
                                    5: ("20TB", "适合企业用户"),
                                    6: ("50TB", "适合大型企业"),
                                    7: ("100TB", "适合超大企业"),
                                    8: ("无限容量", "顶级VIP专享")
                                }

                                if is_vip in vip_space_info:
                                    space_size, description = vip_space_info[is_vip]
                                    color = vip_colors.get(is_vip, "#9966ff")

                                    st.markdown(f"""
                                    <div style="background: linear-gradient(135deg, {color}20, {color}10);
                                                padding: 2rem; border-radius: 1rem; text-align: center;
                                                border: 2px solid {color}40;">
                                        <h2 style="color: {color}; margin: 0;">🎉 VIP{is_vip} 专属用户</h2>
                                        <h1 style="color: {color}; margin: 1rem 0; font-size: 3rem;">{space_size}</h1>
                                        <p style="color: #666; margin: 0; font-size: 1.2rem;">{description}</p>
                                        <div style="margin-top: 1rem;">
                                            <span style="background-color: {color}; color: white;
                                                       padding: 0.5rem 1rem; border-radius: 2rem;
                                                       font-weight: bold;">✨ 专属特权</span>
                                        </div>
                                    </div>
                                    """, unsafe_allow_html=True)
                            else:
                                # 普通用户的展示
                                st.markdown("""
                                <div style="background: linear-gradient(135deg, #f093fb20, #f5576c20);
                                            padding: 2rem; border-radius: 1rem; text-align: center;
                                            border: 2px solid #f093fb40;">
                                    <h2 style="color: #f093fb; margin: 0;">📦 普通用户</h2>
                                    <h1 style="color: #f093fb; margin: 1rem 0; font-size: 3rem;">15GB</h1>
                                    <p style="color: #666; margin: 0; font-size: 1.2rem;">免费存储空间</p>
                                    <div style="margin-top: 1rem;">
                                        <span style="background-color: #f093fb; color: white;
                                                   padding: 0.5rem 1rem; border-radius: 2rem;
                                                   font-weight: bold;">🆓 免费使用</span>
                                    </div>
                                </div>
                                """, unsafe_allow_html=True)

                            # 操作提示
                            st.markdown("---")
                            col1, col2 = st.columns(2)
                            with col1:
                                st.markdown("""
                                **💡 温馨提示:**
                                - 🔍 无法获取精确存储空间数据
                                - 📊 以上为根据VIP等级的预估信息
                                - 🌐 如需查看准确数据，请访问115官网
                                """)
                            with col2:
                                if st.button("🔄 重新获取存储空间", use_container_width=True):
                                    # 清除存储空间缓存并重新获取
                                    try:
                                        from pathlib import Path
                                        import json

                                        config_file = Path("bg_upload/115_config.json")
                                        if config_file.exists():
                                            # 读取现有配置
                                            with open(config_file, 'r', encoding='utf-8') as f:
                                                config = json.load(f)

                                            # 只清除用户信息中的存储空间数据，保留其他信息
                                            if 'user_info' in config:
                                                config['user_info']['space_used'] = '0B'
                                                config['user_info']['space_total'] = '0B'
                                                # 清除存储空间API数据
                                                if 'space_api_data' in config['user_info']:
                                                    del config['user_info']['space_api_data']

                                            # 保存更新后的配置
                                            with open(config_file, 'w', encoding='utf-8') as f:
                                                json.dump(config, f, ensure_ascii=False, indent=2)

                                            st.success("✅ 存储空间缓存已清除，正在重新获取...")

                                            # 强制重新获取用户信息（包括存储空间）
                                            if hasattr(auth, 'p115_client') and auth.p115_client:
                                                try:
                                                    # 尝试重新获取用户信息
                                                    user_info_result = auth.p115_client.user_info()
                                                    if user_info_result:
                                                        st.info("🔄 正在尝试重新获取存储空间信息...")
                                                except Exception as e:
                                                    st.error(f"❌ 重新获取失败: {e}")
                                        else:
                                            st.warning("⚠️ 配置文件不存在")

                                        st.rerun()
                                    except Exception as e:
                                        st.error(f"❌ 操作失败: {e}")

                                if st.button("🌐 访问115官网", use_container_width=True):
                                    st.markdown('<a href="https://115.com" target="_blank">🌐 115官网</a>', unsafe_allow_html=True)
                        else:
                            # 有部分数据但不完整的情况
                            st.markdown(f"""
                            <div style="background-color: #fff3cd; padding: 1.5rem; border-radius: 0.5rem;
                                       border-left: 4px solid #ffc107;">
                                <h4 style="color: #856404; margin: 0 0 1rem 0;">📊 存储空间信息</h4>
                                <p style="margin: 0; color: #856404; font-size: 1.1rem;">
                                    <strong>已使用:</strong> {space_used_str} / <strong>总容量:</strong> {space_total_str}
                                </p>
                            </div>
                            """, unsafe_allow_html=True)



                    # 退出登录按钮
                    if st.button("🚪 退出登录", type="secondary"):
                        if auth.logout():
                            st.success("✅ 已退出登录")
                            st.rerun()
                        else:
                            st.error("❌ 退出登录失败")
                else:
                    st.error(f"❌ 获取用户信息失败: {user_info_result.get('error', '未知错误')}")

                    # 提供重新登录选项
                    if st.button("🔄 强制重新登录", type="secondary"):
                        result = auth.force_relogin()
                        if result['success']:
                            st.success("✅ 已清理登录状态，请重新登录")
                            st.rerun()
                        else:
                            st.error(f"❌ 重新登录失败: {result.get('error', '未知错误')}")
            else:
                st.warning("⚠️ 未登录115云盘")

                # Cookie登录方式
                st.markdown("##### 🍪 Cookie登录")
                st.info("💡 使用115网页版Cookie直接登录，基于p115client，快速安全")

                with st.expander("📋 如何获取115 Cookie？", expanded=False):
                    st.markdown("""
                    **获取Cookie步骤：**
                    1. 打开浏览器，访问 https://115.com
                    2. 登录您的115账号
                    3. 按F12打开开发者工具
                    4. 切换到"Network"标签页
                    5. 刷新页面，找到任意请求
                    6. 在请求头中找到"Cookie"字段
                    7. 复制Cookie值粘贴到下方输入框

                    **Cookie示例格式：**
                    ```
                    UID=123456789; CID=abcdef123; SEID=xyz789abc; PHPSESSID=session123
                    ```

                    **p115client方式：**
                    - 支持自动重新登录
                    - 自动保存到文件
                    - 更稳定的API调用
                    """)

                    # 处理演示Cookie
                    demo_cookie_value = ""
                    if 'demo_cookie' in st.session_state:
                        demo_cookie_value = st.session_state.demo_cookie
                        del st.session_state.demo_cookie  # 使用后删除

                    cookie_input = st.text_area(
                        "🍪 输入115 Cookie",
                        value=demo_cookie_value,
                        height=100,
                        placeholder="UID=123456789; CID=abcdef123; SEID=xyz789abc; PHPSESSID=session123",
                        help="从浏览器开发者工具中复制完整的Cookie字符串"
                    )

                    col1, col2 = st.columns([3, 1])
                    with col1:
                        if st.button("🚀 使用Cookie登录", type="primary", disabled=not cookie_input.strip()):
                            with st.spinner("正在使用p115client验证Cookie..."):
                                if cookie_input.strip():
                                    try:
                                        # 使用p115client方式登录
                                        login_result = auth.login_with_cookies(cookie_input.strip())

                                        if login_result['success']:
                                            st.success("🎉 Cookie登录成功！")

                                            # 显示登录信息
                                            if login_result.get('user_info'):
                                                user_info = login_result['user_info']
                                                st.info(f"✅ 欢迎回来，{user_info.get('username', '用户')}！")

                                                # 显示详细信息
                                                with st.expander("👤 用户信息详情", expanded=False):
                                                    col1, col2 = st.columns(2)
                                                    with col1:
                                                        st.write(f"**用户ID:** {user_info.get('user_id', 'N/A')}")
                                                        st.write(f"**用户名:** {user_info.get('username', 'N/A')}")
                                                    with col2:
                                                        st.write(f"**VIP等级:** {user_info.get('vip_level', 'N/A')}")
                                                        st.write(f"**存储空间:** {user_info.get('space_used', 'N/A')}/{user_info.get('space_total', 'N/A')}")

                                            if login_result.get('is_real_client'):
                                                st.success("🔗 已连接到真实115服务")
                                            else:
                                                st.info("⚠️ 使用兼容模式")

                                            st.balloons()
                                            time.sleep(1)
                                            st.rerun()
                                        else:
                                            st.error(f"❌ Cookie登录失败: {login_result.get('error', '未知错误')}")

                                            # 显示详细错误信息
                                            if login_result.get('details'):
                                                with st.expander("🔍 错误详情", expanded=False):
                                                    st.code(login_result['details'])

                                    except Exception as e:
                                        st.error(f"❌ Cookie登录异常: {str(e)}")

                                        # 显示调试信息
                                        with st.expander("🐛 调试信息", expanded=False):
                                            import traceback
                                            st.code(traceback.format_exc())
                                else:
                                    st.error("❌ 请输入有效的Cookie")

                    with col2:
                        st.markdown("<br>", unsafe_allow_html=True)
                        if st.button("🧪 使用演示Cookie"):
                            # 填入演示Cookie（p115client格式）
                            demo_cookie = "UID=987654321; CID=demo789abc; SEID=xyz456def; PHPSESSID=demo123session"
                            st.session_state.demo_cookie = demo_cookie
                            st.info("✅ 已填入演示Cookie，点击登录按钮测试")
                            st.rerun()



        with subtab2:
            st.markdown("##### 📂 云端文件浏览")

            if not auth.is_logged_in():
                st.warning("⚠️ 请先登录115云盘")
            else:
                # 初始化导航状态
                if 'current_folder_id' not in st.session_state:
                    st.session_state.current_folder_id = "0"

                # 简化的导航界面
                col1, col2, col3 = st.columns([2, 1, 1])
                with col1:
                    folder_id = st.text_input("📁 文件夹ID", value=st.session_state.current_folder_id, help="输入要浏览的文件夹ID，0为根目录")
                with col2:
                    if st.button("🔄 刷新文件列表"):
                        st.session_state.current_folder_id = folder_id
                        st.rerun()
                with col3:
                    if st.button("🏠 返回根目录"):
                        st.session_state.current_folder_id = "0"
                        st.rerun()

                # 获取当前文件夹的文件列表
                current_folder_id = st.session_state.current_folder_id

                with st.spinner("正在获取文件列表..."):
                    files_result = file_manager.list_files(current_folder_id)

                    if files_result['success']:
                        files_data = files_result['data']
                        files = files_data['files']

                        # 分离文件夹和文件
                        folders = [f for f in files if f['is_folder']]
                        regular_files = [f for f in files if not f['is_folder']]

                        st.success(f"✅ 共找到 {len(folders)} 个文件夹，{len(regular_files)} 个文件")

                        if files:
                            # 文件夹列表（可点击进入）
                            if folders:
                                st.markdown("##### 📁 文件夹")

                                # 简化的文件夹显示
                                for folder in folders:
                                    col1, col2 = st.columns([3, 1])
                                    with col1:
                                        if st.button(
                                            f"📁 {folder['name']}",
                                            key=f"folder_{folder['file_id']}",
                                            help=f"点击进入文件夹: {folder['name']}"
                                        ):
                                            # 进入子文件夹
                                            st.session_state.current_folder_id = folder['file_id']
                                            st.rerun()
                                    with col2:
                                        st.caption(f"ID: {folder['file_id']}")

                                st.markdown("---")

                            # 文件列表表格
                            if regular_files:
                                st.markdown("##### 📄 文件列表")

                                # 文件类型筛选
                                file_types = list(set([f['type'] for f in regular_files]))
                                selected_types = st.multiselect(
                                    "🔍 筛选文件类型",
                                    options=file_types,
                                    default=file_types,
                                    key="file_type_filter"
                                )

                                # 筛选文件
                                filtered_files = [f for f in regular_files if f['type'] in selected_types]

                                if filtered_files:
                                    # 创建文件列表表格
                                    df_data = []
                                    for file_info in filtered_files:
                                        size_str = f"{file_info['size'] / (1024*1024):.1f} MB" if file_info['size'] > 0 else "-"

                                        # 文件类型图标
                                        type_icon = {
                                            'video': '🎬',
                                            'image': '🖼️',
                                            'audio': '🎵',
                                            'document': '📄',
                                            'archive': '📦',
                                            'file': '📄'
                                        }.get(file_info['type'], '📄')

                                        df_data.append({
                                            "📁 名称": f"{type_icon} {file_info['name']}",
                                            "📊 大小": size_str,
                                            "📅 创建时间": file_info['created_time'],
                                            "🏷️ 类型": file_info['type'],
                                            "🆔 ID": file_info['file_id']
                                        })

                                    df = pd.DataFrame(df_data)
                                    st.dataframe(df, use_container_width=True)
                                else:
                                    st.info("🔍 没有符合筛选条件的文件")
                            else:
                                st.info("📄 当前文件夹中没有文件")

                            # 文件统计和操作
                            video_files = [f for f in regular_files if
                                         os.path.splitext(f['name'])[1].lower() in VIDEO_EXTS]

                            if video_files:
                                st.markdown("---")
                                st.markdown("##### 🎬 视频文件操作")

                                col1, col2, col3 = st.columns(3)
                                with col1:
                                    st.metric("🎬 视频文件", len(video_files))
                                with col2:
                                    total_video_size = sum(f['size'] for f in video_files)
                                    st.metric("📊 总大小", f"{total_video_size / (1024**3):.2f} GB")
                                with col3:
                                    st.metric("📁 文件夹", len(folders))

                                # STRM生成配置
                                st.markdown("##### 🎬 STRM文件生成")

                                col1, col2 = st.columns(2)
                                with col1:
                                    strm_output_dir = st.text_input("📂 STRM输出目录",
                                                                  value="/vol1/1000/媒体库/115/R+18/JAV/",
                                                                  help="STRM文件的保存目录")

                                    scan_recursive = st.checkbox("🔄 递归扫描子文件夹",
                                                               value=True,
                                                               help="是否扫描当前文件夹的所有子文件夹")

                                with col2:
                                    use_direct_url = st.radio("🔗 STRM内容类型",
                                                            options=[True, False],
                                                            format_func=lambda x: "115直链" if x else "自定义URL",
                                                            index=0,
                                                            help="选择STRM文件中使用的链接类型")

                                    if not use_direct_url:
                                        url_prefix = st.text_input("🌐 URL前缀",
                                                                 value="http://192.168.1.2:5244/d/115/R+18/已整理/更新",
                                                                 help="自定义URL前缀，用于构建STRM文件内容")
                                    else:
                                        url_prefix = ""

                                keep_structure = st.checkbox("📁 保持文件夹结构",
                                                           value=True,
                                                           help="在STRM输出目录中保持原有的文件夹结构")

                                if st.button("🎬 生成STRM文件", type="primary"):
                                    if strm_output_dir:
                                        with st.spinner("正在扫描和生成STRM文件..."):
                                            # 根据选择决定扫描方式
                                            if scan_recursive:
                                                scan_result = strm_manager.scan_cloud_videos_recursive(current_folder_id)
                                            else:
                                                scan_result = strm_manager.scan_cloud_videos(current_folder_id)

                                            if scan_result['success']:
                                                all_videos = scan_result['data']
                                                st.info(f"📊 共扫描到 {len(all_videos)} 个视频文件")

                                                if all_videos:
                                                    # 生成STRM文件
                                                    if keep_structure:
                                                        strm_results = strm_manager.generate_cloud_strm_with_structure(
                                                            all_videos, strm_output_dir, url_prefix, use_direct_url
                                                        )
                                                    else:
                                                        # 不保持结构，所有文件放在根目录
                                                        for video in all_videos:
                                                            video['path'] = ""  # 清空路径
                                                        strm_results = strm_manager.generate_cloud_strm_with_structure(
                                                            all_videos, strm_output_dir, url_prefix, use_direct_url
                                                        )

                                                    st.success(f"✅ STRM生成完成！共处理 {len(strm_results)} 个文件")

                                                    # 统计结果
                                                    success_count = len([r for r in strm_results if r.startswith("✅")])
                                                    error_count = len([r for r in strm_results if r.startswith("❌")])

                                                    col1, col2 = st.columns(2)
                                                    with col1:
                                                        st.metric("✅ 成功", success_count)
                                                    with col2:
                                                        st.metric("❌ 失败", error_count)

                                                    with st.expander("📋 查看详细结果", expanded=True):
                                                        for result in strm_results:
                                                            if result.startswith("✅"):
                                                                st.success(result)
                                                            else:
                                                                st.error(result)
                                                else:
                                                    st.warning("📂 未找到视频文件")
                                            else:
                                                st.error(f"❌ 扫描失败: {scan_result['error']}")
                                    else:
                                        st.error("❌ 请设置STRM输出目录")
                        else:
                            st.info("📂 当前文件夹为空")

                        # 快速操作面板
                        if files:
                            st.markdown("---")
                            st.markdown("##### ⚡ 快速操作")

                            col1, col2, col3, col4 = st.columns(4)

                            with col1:
                                if st.button("📊 文件统计", use_container_width=True):
                                    # 显示详细统计信息
                                    st.info("📊 文件统计功能")

                                    # 按类型统计
                                    type_stats = {}
                                    total_size = 0
                                    for file in regular_files:
                                        file_type = file['type']
                                        if file_type not in type_stats:
                                            type_stats[file_type] = {'count': 0, 'size': 0}
                                        type_stats[file_type]['count'] += 1
                                        type_stats[file_type]['size'] += file['size']
                                        total_size += file['size']

                                    st.write("**文件类型统计:**")
                                    for file_type, stats in type_stats.items():
                                        st.write(f"- {file_type}: {stats['count']} 个文件, {stats['size'] / (1024**2):.1f} MB")

                                    st.write(f"**总计:** {len(regular_files)} 个文件, {total_size / (1024**2):.1f} MB")

                            with col2:
                                if st.button("🔗 批量获取链接", use_container_width=True):
                                    st.info("🔗 批量获取下载链接功能")
                                    # 这里可以添加批量获取下载链接的功能

                            with col3:
                                if st.button("📋 导出文件列表", use_container_width=True):
                                    # 导出当前文件列表为CSV
                                    if regular_files:
                                        import io
                                        output = io.StringIO()
                                        df_export = pd.DataFrame([{
                                            '文件名': f['name'],
                                            '大小(MB)': f['size'] / (1024**2) if f['size'] > 0 else 0,
                                            '类型': f['type'],
                                            '创建时间': f['created_time'],
                                            '文件ID': f['file_id']
                                        } for f in regular_files])

                                        csv_data = df_export.to_csv(index=False)
                                        st.download_button(
                                            label="📥 下载CSV文件",
                                            data=csv_data,
                                            file_name=f"115_files_{current_folder_id}.csv",
                                            mime="text/csv"
                                        )

                            with col4:
                                if st.button("🔄 清除缓存", use_container_width=True):
                                    st.session_state.folder_cache.clear()
                                    st.success("✅ 缓存已清除")
                                    st.rerun()
                    else:
                        st.error(f"❌ 获取文件列表失败: {files_result['error']}")

                        # 提供重试选项
                        if st.button("🔄 重试获取文件列表"):
                            if current_folder_id in st.session_state.folder_cache:
                                del st.session_state.folder_cache[current_folder_id]
                            st.rerun()

        with subtab3:
            st.markdown("##### 🎬 云端STRM批量生成")
            st.info("从115云盘批量生成STRM文件，支持文件夹结构和自定义路径配置")

            if not auth.is_logged_in():
                st.warning("⚠️ 请先登录115云盘")
            else:
                # STRM生成配置区域
                st.markdown("**📋 生成配置**")

                col1, col2 = st.columns(2)
                with col1:
                    # 源文件夹选择
                    source_folder_id = st.text_input("📁 源文件夹ID",
                                                   value="0",
                                                   help="要扫描的115云盘文件夹ID，0表示根目录")

                    strm_output_dir = st.text_input("📂 STRM输出目录",
                                                  value="/vol1/1000/媒体库/115/R+18/JAV/",
                                                  help="本地STRM文件的保存目录")

                with col2:
                    scan_recursive = st.checkbox("🔄 递归扫描子文件夹",
                                               value=True,
                                               help="是否扫描所有子文件夹中的视频文件")

                    keep_structure = st.checkbox("📁 保持文件夹结构",
                                               value=True,
                                               help="在STRM输出目录中保持云盘的文件夹结构")

                # STRM内容配置
                st.markdown("**🔗 STRM内容配置**")
                use_direct_url = st.radio("STRM链接类型",
                                        options=[True, False],
                                        format_func=lambda x: "115直链（推荐）" if x else "自定义URL前缀",
                                        index=0,
                                        horizontal=True,
                                        help="115直链：使用115的直接下载链接；自定义URL：使用自己的文件服务器")

                if not use_direct_url:
                    url_prefix = st.text_input("🌐 URL前缀",
                                             value="http://192.168.1.2:5244/d/115/R+18/已整理/更新",
                                             help="自定义URL前缀，STRM文件将使用此前缀+相对路径")
                else:
                    url_prefix = ""

                # 预览和执行区域
                st.markdown("**🚀 执行操作**")

                col1, col2 = st.columns(2)
                with col1:
                    if st.button("🔍 预览扫描结果", type="secondary"):
                        with st.spinner("正在扫描视频文件..."):
                            if scan_recursive:
                                scan_result = strm_manager.scan_cloud_videos_recursive(source_folder_id)
                            else:
                                scan_result = strm_manager.scan_cloud_videos(source_folder_id)

                            if scan_result['success']:
                                videos = scan_result['data']
                                st.success(f"📊 共找到 {len(videos)} 个视频文件")

                                if videos:
                                    # 按文件夹分组显示
                                    folder_groups = {}
                                    for video in videos:
                                        folder_path = video.get('path', '根目录')
                                        if folder_path == '':
                                            folder_path = '根目录'
                                        if folder_path not in folder_groups:
                                            folder_groups[folder_path] = []
                                        folder_groups[folder_path].append(video)

                                    with st.expander("📋 视频文件预览", expanded=True):
                                        for folder, files in folder_groups.items():
                                            st.markdown(f"**📁 {folder}** ({len(files)} 个文件)")
                                            for file in files[:5]:  # 只显示前5个
                                                st.write(f"  📄 {file['name']}")
                                            if len(files) > 5:
                                                st.write(f"  ... 还有 {len(files) - 5} 个文件")
                                            st.write("")
                                else:
                                    st.info("📂 未找到视频文件")
                            else:
                                st.error(f"❌ 扫描失败: {scan_result['error']}")

                with col2:
                    if st.button("🎬 开始生成STRM", type="primary"):
                        if not strm_output_dir:
                            st.error("❌ 请设置STRM输出目录")
                        elif not use_direct_url and not url_prefix:
                            st.error("❌ 使用自定义URL时请设置URL前缀")
                        else:
                            with st.spinner("正在生成STRM文件..."):
                                # 扫描视频文件
                                if scan_recursive:
                                    scan_result = strm_manager.scan_cloud_videos_recursive(source_folder_id)
                                else:
                                    scan_result = strm_manager.scan_cloud_videos(source_folder_id)

                                if scan_result['success']:
                                    videos = scan_result['data']

                                    if videos:
                                        # 处理文件夹结构
                                        if not keep_structure:
                                            for video in videos:
                                                video['path'] = ""  # 清空路径，所有文件放在根目录

                                        # 生成STRM文件
                                        strm_results = strm_manager.generate_cloud_strm_with_structure(
                                            videos, strm_output_dir, url_prefix, use_direct_url
                                        )

                                        # 显示结果
                                        success_count = len([r for r in strm_results if r.startswith("✅")])
                                        error_count = len([r for r in strm_results if r.startswith("❌")])

                                        st.success(f"🎉 STRM生成完成！")

                                        col1, col2, col3 = st.columns(3)
                                        with col1:
                                            st.metric("📊 总数", len(videos))
                                        with col2:
                                            st.metric("✅ 成功", success_count)
                                        with col3:
                                            st.metric("❌ 失败", error_count)

                                        with st.expander("📋 详细结果", expanded=error_count > 0):
                                            for result in strm_results:
                                                if result.startswith("✅"):
                                                    st.success(result)
                                                else:
                                                    st.error(result)
                                    else:
                                        st.warning("📂 未找到视频文件")
                                else:
                                    st.error(f"❌ 扫描失败: {scan_result['error']}")

        with subtab4:
            st.markdown("##### ⬆️ 文件上传")

            if not auth.is_logged_in():
                st.warning("⚠️ 请先登录115云盘")
            else:
                st.info("📤 上传本地文件到115云盘")

                uploaded_file = st.file_uploader("选择要上传的文件", type=None, key="upload_115_file")
                parent_folder_id = st.text_input("目标文件夹ID", value="0", help="上传到指定文件夹，0为根目录")

                if uploaded_file and st.button("🚀 开始上传", type="primary"):
                    # 保存临时文件
                    temp_path = os.path.join(BG_UPLOAD_DIR, uploaded_file.name)
                    with open(temp_path, "wb") as f:
                        f.write(uploaded_file.read())

                    with st.spinner("正在上传文件..."):
                        upload_result = file_manager.upload_file(temp_path, parent_folder_id)

                        if upload_result['success']:
                            st.success("✅ 文件上传成功！")
                            upload_data = upload_result['data']
                            st.info(f"**文件名:** {upload_data['name']}")
                            st.info(f"**大小:** {upload_data['size'] / (1024*1024):.1f} MB")
                            st.info(f"**上传时间:** {upload_data['upload_time']}")
                        else:
                            st.error(f"❌ 文件上传失败: {upload_result['error']}")

                    # 清理临时文件
                    if os.path.exists(temp_path):
                        os.remove(temp_path)

        with subtab5:
            st.markdown("##### ⬇️ 文件下载")

            if not auth.is_logged_in():
                st.warning("⚠️ 请先登录115云盘")
            else:
                st.info("📥 从115云盘下载文件")

                col1, col2 = st.columns(2)
                with col1:
                    file_id = st.text_input("文件ID", help="要下载的文件ID")
                with col2:
                    save_dir = st.text_input("保存目录", value="./downloads/", help="文件保存的本地目录")

                if file_id and save_dir and st.button("📥 获取下载链接", type="primary"):
                    with st.spinner("正在获取下载链接..."):
                        url_result = file_manager.get_download_url(file_id)

                        if url_result['success']:
                            download_url = url_result['data']['download_url']
                            expire_time = datetime.fromtimestamp(url_result['data']['expire_time'])

                            st.success("✅ 下载链接获取成功！")
                            st.code(download_url, language="text")
                            st.info(f"⏰ 链接过期时间: {expire_time.strftime('%Y-%m-%d %H:%M:%S')}")

                            st.markdown("**使用说明:**")
                            st.write("1. 复制上方链接到下载工具（如IDM、迅雷等）")
                            st.write("2. 或者点击下方按钮尝试直接下载")

                            if st.button("🔗 在新窗口打开下载链接"):
                                st.markdown(f'<a href="{download_url}" target="_blank">点击下载</a>',
                                          unsafe_allow_html=True)
                        else:
                            st.error(f"❌ 获取下载链接失败: {url_result['error']}")

        with subtab6:
            st.markdown("##### 📋 元数据下载")
            st.info("🎯 批量下载115云盘中的NFO、海报、字幕等元数据文件，保持目录结构")

            if not auth.is_logged_in():
                st.warning("⚠️ 请先登录115云盘")
            else:
                # 扫描设置
                st.markdown("**🔍 扫描设置**")
                col1, col2, col3 = st.columns(3)

                with col1:
                    scan_folder_id = st.text_input("📁 扫描文件夹ID", value="0", help="要扫描的115云盘文件夹ID，0为根目录")

                with col2:
                    recursive_scan = st.checkbox("🔄 递归扫描子文件夹", value=True, help="是否扫描所有子文件夹")

                with col3:
                    max_depth = st.number_input("📊 最大扫描深度", min_value=1, max_value=20, value=10, help="递归扫描的最大深度")

                # 下载设置
                st.markdown("**📥 下载设置**")
                col1, col2 = st.columns(2)

                with col1:
                    local_base_dir = st.text_input(
                        "💾 本地保存目录",
                        value="./downloads/metadata/",
                        help="元数据文件的本地保存目录"
                    )

                with col2:
                    preserve_structure = st.checkbox(
                        "🗂️ 保持目录结构",
                        value=True,
                        help="是否在本地保持云盘的目录结构"
                    )

                # 扫描按钮
                if st.button("🔍 扫描元数据文件", type="primary"):
                    with st.spinner("正在扫描元数据文件..."):
                        scan_result = metadata_downloader.scan_metadata_files(
                            folder_id=scan_folder_id,
                            recursive=recursive_scan,
                            max_depth=max_depth if recursive_scan else 1
                        )

                        if scan_result['success']:
                            metadata_files = scan_result['data']['metadata_files']
                            total_count = scan_result['data']['total_count']

                            if metadata_files:
                                st.success(f"✅ 找到 {total_count} 个元数据文件")

                                # 按类型分组显示
                                metadata_by_type = {}
                                for file_info in metadata_files:
                                    file_type = file_info.get('metadata_type', 'other')
                                    if file_type not in metadata_by_type:
                                        metadata_by_type[file_type] = []
                                    metadata_by_type[file_type].append(file_info)

                                # 显示统计信息
                                st.markdown("**📊 文件类型统计**")
                                type_names = {
                                    'poster': '🖼️ 海报图片',
                                    'info': '📄 NFO信息',
                                    'subtitle': '📝 字幕文件',
                                    'description': '📋 描述文件',
                                    'other': '📄 其他文件'
                                }

                                cols = st.columns(len(metadata_by_type))
                                for i, (file_type, files) in enumerate(metadata_by_type.items()):
                                    with cols[i]:
                                        st.metric(
                                            type_names.get(file_type, f"📄 {file_type}"),
                                            len(files)
                                        )

                                # 显示文件列表
                                st.markdown("**📋 元数据文件列表**")

                                # 创建文件列表表格
                                df_data = []
                                for file_info in metadata_files:
                                    type_icon = {
                                        'poster': '🖼️',
                                        'info': '📄',
                                        'subtitle': '📝',
                                        'description': '📋',
                                        'other': '📄'
                                    }.get(file_info.get('metadata_type', 'other'), '📄')

                                    size_str = f"{file_info['size'] / 1024:.1f} KB" if file_info['size'] > 0 else "-"

                                    df_data.append({
                                        "📁 文件名": f"{type_icon} {file_info['name']}",
                                        "📂 路径": file_info.get('path', '/'),
                                        "📊 大小": size_str,
                                        "🏷️ 类型": type_names.get(file_info.get('metadata_type', 'other'), '其他'),
                                        "🆔 ID": file_info['file_id']
                                    })

                                df = pd.DataFrame(df_data)
                                st.dataframe(df, use_container_width=True)

                                # 保存扫描结果到session state
                                st.session_state.scanned_metadata_files = metadata_files

                                # 下载按钮
                                st.markdown("---")
                                col1, col2 = st.columns(2)

                                with col1:
                                    if st.button("📥 开始下载所有元数据", type="primary", use_container_width=True):
                                        with st.spinner("正在下载元数据文件..."):
                                            download_result = metadata_downloader.download_metadata_with_structure(
                                                metadata_files,
                                                local_base_dir,
                                                preserve_structure
                                            )

                                            if download_result['success']:
                                                summary = download_result['data']['summary']
                                                st.success(f"✅ 下载完成！成功: {summary['success_count']}, 失败: {summary['failed_count']}")
                                                st.info(f"📁 文件保存在: {summary['local_base_dir']}")

                                                # 显示下载详情
                                                if st.checkbox("📋 显示下载详情"):
                                                    download_results = download_result['data']['download_results']

                                                    success_files = [r for r in download_results if r['status'] == 'success']
                                                    failed_files = [r for r in download_results if r['status'] == 'failed']

                                                    if success_files:
                                                        st.markdown("**✅ 下载成功的文件:**")
                                                        for result in success_files:
                                                            st.write(f"- ✅ {result['file_name']} ({result.get('metadata_type', 'other')})")

                                                    if failed_files:
                                                        st.markdown("**❌ 下载失败的文件:**")
                                                        for result in failed_files:
                                                            st.write(f"- ❌ {result['file_name']}: {result.get('error', '未知错误')}")
                                            else:
                                                st.error(f"❌ 下载失败: {download_result['error']}")

                                with col2:
                                    if st.button("📂 打开本地目录", use_container_width=True):
                                        import subprocess
                                        import platform

                                        try:
                                            # 确保目录存在
                                            os.makedirs(local_base_dir, exist_ok=True)

                                            # 根据操作系统打开文件夹
                                            if platform.system() == "Windows":
                                                subprocess.run(["explorer", local_base_dir])
                                            elif platform.system() == "Darwin":  # macOS
                                                subprocess.run(["open", local_base_dir])
                                            else:  # Linux
                                                subprocess.run(["xdg-open", local_base_dir])

                                            st.success(f"📂 已打开目录: {local_base_dir}")
                                        except Exception as e:
                                            st.error(f"❌ 打开目录失败: {str(e)}")
                            else:
                                st.warning("🔍 未找到元数据文件")
                                st.info("💡 提示: 元数据文件包括 .jpg, .png, .nfo, .srt, .ass, .sub 等格式")
                        else:
                            st.error(f"❌ 扫描失败: {scan_result['error']}")

                # 支持的文件格式说明
                st.markdown("---")
                st.markdown("**📋 支持的元数据文件格式:**")
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("""
                    **🖼️ 图片文件:**
                    - .jpg, .jpeg - 海报图片
                    - .png - 海报图片
                    - .webp - 海报图片
                    """)

                    st.markdown("""
                    **📄 信息文件:**
                    - .nfo - 媒体信息文件
                    - .txt - 描述文件
                    """)

                with col2:
                    st.markdown("""
                    **📝 字幕文件:**
                    - .srt - SRT字幕
                    - .ass - ASS字幕
                    - .sub - SUB字幕
                    """)

                    st.markdown("""
                    **💡 功能特点:**
                    - 🔄 支持递归扫描子文件夹
                    - 🗂️ 保持云盘目录结构
                    - 📊 按文件类型分类显示
                    """)






elif active_tab == "统计设置":
    st.markdown("### 📊 统计设置")
    st.markdown("#### 📋 JSON配置文件管理")
    st.info("在线查看、搜索、编辑系统配置文件，支持实时修改和保存")
    json_files = {
        "number_prefixes.json": "番号前缀",
        "h_prefix_numbers.json": "h前缀列表",
        "prefix_h_prefix_map.json": "前缀-h映射"
    }
    selected_file = st.selectbox("📁 选择要管理的JSON配置文件", list(json_files.keys()), format_func=lambda x: f"📄 {json_files[x]}")
    json_path = os.path.join(".", selected_file)
    json_data = load_json(json_path)

    # 针对不同结构做表格展示
    if selected_file == "number_prefixes.json" or selected_file == "h_prefix_numbers.json":
        df = pd.DataFrame(json_data, columns=["内容"])
        search_val = st.text_input("🔍 搜索内容", "", placeholder="输入关键词搜索...")
        if search_val:
            df = df[df["内容"].astype(str).str.contains(search_val)]
        st.dataframe(df, use_container_width=True)

        st.markdown("##### ✏️ 编辑操作")
        col1, col2 = st.columns(2)
        with col1:
            new_val = st.text_input("➕ 新增内容", placeholder="输入要添加的内容...")
            if st.button("➕ 添加"):
                if new_val and new_val not in json_data:
                    json_data.append(new_val)
                    msg = save_json(json_path, json_data)
                    st.success(f"✅ {msg}")
                    st.rerun()
                elif not new_val:
                    st.warning("请输入要添加的内容！")
                else:
                    st.warning("该内容已存在！")
        with col2:
            del_val = st.text_input("🗑️ 删除内容", placeholder="输入要删除的内容...")
            if st.button("🗑️ 删除"):
                if del_val in json_data:
                    json_data.remove(del_val)
                    msg = save_json(json_path, json_data)
                    st.success(f"✅ {msg}")
                    st.rerun()
                elif not del_val:
                    st.warning("请输入要删除的内容！")
                else:
                    st.warning("未找到该内容！")
    elif selected_file == "prefix_h_prefix_map.json":
        st.markdown("##### 🔗 前缀-h映射表")
        df = pd.DataFrame([
            {"前缀": k, "h前缀列表": ",".join(map(str, v))}
            for k, v in json_data.items()
        ])
        search_val = st.text_input("🔍 搜索前缀", "", placeholder="输入前缀关键词...")
        if search_val:
            df = df[df["前缀"].str.contains(search_val)]
        st.dataframe(df, use_container_width=True)

        st.markdown("##### ✏️ 编辑映射关系")
        col1, col2 = st.columns(2)
        with col1:
            prefix = st.text_input("📝 前缀", placeholder="例如：IPX")
        with col2:
            h_list = st.text_input("📋 h前缀列表（逗号分隔）", placeholder="例如：1,2,3")
        if st.button("➕ 添加/修改映射"):
            if prefix:
                h_nums = [int(x) for x in h_list.split(",") if x.strip().isdigit()]
                json_data[prefix] = h_nums
                msg = save_json(json_path, json_data)
                st.success(f"✅ {msg}")
                st.rerun()
            else:
                st.warning("请输入前缀！")

        # 删除功能
        st.markdown("---")
        del_prefix = st.text_input("🗑️ 删除前缀", placeholder="输入要删除的前缀...")
        if st.button("🗑️ 删除前缀"):
            if del_prefix in json_data:
                del json_data[del_prefix]
                msg = save_json(json_path, json_data)
                st.success(f"✅ {msg}")
                st.rerun()
            elif not del_prefix:
                st.warning("请输入要删除的前缀！")
            else:
                st.warning("未找到该前缀！")

elif active_tab == "NFO元数据修改":
    st.markdown("## 📝 NFO元数据修改")

    # 激活验证检查
    if not is_feature_enabled("advanced"):
        st.error("🔒 此功能需要激活后使用")
        st.warning("⚠️ NFO元数据修改功能仅对已激活用户开放，请前往【激活管理】输入激活码。")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🔐 前往激活", type="primary"):
                st.session_state.active_tab = "激活管理"
                st.rerun()
        with col2:
            if st.button("🏠 返回首页"):
                st.session_state.active_tab = "首页"
                st.rerun()

        st.markdown("---")
        st.markdown("#### 💡 功能说明")
        st.info("""
        **NFO元数据修改功能包括：**
        - 📝 批量NFO标签编辑
        - 🔧 单独NFO文件修改
        - 🔍 递归目录扫描
        - 📊 嵌套结构支持

        激活后即可使用完整功能！
        """)
        st.stop()

    import xml.etree.ElementTree as ET

    # 标签中英文对照表
    tag_zh_map = {
        "title": "标题", "originaltitle": "原始标题", "sorttitle": "排序标题", "tagline": "副标题",
        "countrycode": "国家代码", "customrating": "自定义分级", "mpaa": "MPAA分级", "year": "年份",
        "plot": "剧情简介", "genre": "类型", "director": "导演", "studio": "制片公司", "premiered": "发行日期",
        "actor": "演员", "thumb": "封面", "fanart": "剧照", "id": "番号", "set": "系列",
        "runtime": "片长", "rating": "评分", "votes": "投票数", "fileinfo": "文件信息", "path": "路径",
        "maker": "制作商", "label": "厂牌", "tag": "标签", "outline": "简介", "releasedate": "发行日期",
        "trailer": "预告片", "art": "图片", "collectionnumber": "收藏编号", "uniqueid": "唯一ID",
        "epbookmark": "书签", "resume": "进度", "watched": "已观看", "playcount": "播放次数",
        "dateadded": "添加日期", "lastplayed": "上次播放", "userrating": "用户评分", "iswatched": "是否已看",
        "studio": "工作室", "writer": "编剧", "aired": "首播日期", "season": "季", "episode": "集",
        "parent": "父级", "child": "子级", "fanarts": "剧照集", "posters": "封面集"
    }
    st.markdown(
        "<span style='color:#ff512f;font-size:18px;font-weight:bold;'>支持递归扫描目录、批量添加、删除、修改nfo标签内容（支持嵌套结构）</span>",
        unsafe_allow_html=True
    )

    tab = st.tabs(["批量标签编辑", "单独nfo文件修改"])

    # ----------- 批量操作区 -----------
    with tab[0]:
        st.markdown("#### <span style='color:#222;background:#eaf3fb;padding:6px 12px;border-radius:6px;'>可先编辑标签内容，后输入路径批量应用到所有NFO文件。</span>", unsafe_allow_html=True)
        if "batch_tags" not in st.session_state:
            st.session_state.batch_tags = [{"tag": "", "value": ""}]
        batch_tags = st.session_state.batch_tags
        for i, item in enumerate(batch_tags):
            cols = st.columns([4, 8, 2])
            with cols[0]:
                tag = st.text_input(f"标签英文名_{i}", value=item["tag"], key=f"batch_tag_{i}")
            with cols[1]:
                value = st.text_input(f"标签内容_{i}", value=item["value"], key=f"batch_value_{i}")
            with cols[2]:
                if st.button("❌ 删除", key=f"del_batch_{i}"):
                    batch_tags.pop(i)
                    st.session_state.batch_tags = batch_tags
                    st.rerun()
            batch_tags[i] = {"tag": tag, "value": value}
        if st.button("➕ 添加标签行"):
            batch_tags.append({"tag": "", "value": ""})
            st.session_state.batch_tags = batch_tags
            st.rerun()

        st.markdown("#### <span style='color:#ff512f;font-weight:bold;'>输入nfo目录后批量应用</span>", unsafe_allow_html=True)
        batch_nfo_dir = st.text_input("批量操作nfo目录（递归扫描所有子目录）", value="", key="batch_nfo_dir")
        if st.button("批量应用到所有nfo"):
            if not batch_nfo_dir or not os.path.exists(batch_nfo_dir):
                st.warning("请先输入有效的nfo目录路径！")
            else:
                nfo_files = []
                for dirpath, _, filenames in os.walk(batch_nfo_dir):
                    for f in filenames:
                        if f.lower().endswith(".nfo"):
                            nfo_files.append(os.path.join(dirpath, f))
                if not nfo_files:
                    st.warning("该目录及子目录下没有nfo文件")
                else:
                    for nfo in nfo_files:
                        try:
                            tree = ET.parse(nfo)
                            root = tree.getroot()
                            for item in batch_tags:
                                tag = item["tag"].strip()
                                value = item["value"]
                                if not tag:
                                    continue
                                elem = root.find(tag)
                                if value == "":
                                    if elem is not None:
                                        root.remove(elem)
                                else:
                                    if elem is not None:
                                        elem.text = value
                                    else:
                                        ET.SubElement(root, tag).text = value
                            tree.write(nfo, encoding="utf-8", xml_declaration=True)
                        except Exception as e:
                            st.error(f"{nfo} 处理失败: {e}")
                    st.success("批量操作完成！")

    # ----------- 单独修改区 -----------
    with tab[1]:
        st.markdown("#### <span style='color:#ff512f;font-weight:bold;'>✏️ 单独nfo文件修改（输入目录后可选择nfo单独编辑）</span>", unsafe_allow_html=True)
        nfo_dir = st.text_input("nfo目录（递归扫描所有子目录）", value="", key="single_nfo_dir")
        nfo_files = []
        if nfo_dir and os.path.exists(nfo_dir):
            for dirpath, _, filenames in os.walk(nfo_dir):
                for f in filenames:
                    if f.lower().endswith(".nfo"):
                        nfo_files.append(os.path.join(dirpath, f))
        elif nfo_dir:
            st.warning("目录不存在")

        if nfo_files:
            selected_nfo = st.selectbox("选择要编辑的nfo文件", nfo_files)
            try:
                tree = ET.parse(selected_nfo)
                root = tree.getroot()
            except Exception as e:
                st.error(f"解析nfo失败: {e}")
                root = None

            def render_xml_inputs(elem, prefix, tag_zh_map, key_prefix):
                inputs = {}
                tag_count = {}
                for idx, child in enumerate(elem):
                    tag = child.tag
                    tag_count[tag] = tag_count.get(tag, 0) + 1
                    label = f"{tag_zh_map.get(tag, tag)}（{tag}）"
                    full_key = f"{key_prefix}_{prefix}_{tag}_{idx}"
                    if len(child):  # 有子元素，递归
                        st.markdown(f"**{label}**")
                        sub_inputs = render_xml_inputs(child, f"{prefix}/{tag}[{tag_count[tag]}]", tag_zh_map, key_prefix)
                        inputs[f"{tag}_{idx}"] = {"_text": child.text or "", "_children": sub_inputs}
                    else:
                        value = st.text_input(label, value=child.text or "", key=full_key)
                        inputs[f"{tag}_{idx}"] = value
                return inputs

            def update_xml_from_inputs(elem, inputs):
                for idx, child in enumerate(elem):
                    tag = child.tag
                    key = f"{tag}_{idx}"
                    if key in inputs:
                        if isinstance(inputs[key], dict) and "_children" in inputs[key]:
                            update_xml_from_inputs(child, inputs[key]["_children"])
                            child.text = inputs[key].get("_text", "")
                        else:
                            child.text = inputs[key]

            if root is not None:
                st.markdown("##### 当前标签内容（支持嵌套结构）")
                tag_inputs = render_xml_inputs(root, root.tag, tag_zh_map, f"nfo_{selected_nfo}")

                st.markdown("##### 添加新标签")
                new_tag = st.text_input("新标签英文名", key=f"new_tag_{selected_nfo}")
                new_val = st.text_input("新标签内容", key=f"new_val_{selected_nfo}")
                if st.button("添加新标签"):
                    if new_tag:
                        tag_inputs[new_tag] = new_val
                        st.rerun()

                st.markdown("##### 删除标签")
                all_tags = [child.tag for child in root]
                del_tag = st.selectbox("选择要删除的标签", [f"{tag_zh_map.get(t, t)}（{t}）" for t in all_tags], key=f"del_tag_{selected_nfo}")
                del_tag_key = all_tags[[f"{tag_zh_map.get(t, t)}（{t}）" for t in all_tags].index(del_tag)] if del_tag else None
                if st.button("删除所选标签"):
                    if del_tag_key and del_tag_key in tag_inputs:
                        tag_inputs.pop(del_tag_key, None)
                        st.rerun()

                if st.button("保存到当前nfo"):
                    try:
                        update_xml_from_inputs(root, tag_inputs)
                        tree.write(selected_nfo, encoding="utf-8", xml_declaration=True)
                        st.success("保存成功！")
                    except Exception as e:
                        st.error(f"保存失败: {e}")
        elif nfo_dir:
            st.info("该目录及子目录下没有nfo文件")
            
# 系统功能区
elif active_tab == "日志显示":
    st.markdown("### 📜 系统日志")
    st.markdown("#### 📋 查看和搜索系统运行日志")
    st.info("实时查看系统运行日志，支持关键词搜索和筛选")

    log_path = "./dmm_debug.log"
    col1, col2 = st.columns([3, 1])
    with col1:
        keyword = st.text_input("🔍 搜索关键字", placeholder="输入关键词筛选日志...")
    with col2:
        st.markdown("**操作：**")
        if st.button("🔄 刷新日志"):
            st.rerun()

    try:
        with open(log_path, "r", encoding="utf-8") as f:
            lines = f.readlines()

        original_count = len(lines)
        if keyword:
            lines = [line for line in lines if keyword in line]
            filtered_count = len(lines)
            st.info(f"📊 共 {original_count} 条日志，筛选后显示 {filtered_count} 条")
        else:
            st.info(f"📊 共 {original_count} 条日志")

        if lines:
            st.text_area("📄 日志内容", "".join(lines), height=400)
        else:
            st.warning("未找到匹配的日志内容")

    except FileNotFoundError:
        st.error("❌ 日志文件不存在")
    except Exception as e:
        st.error(f"❌ 日志读取失败: {e}")
        
elif active_tab == "系统设置":
    st.markdown("### ⚙️ 系统设置")
    st.info("个性化配置系统界面和功能参数")

    tab1, tab2, tab3 = st.tabs(["🎨 背景设置", "⚙️ STRM全局配置", "🔧 其它设置"])

    with tab1:
        st.markdown("#### 🖼️ 自定义背景图片")
        st.info("上传图片或输入图片链接作为背景图，支持自动模糊处理和实时预览")

        # 上传图片
        col1, col2 = st.columns([2, 1])
        with col1:
            uploaded_file = st.file_uploader("📤 上传背景图片", type=["jpg", "jpeg", "png"],
                                           help="支持JPG、PNG格式，建议分辨率1920x1080以上",
                                           key="bg_uploader_main")
        with col2:
            st.markdown("**图片处理：**")
            blur = st.checkbox("🌫️ 模糊处理", value=True, help="自动添加模糊效果，提升界面可读性", key="bg_blur_main")

        if uploaded_file:
            with st.spinner("正在处理图片..."):
                img = Image.open(uploaded_file).convert("RGB")
                if blur:
                    img = img.filter(ImageFilter.GaussianBlur(radius=18))
                # 保存到本地
                bg_path = os.path.join(BG_UPLOAD_DIR, f"bg_{int(time.time())}.jpg")
                img.save(bg_path, "JPEG", quality=85)
                st.session_state["bg_img_path"] = bg_path
                st.success("✅ 背景图片上传成功！")
                st.image(img, caption="🖼️ 当前背景预览", use_container_width=True)

        # 网络图片链接
        st.markdown("---")
        st.markdown("**🔗 或使用网络图片链接**")
        img_url = st.text_input("图片链接", placeholder="https://example.com/image.jpg",
                               help="输入图片的完整URL地址", key="bg_url_input")

        if st.button("🔗 应用网络图片", key="apply_bg_url") and img_url:
            try:
                # 验证链接
                response = requests.head(img_url, timeout=5)
                if response.status_code == 200:
                    st.session_state["bg_img_url"] = img_url
                    st.success("✅ 网络背景图片设置成功！")
                    st.image(img_url, caption="🔗 当前背景（网络链接）", use_container_width=True)
                else:
                    st.error("❌ 无法访问该图片链接")
            except Exception as e:
                st.error(f"❌ 图片链接验证失败: {str(e)}")

        # 背景预览
        st.markdown("---")
        st.markdown("**👀 当前背景预览**")

        if st.session_state.get("bg_img_path"):
            try:
                st.image(st.session_state["bg_img_path"], caption="📁 当前背景（本地文件）", use_container_width=True)
            except:
                st.error("❌ 背景图片文件无效或已删除")
        elif st.session_state.get("bg_img_url"):
            try:
                st.image(st.session_state["bg_img_url"], caption="🔗 当前背景（网络链接）", use_container_width=True)
            except:
                st.error("❌ 背景图片链接无效或无法加载")
        else:
            st.info("💡 当前使用系统默认背景")

        # 清除背景
        st.markdown("---")
        if st.button("🗑️ 清除自定义背景", key="clear_bg_main"):
            st.session_state["bg_img_path"] = ""
            st.session_state["bg_img_url"] = ""
            # 这里可以添加保存配置的逻辑
            if True:  # 假设保存成功
                st.success("✅ 自定义背景已清除，刷新页面恢复默认背景。")
            else:
                st.warning("⚠️ 背景已清除，但配置保存失败，刷新页面后可能会重置。")
            st.rerun()

    with tab2:
        st.markdown("#### ⚙️ STRM全局配置")
        st.info("STRM整理功能的全局配置设置 - 可编辑和保存")

        # 初始化配置管理器
        if 'strm_config_manager' not in st.session_state:
            st.session_state.strm_config_manager = STRMConfigManager()

        config_manager = st.session_state.strm_config_manager

        # 加载当前配置
        if 'strm_config' not in st.session_state:
            st.session_state.strm_config = config_manager.load_config()

        config = st.session_state.strm_config

        # 创建配置编辑界面
        st.markdown("---")

        # 路径配置
        st.markdown("### 📁 路径配置")
        col1, col2 = st.columns(2)

        with col1:
            new_source_dir = st.text_input(
                "📂 默认源目录",
                value=config["paths"]["source_dir"],
                help="视频文件的源目录路径"
            )

            new_strm_dir = st.text_input(
                "🎬 默认STRM目录",
                value=config["paths"]["strm_dir"],
                help="STRM文件的保存目录路径"
            )

        with col2:
            new_url_prefix = st.text_input(
                "🌐 默认URL前缀",
                value=config["paths"]["url_prefix"],
                help="STRM文件中使用的URL前缀"
            )

            new_download_dir = st.text_input(
                "📥 元数据下载目录",
                value=config["paths"]["download_dir"],
                help="元数据文件的下载保存目录"
            )

        # 文件格式配置
        st.markdown("### 📄 文件格式配置")
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**🎬 支持的视频格式:**")
            video_exts_text = st.text_area(
                "视频文件扩展名 (每行一个)",
                value="\n".join(config["file_formats"]["video_exts"]),
                height=120,
                help="支持的视频文件扩展名，每行一个"
            )

        with col2:
            st.markdown("**📋 支持的元数据格式:**")
            meta_exts_text = st.text_area(
                "元数据文件扩展名 (每行一个)",
                value="\n".join(config["file_formats"]["meta_exts"]),
                height=120,
                help="支持的元数据文件扩展名，每行一个"
            )

        # 扫描设置
        st.markdown("### 🔍 扫描设置")
        col1, col2, col3 = st.columns(3)

        with col1:
            new_max_depth = st.number_input(
                "📊 最大扫描深度",
                min_value=1,
                max_value=50,
                value=config["scan_settings"]["max_depth"],
                help="递归扫描的最大深度"
            )

        with col2:
            new_recursive_scan = st.checkbox(
                "🔄 默认递归扫描",
                value=config["scan_settings"]["recursive_scan"],
                help="是否默认启用递归扫描"
            )

        with col3:
            new_preserve_structure = st.checkbox(
                "🗂️ 默认保持目录结构",
                value=config["scan_settings"]["preserve_structure"],
                help="是否默认保持目录结构"
            )

        # 高级设置
        st.markdown("### ⚙️ 高级设置")
        col1, col2, col3 = st.columns(3)

        with col1:
            new_concurrent_downloads = st.number_input(
                "🚀 并发下载数",
                min_value=1,
                max_value=10,
                value=config["advanced"]["concurrent_downloads"],
                help="同时进行的下载任务数量"
            )

        with col2:
            new_timeout_seconds = st.number_input(
                "⏱️ 超时时间(秒)",
                min_value=10,
                max_value=300,
                value=config["advanced"]["timeout_seconds"],
                help="网络请求的超时时间"
            )

        with col3:
            new_retry_attempts = st.number_input(
                "🔄 重试次数",
                min_value=1,
                max_value=10,
                value=config["advanced"]["retry_attempts"],
                help="下载失败时的重试次数"
            )

        # 保存和重置按钮
        st.markdown("---")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("💾 保存配置", type="primary", use_container_width=True):
                # 更新配置
                config["paths"]["source_dir"] = new_source_dir
                config["paths"]["strm_dir"] = new_strm_dir
                config["paths"]["url_prefix"] = new_url_prefix
                config["paths"]["download_dir"] = new_download_dir

                # 处理文件格式
                config["file_formats"]["video_exts"] = [ext.strip() for ext in video_exts_text.split('\n') if ext.strip()]
                config["file_formats"]["meta_exts"] = [ext.strip() for ext in meta_exts_text.split('\n') if ext.strip()]

                config["scan_settings"]["max_depth"] = new_max_depth
                config["scan_settings"]["recursive_scan"] = new_recursive_scan
                config["scan_settings"]["preserve_structure"] = new_preserve_structure

                config["advanced"]["concurrent_downloads"] = new_concurrent_downloads
                config["advanced"]["timeout_seconds"] = new_timeout_seconds
                config["advanced"]["retry_attempts"] = new_retry_attempts

                # 保存到文件
                if config_manager.save_config(config):
                    st.session_state.strm_config = config
                    st.success("✅ 配置保存成功！")
                    st.rerun()

        with col2:
            if st.button("🔄 重置为默认", use_container_width=True):
                default_config = config_manager.default_config.copy()
                st.session_state.strm_config = default_config
                if config_manager.save_config(default_config):
                    st.success("✅ 配置已重置为默认值！")
                    st.rerun()

        with col3:
            if st.button("📂 打开配置文件", use_container_width=True):
                try:
                    import subprocess
                    import platform

                    config_path = os.path.abspath(config_manager.config_file)

                    if platform.system() == "Windows":
                        subprocess.run(["notepad", config_path])
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", "-t", config_path])
                    else:  # Linux
                        subprocess.run(["xdg-open", config_path])

                    st.success(f"📂 已打开配置文件: {config_path}")
                except Exception as e:
                    st.error(f"❌ 打开配置文件失败: {e}")

        with col4:
            if st.button("📋 导出配置", use_container_width=True):
                config_json = json.dumps(config, ensure_ascii=False, indent=2)
                st.download_button(
                    label="📥 下载配置文件",
                    data=config_json,
                    file_name=f"strm_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )

        # 配置预览
        st.markdown("---")
        st.markdown("### 📋 当前配置预览")

        if st.checkbox("🔍 显示完整配置", help="展开显示完整的配置信息"):
            st.json(config)
        else:
            # 简化显示
            st.markdown("**📁 路径配置:**")
            st.write(f"- 源目录: `{config['paths']['source_dir']}`")
            st.write(f"- STRM目录: `{config['paths']['strm_dir']}`")
            st.write(f"- URL前缀: `{config['paths']['url_prefix']}`")

            st.markdown("**📄 文件格式:**")
            st.write(f"- 视频格式: {len(config['file_formats']['video_exts'])} 种")
            st.write(f"- 元数据格式: {len(config['file_formats']['meta_exts'])} 种")

            st.markdown("**⚙️ 其他设置:**")
            st.write(f"- 最大扫描深度: {config['scan_settings']['max_depth']}")
            st.write(f"- 并发下载数: {config['advanced']['concurrent_downloads']}")

        # 使用说明
        st.markdown("---")
        st.markdown("### 💡 使用说明")
        st.markdown("""
        **📝 配置说明:**
        - **路径配置**: 设置各种功能使用的默认路径
        - **文件格式**: 定义支持的视频和元数据文件格式
        - **扫描设置**: 控制文件扫描的行为
        - **高级设置**: 性能和网络相关的参数

        **🔧 操作说明:**
        - 修改配置后点击"保存配置"生效
        - "重置为默认"会恢复所有设置到初始状态
        - "导出配置"可以备份当前配置
        - 配置文件保存在: `strm_config.json`
        """)

    with tab3:
        st.markdown("#### 🔧 其他系统配置")
        st.info("更多系统设置功能正在开发中...")

        # 安全模式状态显示
        st.markdown("##### 🔒 安全模式")
        if st.session_state.get("safe_mode", False):
            st.success("🔒 安全模式已启用")
            if st.button("关闭安全模式"):
                st.session_state.safe_mode = False
                st.rerun()
        else:
            st.info("🔓 安全模式已关闭")
            if st.button("启用安全模式"):
                st.session_state.safe_mode = True
                st.rerun()

        # 系统信息
        st.markdown("##### 📊 系统信息")
        st.write(f"**Python版本：** {st.__version__}")
        st.write(f"**当前主题：** {'暗色' if st.get_option('theme.base') == 'dark' else '浅色'}")
        st.write(f"**会话状态：** {len(st.session_state)} 个变量")

elif active_tab == "实时监控":
    st.markdown("### 📡 实时监控设置")

    # 激活验证检查
    if not is_feature_enabled("monitor"):
        st.error("🔒 此功能需要激活后使用")
        st.warning("⚠️ 实时监控功能仅对已激活用户开放，请前往【激活管理】输入激活码。")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🔐 前往激活", type="primary"):
                st.session_state.active_tab = "激活管理"
                st.rerun()
        with col2:
            if st.button("🏠 返回首页"):
                st.session_state.active_tab = "首页"
                st.rerun()

        st.markdown("---")
        st.markdown("#### 💡 功能说明")
        st.info("""
        **实时监控功能包括：**
        - 🖥️ CD2本地目录实时监控
        - ☁️ 115云盘变化监控
        - 🗂️ 网盘文件整理监控
        - 📊 监控状态统计和日志

        激活后即可使用完整功能！
        """)
        st.stop()

    st.info("配置和管理CD2、115云盘、网盘整理等实时监控功能")

    # 初始化监控管理器
    if 'monitor_manager' not in st.session_state:
        st.session_state.monitor_manager = RealTimeMonitorManager()

    monitor_manager = st.session_state.monitor_manager

    # 加载配置
    if 'monitor_config' not in st.session_state:
        st.session_state.monitor_config = monitor_manager.load_config()

    config = st.session_state.monitor_config

    # 创建三个标签页
    tab1, tab2, tab3 = st.tabs(["🖥️ CD2实时监控", "☁️ 115实时监控", "🗂️ 网盘整理监控"])

    with tab1:
        st.markdown("#### 🖥️ CD2实时监控")
        st.info("监控本地CD2目录，自动处理新增文件并生成STRM")

        # 监控状态显示
        status = monitor_manager.get_monitor_status()
        cd2_status = status["cd2_monitor"]

        col1, col2, col3 = st.columns(3)
        with col1:
            status_color = "🟢" if cd2_status["running"] else "🔴"
            st.metric("监控状态", f"{status_color} {'运行中' if cd2_status['running'] else '已停止'}")
        with col2:
            st.metric("已处理文件", cd2_status["files_processed"])
        with col3:
            last_scan = cd2_status["last_scan"] or "从未扫描"
            st.metric("最后扫描", last_scan)

        # 配置设置
        st.markdown("**📁 路径配置**")
        col1, col2 = st.columns(2)

        with col1:
            new_source_dir = st.text_input(
                "监控源目录",
                value=config["cd2_monitor"]["source_dir"],
                help="需要监控的文件夹路径"
            )

            new_strm_dir = st.text_input(
                "STRM输出目录",
                value=config["cd2_monitor"]["strm_dir"],
                help="STRM文件的保存目录"
            )

        with col2:
            new_library_dir = st.text_input(
                "媒体库目录",
                value=config["cd2_monitor"]["library_dir"],
                help="最终媒体库存储目录"
            )

            new_cloud_url = st.text_input(
                "云盘URL前缀",
                value=config["cd2_monitor"]["cloud_url"],
                help="STRM文件中使用的URL前缀"
            )

        # 高级设置
        st.markdown("**⚙️ 高级设置**")
        col1, col2, col3 = st.columns(3)

        with col1:
            new_scan_interval = st.number_input(
                "扫描间隔(秒)",
                min_value=1,
                max_value=300,
                value=config["cd2_monitor"]["scan_interval"],
                help="文件夹扫描的时间间隔"
            )

        with col2:
            new_max_workers = st.number_input(
                "并发处理数",
                min_value=1,
                max_value=20,
                value=config["cd2_monitor"]["max_workers"],
                help="同时处理文件的线程数"
            )

        with col3:
            cd2_enabled = st.checkbox(
                "启用CD2监控",
                value=config["cd2_monitor"]["enabled"],
                help="是否启用CD2实时监控功能"
            )

        # 控制按钮
        st.markdown("---")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("▶️ 启动监控", type="primary", use_container_width=True):
                if cd2_enabled:
                    # 更新配置
                    config["cd2_monitor"]["source_dir"] = new_source_dir
                    config["cd2_monitor"]["strm_dir"] = new_strm_dir
                    config["cd2_monitor"]["library_dir"] = new_library_dir
                    config["cd2_monitor"]["cloud_url"] = new_cloud_url
                    config["cd2_monitor"]["scan_interval"] = new_scan_interval
                    config["cd2_monitor"]["max_workers"] = new_max_workers
                    config["cd2_monitor"]["enabled"] = cd2_enabled

                    result = monitor_manager.start_cd2_monitor(config["cd2_monitor"])
                    if result["success"]:
                        st.success(f"✅ {result['message']}")
                        monitor_manager.save_config(config)
                        st.rerun()
                    else:
                        st.error(f"❌ {result['error']}")
                else:
                    st.warning("⚠️ 请先启用CD2监控功能")

        with col2:
            if st.button("⏹️ 停止监控", use_container_width=True):
                result = monitor_manager.stop_cd2_monitor()
                if result["success"]:
                    st.success(f"✅ {result['message']}")
                    st.rerun()
                else:
                    st.error(f"❌ {result['error']}")

        with col3:
            if st.button("💾 保存配置", use_container_width=True):
                # 保存配置
                config["cd2_monitor"]["source_dir"] = new_source_dir
                config["cd2_monitor"]["strm_dir"] = new_strm_dir
                config["cd2_monitor"]["library_dir"] = new_library_dir
                config["cd2_monitor"]["cloud_url"] = new_cloud_url
                config["cd2_monitor"]["scan_interval"] = new_scan_interval
                config["cd2_monitor"]["max_workers"] = new_max_workers
                config["cd2_monitor"]["enabled"] = cd2_enabled

                if monitor_manager.save_config(config):
                    st.session_state.monitor_config = config
                    st.success("✅ 配置保存成功！")
                else:
                    st.error("❌ 配置保存失败")

    with tab2:
        st.markdown("#### ☁️ 115实时监控")
        st.info("监控115云盘变化，自动同步文件和元数据")

        # 监控状态显示
        cloud115_status = status["cloud115_monitor"]

        col1, col2, col3 = st.columns(3)
        with col1:
            status_color = "🟢" if cloud115_status["running"] else "🔴"
            st.metric("监控状态", f"{status_color} {'运行中' if cloud115_status['running'] else '已停止'}")
        with col2:
            st.metric("已同步文件", cloud115_status["files_synced"])
        with col3:
            last_scan = cloud115_status["last_scan"] or "从未扫描"
            st.metric("最后扫描", last_scan)

        # 115云盘配置
        st.markdown("**☁️ 115云盘设置**")
        col1, col2 = st.columns(2)

        with col1:
            new_folder_id = st.text_input(
                "监控文件夹ID",
                value=config["cloud115_monitor"]["folder_id"],
                help="要监控的115云盘文件夹ID，0为根目录"
            )

            new_115_interval = st.number_input(
                "扫描间隔(秒)",
                min_value=10,
                max_value=3600,
                value=config["cloud115_monitor"]["scan_interval"],
                help="115云盘扫描的时间间隔"
            )

        with col2:
            new_local_sync_dir = st.text_input(
                "本地同步目录",
                value=config["cloud115_monitor"]["local_sync_dir"],
                help="本地同步文件的保存目录"
            )

            cloud115_enabled = st.checkbox(
                "启用115监控",
                value=config["cloud115_monitor"]["enabled"],
                help="是否启用115云盘实时监控功能"
            )

        # 同步选项
        st.markdown("**📥 同步选项**")
        col1, col2 = st.columns(2)

        with col1:
            auto_download = st.checkbox(
                "自动下载新文件",
                value=config["cloud115_monitor"]["auto_download"],
                help="是否自动下载新增的文件"
            )

        with col2:
            download_types = st.multiselect(
                "下载文件类型",
                options=["video", "metadata", "image", "subtitle"],
                default=config["cloud115_monitor"]["download_types"],
                help="选择要自动下载的文件类型"
            )

        # 控制按钮
        st.markdown("---")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("▶️ 启动115监控", type="primary", use_container_width=True):
                if cloud115_enabled:
                    # 检查115登录状态
                    if 'cloud115_auth' in st.session_state and st.session_state.cloud115_auth.is_logged_in():
                        # 更新配置
                        config["cloud115_monitor"]["folder_id"] = new_folder_id
                        config["cloud115_monitor"]["scan_interval"] = new_115_interval
                        config["cloud115_monitor"]["local_sync_dir"] = new_local_sync_dir
                        config["cloud115_monitor"]["auto_download"] = auto_download
                        config["cloud115_monitor"]["download_types"] = download_types
                        config["cloud115_monitor"]["enabled"] = cloud115_enabled

                        p115_client = getattr(st.session_state.cloud115_auth, 'p115_client', None)
                        result = monitor_manager.start_115_monitor(config["cloud115_monitor"], p115_client)

                        if result["success"]:
                            st.success(f"✅ {result['message']}")
                            monitor_manager.save_config(config)
                            st.rerun()
                        else:
                            st.error(f"❌ {result['error']}")
                    else:
                        st.error("❌ 请先在115云盘管理中完成登录")
                else:
                    st.warning("⚠️ 请先启用115监控功能")

        with col2:
            if st.button("⏹️ 停止115监控", use_container_width=True):
                result = monitor_manager.stop_115_monitor()
                if result["success"]:
                    st.success(f"✅ {result['message']}")
                    st.rerun()
                else:
                    st.error(f"❌ {result['error']}")

        with col3:
            if st.button("💾 保存115配置", use_container_width=True):
                # 保存配置
                config["cloud115_monitor"]["folder_id"] = new_folder_id
                config["cloud115_monitor"]["scan_interval"] = new_115_interval
                config["cloud115_monitor"]["local_sync_dir"] = new_local_sync_dir
                config["cloud115_monitor"]["auto_download"] = auto_download
                config["cloud115_monitor"]["download_types"] = download_types
                config["cloud115_monitor"]["enabled"] = cloud115_enabled

                if monitor_manager.save_config(config):
                    st.session_state.monitor_config = config
                    st.success("✅ 115配置保存成功！")
                else:
                    st.error("❌ 115配置保存失败")

    with tab3:
        st.markdown("#### 🗂️ 网盘整理监控")
        st.info("监控网盘存储变化，自动整理和分类文件")

        # 监控状态显示
        storage_status = status["storage_monitor"]

        col1, col2, col3 = st.columns(3)
        with col1:
            status_color = "🟢" if storage_status["running"] else "🔴"
            st.metric("监控状态", f"{status_color} {'运行中' if storage_status['running'] else '已停止'}")
        with col2:
            st.metric("已整理文件夹", storage_status["folders_organized"])
        with col3:
            last_scan = storage_status["last_scan"] or "从未扫描"
            st.metric("最后扫描", last_scan)

        # 整理设置
        st.markdown("**🗂️ 整理设置**")
        col1, col2 = st.columns(2)

        with col1:
            new_storage_interval = st.number_input(
                "扫描间隔(秒)",
                min_value=30,
                max_value=7200,
                value=config["storage_monitor"]["scan_interval"],
                help="网盘整理扫描的时间间隔"
            )

            storage_enabled = st.checkbox(
                "启用网盘整理监控",
                value=config["storage_monitor"]["enabled"],
                help="是否启用网盘整理实时监控功能"
            )

        with col2:
            auto_organize = st.checkbox(
                "自动整理文件",
                value=config["storage_monitor"]["auto_organize"],
                help="是否自动整理检测到的文件"
            )

        # 整理规则
        st.markdown("**📋 整理规则**")
        col1, col2, col3 = st.columns(3)

        with col1:
            organize_by_date = st.checkbox(
                "按日期整理",
                value=config["storage_monitor"]["organize_rules"]["by_date"],
                help="按文件创建日期进行分类整理"
            )

        with col2:
            organize_by_type = st.checkbox(
                "按类型整理",
                value=config["storage_monitor"]["organize_rules"]["by_type"],
                help="按文件类型进行分类整理"
            )

        with col3:
            organize_by_size = st.checkbox(
                "按大小整理",
                value=config["storage_monitor"]["organize_rules"]["by_size"],
                help="按文件大小进行分类整理"
            )

        # 通知设置
        st.markdown("**📢 通知设置**")

        # Telegram通知
        st.markdown("**📱 Telegram通知**")
        col1, col2 = st.columns(2)

        with col1:
            telegram_enabled = st.checkbox(
                "启用Telegram通知",
                value=config["notifications"]["telegram"]["enabled"],
                help="是否启用Telegram机器人通知"
            )

            telegram_bot_token = st.text_input(
                "Bot Token",
                value=config["notifications"]["telegram"]["bot_token"],
                type="password",
                help="Telegram机器人的Token"
            )

        with col2:
            telegram_chat_id = st.text_input(
                "Chat ID",
                value=config["notifications"]["telegram"]["chat_id"],
                help="接收通知的Chat ID"
            )

            telegram_template = st.text_area(
                "消息模板",
                value=config["notifications"]["telegram"]["message_template"],
                height=100,
                help="通知消息的模板，支持{media_list}等变量"
            )

        # Emby通知
        st.markdown("**🎬 Emby媒体库刷新**")
        col1, col2 = st.columns(2)

        with col1:
            emby_enabled = st.checkbox(
                "启用Emby刷新",
                value=config["notifications"]["emby"]["enabled"],
                help="是否自动刷新Emby媒体库"
            )

            emby_server_url = st.text_input(
                "Emby服务器地址",
                value=config["notifications"]["emby"]["server_url"],
                help="Emby服务器的URL地址"
            )

        with col2:
            emby_api_key = st.text_input(
                "API Key",
                value=config["notifications"]["emby"]["api_key"],
                type="password",
                help="Emby服务器的API密钥"
            )

            emby_auto_refresh = st.checkbox(
                "自动刷新媒体库",
                value=config["notifications"]["emby"]["auto_refresh"],
                help="文件变化时自动刷新Emby媒体库"
            )

        # 控制按钮
        st.markdown("---")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("▶️ 启动整理监控", type="primary", use_container_width=True):
                if storage_enabled:
                    # 更新配置
                    config["storage_monitor"]["scan_interval"] = new_storage_interval
                    config["storage_monitor"]["auto_organize"] = auto_organize
                    config["storage_monitor"]["organize_rules"]["by_date"] = organize_by_date
                    config["storage_monitor"]["organize_rules"]["by_type"] = organize_by_type
                    config["storage_monitor"]["organize_rules"]["by_size"] = organize_by_size
                    config["storage_monitor"]["enabled"] = storage_enabled

                    # 更新通知配置
                    config["notifications"]["telegram"]["enabled"] = telegram_enabled
                    config["notifications"]["telegram"]["bot_token"] = telegram_bot_token
                    config["notifications"]["telegram"]["chat_id"] = telegram_chat_id
                    config["notifications"]["telegram"]["message_template"] = telegram_template

                    config["notifications"]["emby"]["enabled"] = emby_enabled
                    config["notifications"]["emby"]["server_url"] = emby_server_url
                    config["notifications"]["emby"]["api_key"] = emby_api_key
                    config["notifications"]["emby"]["auto_refresh"] = emby_auto_refresh

                    result = monitor_manager.start_storage_monitor(config["storage_monitor"])
                    if result["success"]:
                        st.success(f"✅ {result['message']}")
                        monitor_manager.save_config(config)
                        st.rerun()
                    else:
                        st.error(f"❌ {result['error']}")
                else:
                    st.warning("⚠️ 请先启用网盘整理监控功能")

        with col2:
            if st.button("⏹️ 停止整理监控", use_container_width=True):
                result = monitor_manager.stop_storage_monitor()
                if result["success"]:
                    st.success(f"✅ {result['message']}")
                    st.rerun()
                else:
                    st.error(f"❌ {result['error']}")

        with col3:
            if st.button("📱 测试Telegram", use_container_width=True):
                if telegram_enabled and telegram_bot_token and telegram_chat_id:
                    test_message = "🧪 这是一条测试消息\n\n📡 实时监控系统正常工作"
                    result = monitor_manager.send_telegram_notification(test_message, config)
                    if result["success"]:
                        st.success(f"✅ {result['message']}")
                    else:
                        st.error(f"❌ {result['error']}")
                else:
                    st.warning("⚠️ 请先配置Telegram通知设置")

        with col4:
            if st.button("🎬 测试Emby刷新", use_container_width=True):
                if emby_enabled and emby_server_url and emby_api_key:
                    result = monitor_manager.refresh_emby_library(config)
                    if result["success"]:
                        st.success(f"✅ {result['message']}")
                    else:
                        st.error(f"❌ {result['error']}")
                else:
                    st.warning("⚠️ 请先配置Emby设置")

        # 保存配置按钮
        st.markdown("---")
        if st.button("💾 保存所有配置", type="primary", use_container_width=True):
            # 保存所有配置
            config["storage_monitor"]["scan_interval"] = new_storage_interval
            config["storage_monitor"]["auto_organize"] = auto_organize
            config["storage_monitor"]["organize_rules"]["by_date"] = organize_by_date
            config["storage_monitor"]["organize_rules"]["by_type"] = organize_by_type
            config["storage_monitor"]["organize_rules"]["by_size"] = organize_by_size
            config["storage_monitor"]["enabled"] = storage_enabled

            config["notifications"]["telegram"]["enabled"] = telegram_enabled
            config["notifications"]["telegram"]["bot_token"] = telegram_bot_token
            config["notifications"]["telegram"]["chat_id"] = telegram_chat_id
            config["notifications"]["telegram"]["message_template"] = telegram_template

            config["notifications"]["emby"]["enabled"] = emby_enabled
            config["notifications"]["emby"]["server_url"] = emby_server_url
            config["notifications"]["emby"]["api_key"] = emby_api_key
            config["notifications"]["emby"]["auto_refresh"] = emby_auto_refresh

            if monitor_manager.save_config(config):
                st.session_state.monitor_config = config
                st.success("✅ 所有配置保存成功！")
            else:
                st.error("❌ 配置保存失败")

elif active_tab == "激活管理":
    st.markdown("### 🔐 激活管理")
    st.info("管理软件激活状态，输入激活码解锁完整功能")

    # 激活验证逻辑
    import hashlib
    import datetime
    import json

    # 激活码配置
    ACTIVATION_FILE = "activation_config.json"
    ACTIVATION_RECORDS_FILE = "activation_records.json"  # 记录激活码使用情况

    # 预设的有效激活码（统一为7天试用）
    VALID_ACTIVATION_CODES = {
        "MMA-TRIAL-7DAYS": {
            "type": "trial",
            "trial_days": 7,
            "features": ["all"],
            "description": "7天完整功能试用",
            "max_devices": 1  # 最多激活设备数
        }
    }

    def load_activation_status():
        """加载激活状态"""
        try:
            if os.path.exists(ACTIVATION_FILE):
                with open(ACTIVATION_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        return {"activated": False, "code": "", "type": "", "expires": "", "activated_date": ""}

    def save_activation_status(status):
        """保存激活状态"""
        try:
            with open(ACTIVATION_FILE, 'w', encoding='utf-8') as f:
                json.dump(status, f, ensure_ascii=False, indent=2)
            return True
        except Exception:
            return False

    def validate_activation_code(code):
        """验证激活码"""
        if code in VALID_ACTIVATION_CODES:
            config = VALID_ACTIVATION_CODES[code]
            machine_id = generate_machine_id()

            # 检查激活码使用情况
            can_use, message = check_activation_code_usage(code, machine_id)
            if not can_use:
                return False, {"error": message}

            # 对于试用版，检查是否已有激活记录
            if "trial_days" in config:
                records = load_activation_records()
                code_record = records.get(code, {})

                # 如果是同一设备重新激活，使用原始激活时间
                if machine_id in code_record.get("devices", []):
                    first_activation = code_record.get("first_activation")
                    if first_activation:
                        # 使用首次激活时间，不重置试用期
                        config_copy = config.copy()
                        config_copy["preserve_activation_time"] = first_activation
                        return True, config_copy

                # 新设备激活
                return True, config
            else:
                # 固定到期时间的激活码
                try:
                    expire_date = datetime.datetime.strptime(config["expires"], "%Y-%m-%d")
                    if datetime.datetime.now() <= expire_date:
                        return True, config
                    else:
                        return False, {"error": "激活码已过期"}
                except Exception:
                    return False, {"error": "激活码格式错误"}
        return False, {"error": "无效的激活码"}

    def generate_machine_id():
        """生成机器唯一标识"""
        import platform
        machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
        return hashlib.md5(machine_info.encode()).hexdigest()[:16]

    def load_activation_records():
        """加载激活记录"""
        try:
            if os.path.exists(ACTIVATION_RECORDS_FILE):
                with open(ACTIVATION_RECORDS_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        return {}

    def save_activation_records(records):
        """保存激活记录"""
        try:
            with open(ACTIVATION_RECORDS_FILE, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
            return True
        except Exception:
            return False

    def check_activation_code_usage(code, machine_id):
        """检查激活码使用情况"""
        records = load_activation_records()
        code_record = records.get(code, {})

        # 检查是否已被其他设备使用
        if code_record:
            used_devices = code_record.get("devices", [])
            if used_devices and machine_id not in used_devices:
                # 检查设备数量限制
                config = VALID_ACTIVATION_CODES.get(code, {})
                max_devices = config.get("max_devices", 1)
                if len(used_devices) >= max_devices:
                    return False, f"激活码已在其他设备使用，最多支持{max_devices}台设备"

        return True, "可以使用"

    def record_activation_usage(code, machine_id, activation_time):
        """记录激活码使用情况"""
        records = load_activation_records()

        if code not in records:
            records[code] = {
                "first_activation": activation_time,
                "devices": [],
                "activation_count": 0
            }

        code_record = records[code]

        # 记录设备信息
        if machine_id not in code_record["devices"]:
            code_record["devices"].append(machine_id)

        # 更新激活次数
        code_record["activation_count"] += 1
        code_record["last_activation"] = activation_time

        # 保存记录
        save_activation_records(records)
        return code_record

    # 加载当前激活状态
    if 'activation_status' not in st.session_state:
        st.session_state.activation_status = load_activation_status()

    activation_status = st.session_state.activation_status

    # 确保激活状态在session_state中正确保存
    if activation_status.get("activated", False):
        # 验证激活状态是否仍然有效
        is_valid, current_status = get_current_activation_status()
        if is_valid:
            st.session_state.activation_status = current_status
            activation_status = current_status

    # 显示当前激活状态
    st.markdown("---")
    st.markdown("### 📋 当前激活状态")

    if activation_status.get("activated", False):
        # 已激活状态 - 优化UI显示
        activation_type = activation_status.get('type', '未知').upper()

        # 根据激活类型显示不同的成功消息
        if activation_type == "TRIAL":
            st.success("🎉 试用版已激活 - 享受7天完整功能体验！")
        else:
            st.success("✅ 软件已激活")

        # 美化的信息卡片
        st.markdown("---")

        # 激活信息展示
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("#### 📋 激活信息")
            activation_desc = activation_status.get('description', '')
            if activation_desc:
                st.info(f"🏷️ **{activation_desc}**")
            else:
                st.info(f"🏷️ **{activation_type}版本**")

            st.write(f"**激活日期：** {activation_status.get('activated_date', '未知')}")

        with col2:
            st.markdown("#### ⏰ 有效期信息")

            # 计算剩余时间
            try:
                if activation_status.get('type') == 'trial':
                    # 试用版显示精确的剩余时间
                    expire_datetime_str = activation_status.get('expires_datetime', '')
                    if expire_datetime_str:
                        expire_datetime = datetime.datetime.strptime(expire_datetime_str, "%Y-%m-%d %H:%M:%S")
                    else:
                        expire_date = datetime.datetime.strptime(activation_status.get('expires', ''), "%Y-%m-%d")
                        expire_datetime = expire_date.replace(hour=23, minute=59, second=59)

                    now = datetime.datetime.now()
                    time_left = expire_datetime - now

                    if time_left.total_seconds() > 0:
                        days_left = time_left.days
                        hours_left = time_left.seconds // 3600

                        if days_left > 0:
                            st.success(f"⏳ 还有 **{days_left}天{hours_left}小时**")
                        else:
                            st.warning(f"⚠️ 还有 **{hours_left}小时** 到期")

                        # 进度条显示
                        total_seconds = 7 * 24 * 3600  # 7天总秒数
                        remaining_seconds = time_left.total_seconds()
                        progress = max(0, remaining_seconds / total_seconds)

                        st.progress(progress)
                        st.caption(f"试用进度：{(1-progress)*100:.1f}% 已使用")
                    else:
                        st.error("❌ 试用期已过期")
                else:
                    # 其他类型激活码
                    expire_date = datetime.datetime.strptime(activation_status.get('expires', ''), "%Y-%m-%d")
                    days_left = (expire_date - datetime.datetime.now()).days

                    if days_left > 30:
                        st.success(f"📅 还有 **{days_left}天** 到期")
                    elif days_left > 0:
                        st.warning(f"⚠️ 还有 **{days_left}天** 到期")
                    else:
                        st.error("❌ 激活已过期")

            except Exception as e:
                st.warning("⚠️ 无法计算到期时间")
                st.caption(f"错误：{str(e)}")

            st.write(f"**到期日期：** {activation_status.get('expires', '未知')}")

        with col3:
            st.markdown("#### 🔐 安全信息")
            machine_id = generate_machine_id()
            st.write(f"**机器ID：** `{machine_id}`")
            st.write(f"**激活码：** `{activation_status.get('code', '未知')}`")

            # 功能权限显示
            features = activation_status.get('features', [])
            if 'all' in features:
                st.success("🎯 **完整功能权限**")
            else:
                st.info(f"🔧 **限制功能：** {', '.join(features)}")

        # 重新激活按钮
        st.markdown("---")
        if st.button("🔄 重新激活", help="使用新的激活码重新激活"):
            st.session_state.activation_status = {"activated": False, "code": "", "type": "", "expires": "", "activated_date": ""}
            save_activation_status(st.session_state.activation_status)
            st.rerun()

    else:
        # 未激活状态 - 优化UI显示
        st.markdown("""
        <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #ff512f, #dd2476); border-radius: 10px; margin: 20px 0;">
            <h2 style="color: white; margin: 0;">🔐 软件未激活</h2>
            <p style="color: white; margin: 10px 0; opacity: 0.9;">获取7天完整功能试用，体验所有高级特性</p>
        </div>
        """, unsafe_allow_html=True)

        # 功能对比展示
        st.markdown("### 🆚 功能对比")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("""
            <div style="padding: 15px; border: 2px solid #dc3545; border-radius: 8px; background: #fff5f5;">
                <h4 style="color: #dc3545; margin-top: 0;">❌ 未激活状态</h4>
                <ul style="color: #666;">
                    <li>🔒 STRM整理功能受限</li>
                    <li>🔒 实时监控功能受限</li>
                    <li>🔒 NFO编辑功能受限</li>
                    <li>🔒 高级工具不可用</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div style="padding: 15px; border: 2px solid #28a745; border-radius: 8px; background: #f8fff8;">
                <h4 style="color: #28a745; margin-top: 0;">✅ 激活后功能</h4>
                <ul style="color: #666;">
                    <li>🎬 完整STRM管理功能</li>
                    <li>📡 实时监控和同步</li>
                    <li>🛠️ NFO批量编辑工具</li>
                    <li>🚀 所有高级功能解锁</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        # 激活码输入区域
        st.markdown("---")
        st.markdown("### 🎯 立即激活，开启完整体验")

        # 突出显示激活码
        st.info("💡 **试用激活码：** `MMA-TRIAL-7DAYS` （7天完整功能试用）")

        col1, col2 = st.columns([2, 1])

        with col1:
            activation_code = st.text_input(
                "🔑 输入激活码",
                placeholder="MMA-TRIAL-7DAYS",
                help="输入激活码获取7天完整功能试用",
                key="activation_input"
            )

        with col2:
            st.markdown("<br>", unsafe_allow_html=True)  # 对齐按钮
            if st.button("🚀 立即激活", type="primary", use_container_width=True):
                if activation_code.strip():
                    is_valid, config = validate_activation_code(activation_code.strip())

                    if is_valid:
                        # 激活成功
                        machine_id = generate_machine_id()
                        current_time = datetime.datetime.now()

                        # 检查是否保持原始激活时间（重新激活情况）
                        if config.get("preserve_activation_time"):
                            # 使用原始激活时间
                            activated_datetime = datetime.datetime.strptime(config["preserve_activation_time"], "%Y-%m-%d %H:%M:%S")
                            activation_message = "🔄 重新激活成功！试用期从首次激活时间开始计算。"
                        else:
                            # 新激活
                            activated_datetime = current_time
                            activation_message = "🎉 试用激活成功！您现在可以体验7天完整功能！"

                            # 记录激活使用情况
                            record_activation_usage(activation_code.strip(), machine_id, activated_datetime.strftime("%Y-%m-%d %H:%M:%S"))

                        new_status = {
                            "activated": True,
                            "code": activation_code.strip(),
                            "type": config["type"],
                            "activated_date": activated_datetime.strftime("%Y-%m-%d %H:%M:%S"),
                            "machine_id": machine_id,
                            "features": config["features"],
                            "description": config.get("description", "")
                        }

                        # 处理试用版到期时间
                        if config.get("trial_days"):
                            new_status["trial_days"] = config["trial_days"]
                            expire_datetime = activated_datetime + datetime.timedelta(days=config["trial_days"])
                            new_status["expires"] = expire_datetime.strftime("%Y-%m-%d")
                            new_status["expires_datetime"] = expire_datetime.strftime("%Y-%m-%d %H:%M:%S")
                        else:
                            new_status["expires"] = config["expires"]

                        if save_activation_status(new_status):
                            st.session_state.activation_status = new_status

                            # 强制更新全局激活状态
                            st.session_state.force_activation_refresh = True

                            # 优化激活成功提示
                            if config.get("trial_days"):
                                st.success(activation_message)
                                if config.get("preserve_activation_time"):
                                    # 计算剩余时间
                                    expire_datetime = datetime.datetime.strptime(new_status["expires_datetime"], "%Y-%m-%d %H:%M:%S")
                                    time_left = expire_datetime - current_time
                                    if time_left.total_seconds() > 0:
                                        days_left = time_left.days
                                        hours_left = time_left.seconds // 3600
                                        st.info(f"⏰ 试用期剩余时间：{days_left}天{hours_left}小时")
                                    else:
                                        st.error("❌ 试用期已过期")
                                else:
                                    st.info("💡 试用期内您可以使用所有高级功能，包括STRM管理、实时监控、NFO编辑等。")
                            else:
                                st.success("🎉 激活成功！软件已解锁完整功能")

                            # 显示激活详情
                            with st.expander("📋 查看激活详情", expanded=True):
                                col1, col2 = st.columns(2)
                                with col1:
                                    st.write(f"**激活类型：** {config['type'].upper()}")
                                    st.write(f"**激活时间：** {new_status['activated_date']}")
                                with col2:
                                    if config.get("trial_days"):
                                        st.write(f"**试用天数：** {config['trial_days']}天")
                                        st.write(f"**到期时间：** {new_status['expires_datetime']}")
                                    else:
                                        st.write(f"**到期日期：** {new_status['expires']}")

                            st.balloons()

                            # 提示用户返回首页查看效果
                            st.success("🎉 **激活成功！** 项目名称已升级为 **MMA Pro**，所有高级功能已解锁！")
                            st.info("💡 **提示：** 返回首页可以看到项目名称从 'MMA' 升级为 'MMA Pro'，侧边栏和浏览器标题也会同步更新。")

                            # 添加快速导航按钮
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                if st.button("🏠 返回首页", type="primary"):
                                    st.session_state.active_tab = "首页"
                                    st.rerun()
                            with col2:
                                if st.button("🎬 STRM整理"):
                                    st.session_state.active_tab = "STRM整理"
                                    st.rerun()
                            with col3:
                                if st.button("📡 实时监控"):
                                    st.session_state.active_tab = "实时监控"
                                    st.rerun()
                        else:
                            st.error("❌ 激活状态保存失败")
                    else:
                        st.error(f"❌ 激活失败：{config.get('error', '未知错误')}")
                else:
                    st.warning("⚠️ 请输入激活码")

        # 使用指导和帮助
        st.markdown("---")
        st.markdown("### 💡 使用指导")

        # 快速复制激活码
        col1, col2 = st.columns([2, 1])

        with col1:
            st.markdown("**🎯 快速激活：**")
            st.code("MMA-TRIAL-7DAYS", language="text")
            st.caption("👆 点击上方代码框右上角复制按钮")

        with col2:
            st.markdown("**📱 一键复制：**")
            # 使用Streamlit原生方式显示可复制的文本
            st.code("MMA-TRIAL-7DAYS", language="text")
            st.caption("👆 点击代码框右上角复制按钮")

        # 功能介绍
        with st.expander("🚀 激活后可使用的功能", expanded=False):
            st.markdown("""
            ### 🎬 STRM管理功能
            - **本地CD2管理**: 批量创建STRM文件，修改路径，下载元数据
            - **115云端管理**: 云盘文件浏览，STRM生成，文件上传下载
            - **批量处理**: 支持递归扫描，保持目录结构

            ### 📡 实时监控功能
            - **CD2监控**: 监控本地目录变化，自动处理新文件
            - **115监控**: 监控云盘变化，自动同步文件和元数据
            - **网盘整理**: 自动整理和分类文件

            ### 🛠️ NFO编辑功能
            - **批量编辑**: 批量修改NFO标签内容
            - **单独编辑**: 精确编辑单个NFO文件
            - **嵌套支持**: 支持复杂的XML嵌套结构

            ### 📊 其他高级功能
            - **批量重命名**: 智能文件重命名
            - **海报替换**: 高清海报批量替换
            - **文件整理**: 自动整理文件结构
            """)

        # 常见问题
        with st.expander("❓ 常见问题解答", expanded=False):
            st.markdown("""
            **Q: 试用期结束后怎么办？**
            A: 试用期结束后，高级功能将被限制。您可以联系管理员获取正式激活码。

            **Q: 可以重复使用激活码吗？**
            A: 每个激活码只能在一台设备上使用，基于硬件信息绑定。

            **Q: 激活码输入后没有反应？**
            A: 请检查激活码格式是否正确，确保网络连接正常，然后重试。

            **Q: 如何查看剩余试用时间？**
            A: 激活成功后，在此页面可以看到详细的剩余时间和进度条。
            """)

        # 联系信息
        st.markdown("---")
        st.info("📞 **需要帮助？** 如有问题请联系技术支持获取帮助。")

# ====== 自定义背景图片功能（放在st.set_page_config后，全局CSS前）======
def get_custom_bg():
    # 首先从配置文件加载设置
    bg_img_path, bg_img_url = load_bg_config()

    # 如果session_state中没有设置，则从配置文件加载
    if not st.session_state.get("bg_img_path") and not st.session_state.get("bg_img_url"):
        st.session_state["bg_img_path"] = bg_img_path
        st.session_state["bg_img_url"] = bg_img_url

    # 优先用本地图片
    if st.session_state.get("bg_img_path") and os.path.exists(st.session_state["bg_img_path"]):
        with open(st.session_state["bg_img_path"], "rb") as f:
            b64_img = base64.b64encode(f.read()).decode()
        return f"data:image/jpeg;base64,{b64_img}"
    # 其次用 session_state 里的网络图片链接
    if st.session_state.get("bg_img_url"):
        return st.session_state["bg_img_url"]
    return None

bg_img = get_custom_bg()
if bg_img:
    bg_css = f"""
    <style>
    body {{
        background: url("{bg_img}") no-repeat center center fixed !important;
        background-size: cover !important;
    }}
    [data-theme="dark"] body::before {{
        content: "";
        position: fixed;
        left: 0; top: 0; width: 100vw; height: 100vh;
        background: rgba(24,28,36,0.65);
        z-index: 0;
        pointer-events: none;
    }}
    .stApp {{
        background: transparent !important;
    }}
    </style>
    """
    st.markdown(bg_css, unsafe_allow_html=True)