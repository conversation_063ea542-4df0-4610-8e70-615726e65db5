#!/usr/bin/env python3
"""
安全的数据库映射验证工具
增强反爬虫保护机制
"""
import sys
import json
import time
import random
from datetime import datetime
from typing import Dict, List, Tuple
sys.path.append('.')

class SafeDatabaseVerifier:
    """安全的数据库映射验证器"""
    
    def __init__(self):
        self.crawler = None
        self.verification_results = {}
        self.start_time = None
        self.request_count = 0
        self.last_request_time = 0
        
        # 安全配置
        self.config = {
            'min_delay': 2.0,      # 最小请求间隔（秒）
            'max_delay': 5.0,      # 最大请求间隔（秒）
            'batch_size': 50,      # 批次大小
            'batch_delay': 300,    # 批次间休息时间（秒）
            'max_requests_per_hour': 1000,  # 每小时最大请求数
            'error_threshold': 5,   # 连续错误阈值
            'cooldown_time': 1800,  # 冷却时间（秒）
        }
        
        self.consecutive_errors = 0
        self.last_error_time = 0
    
    def safe_delay(self):
        """安全延迟"""
        # 随机延迟，避免规律性
        delay = random.uniform(self.config['min_delay'], self.config['max_delay'])
        
        # 确保与上次请求的最小间隔
        elapsed = time.time() - self.last_request_time
        if elapsed < delay:
            additional_delay = delay - elapsed
            time.sleep(additional_delay)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def check_rate_limit(self):
        """检查请求频率限制"""
        if self.start_time:
            elapsed_hours = (time.time() - self.start_time) / 3600
            if elapsed_hours > 0:
                current_rate = self.request_count / elapsed_hours
                if current_rate > self.config['max_requests_per_hour']:
                    print(f"⚠️ 请求频率过高: {current_rate:.1f}/小时")
                    return False
        return True
    
    def handle_error(self, error_msg: str):
        """处理错误"""
        self.consecutive_errors += 1
        self.last_error_time = time.time()
        
        print(f"❌ 错误: {error_msg}")
        
        if self.consecutive_errors >= self.config['error_threshold']:
            print(f"🚨 连续错误达到阈值 ({self.consecutive_errors})")
            print(f"😴 进入冷却期 {self.config['cooldown_time']} 秒...")
            time.sleep(self.config['cooldown_time'])
            self.consecutive_errors = 0
            return True
        
        return False
    
    def reset_error_count(self):
        """重置错误计数"""
        if self.consecutive_errors > 0:
            self.consecutive_errors = 0
            print("✅ 错误计数已重置")
    
    def initialize_crawler(self):
        """初始化DMM爬虫"""
        print("🔧 初始化DMM爬虫...")
        
        try:
            from modules.dmm_search_crawler import DMMSearchCrawler
            
            self.crawler = DMMSearchCrawler()
            
            if not self.crawler.age_verified:
                print("❌ 年龄验证失败")
                return False
            
            print("✅ DMM爬虫初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 爬虫初始化失败: {e}")
            return False
    
    def load_database_mappings(self):
        """加载数据库中的映射数据"""
        print("📖 加载数据库映射...")
        
        try:
            with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'mappings' not in data:
                print("❌ 映射数据格式错误")
                return None
            
            mappings = data['mappings']
            print(f"✅ 加载了 {len(mappings)} 个厂商映射")
            
            return mappings
            
        except Exception as e:
            print(f"❌ 加载映射数据失败: {e}")
            return None
    
    def safe_verify_cid(self, cid: str, detail_url: str) -> bool:
        """安全验证CID"""
        try:
            # 检查频率限制
            if not self.check_rate_limit():
                print("⚠️ 达到频率限制，暂停验证")
                return False
            
            # 安全延迟
            self.safe_delay()
            
            # 验证CID
            result = self.crawler._verify_cid_exists(detail_url, cid)
            
            # 成功则重置错误计数
            if result:
                self.reset_error_count()
            
            return result
            
        except Exception as e:
            # 处理错误
            should_cooldown = self.handle_error(str(e))
            if should_cooldown:
                return False
            
            return False
    
    def verify_studio_mapping(self, studio: str, mapping_data: Dict) -> Dict:
        """安全验证单个厂商的映射"""
        print(f"\n🔍 验证厂商: {studio}")
        
        result = {
            'studio': studio,
            'total_mappings': 0,
            'verified_mappings': [],
            'failed_mappings': [],
            'success_rate': 0.0,
            'verification_time': 0,
            'status': 'unknown',
            'request_count': 0
        }
        
        try:
            start_time = time.time()
            
            # 获取映射信息
            primary = mapping_data.get('primary', '')
            alternatives = mapping_data.get('alternatives', [])
            
            all_prefixes = [primary] + alternatives if primary else alternatives
            result['total_mappings'] = len(all_prefixes)
            
            print(f"   总映射数: {len(all_prefixes)}")
            
            if not all_prefixes:
                result['status'] = 'no_mappings'
                return result
            
            # 限制测试番号数量（减少请求）
            test_numbers = [1, 21]  # 只用2个测试番号
            
            for prefix in all_prefixes:
                prefix_verified = False
                
                for test_num in test_numbers:
                    try:
                        # 构建测试CID
                        test_cid = f"{prefix}{test_num:05d}"
                        detail_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={test_cid}/"
                        
                        # 安全验证CID
                        if self.safe_verify_cid(test_cid, detail_url):
                            result['verified_mappings'].append({
                                'prefix': prefix,
                                'test_cid': test_cid,
                                'test_number': test_num
                            })
                            prefix_verified = True
                            print(f"      ✅ {prefix} (测试: {test_cid})")
                            break
                        
                        result['request_count'] += 1
                        
                    except Exception as e:
                        print(f"      ⚠️ {prefix} 验证异常: {e}")
                        continue
                
                if not prefix_verified:
                    result['failed_mappings'].append(prefix)
                    print(f"      ❌ {prefix} (所有测试都失败)")
            
            # 计算成功率
            verified_count = len(result['verified_mappings'])
            total_count = len(all_prefixes)
            result['success_rate'] = verified_count / total_count if total_count > 0 else 0
            
            # 确定状态
            if result['success_rate'] >= 0.8:
                result['status'] = 'excellent'
            elif result['success_rate'] >= 0.5:
                result['status'] = 'good'
            elif result['success_rate'] >= 0.2:
                result['status'] = 'poor'
            else:
                result['status'] = 'failed'
            
            result['verification_time'] = time.time() - start_time
            
            print(f"   📊 验证完成: {verified_count}/{total_count} ({result['success_rate']:.1%})")
            print(f"   📡 请求数: {result['request_count']}")
            
            return result
            
        except Exception as e:
            print(f"   ❌ 验证异常: {e}")
            result['status'] = 'error'
            result['error'] = str(e)
            return result
    
    def safe_batch_verification(self, mappings: Dict, start_index: int = 0, limit: int = None) -> Dict:
        """安全批量验证"""
        print(f"\n🚀 开始安全批量验证...")
        
        self.start_time = time.time()
        studios = list(mappings.keys())[start_index:]
        
        if limit:
            studios = studios[:limit]
            print(f"⚠️ 限制验证数量: {limit} 个厂商")
        
        print(f"📋 计划验证 {len(studios)} 个厂商")
        print(f"🛡️ 安全配置:")
        print(f"   请求间隔: {self.config['min_delay']}-{self.config['max_delay']} 秒")
        print(f"   批次大小: {self.config['batch_size']}")
        print(f"   批次间休息: {self.config['batch_delay']} 秒")
        
        for i, studio in enumerate(studios, 1):
            print(f"\n{'='*50}")
            print(f"进度: {i}/{len(studios)} ({i/len(studios):.1%})")
            
            # 批次休息
            if i > 1 and (i - 1) % self.config['batch_size'] == 0:
                print(f"😴 批次休息 {self.config['batch_delay']} 秒...")
                time.sleep(self.config['batch_delay'])
            
            mapping_data = mappings[studio]
            result = self.verify_studio_mapping(studio, mapping_data)
            self.verification_results[studio] = result
            
            # 显示进度和统计
            elapsed = time.time() - self.start_time
            avg_time = elapsed / i
            remaining = (len(studios) - i) * avg_time
            
            print(f"⏱️ 已用时: {elapsed:.1f}s, 预计剩余: {remaining:.1f}s")
            print(f"📡 总请求数: {self.request_count}")
            
            # 检查是否需要停止
            if self.consecutive_errors >= self.config['error_threshold']:
                print("🚨 错误过多，停止验证")
                break
        
        return self.verification_results
    
    def generate_report(self) -> Dict:
        """生成验证报告"""
        print(f"\n📊 生成验证报告...")
        
        if not self.verification_results:
            return {}
        
        total_studios = len(self.verification_results)
        total_time = time.time() - self.start_time if self.start_time else 0
        
        # 统计各种状态
        status_counts = {
            'excellent': 0,
            'good': 0,
            'poor': 0,
            'failed': 0,
            'error': 0,
            'no_mappings': 0
        }
        
        total_mappings = 0
        total_verified = 0
        total_requests = 0
        
        for studio, result in self.verification_results.items():
            status = result.get('status', 'unknown')
            if status in status_counts:
                status_counts[status] += 1
            
            total_mappings += result.get('total_mappings', 0)
            total_verified += len(result.get('verified_mappings', []))
            total_requests += result.get('request_count', 0)
        
        overall_success_rate = total_verified / total_mappings if total_mappings > 0 else 0
        
        report = {
            'summary': {
                'total_studios': total_studios,
                'total_mappings': total_mappings,
                'total_verified': total_verified,
                'overall_success_rate': overall_success_rate,
                'verification_time': total_time,
                'total_requests': total_requests,
                'avg_requests_per_hour': (total_requests / (total_time / 3600)) if total_time > 0 else 0,
                'timestamp': datetime.now().isoformat()
            },
            'status_distribution': status_counts,
            'safety_stats': {
                'consecutive_errors': self.consecutive_errors,
                'config': self.config
            },
            'detailed_results': self.verification_results
        }
        
        return report
    
    def print_report(self, report: Dict):
        """打印验证报告"""
        if not report:
            print("❌ 无报告数据")
            return
        
        summary = report['summary']
        status_dist = report['status_distribution']
        safety_stats = report['safety_stats']
        
        print(f"\n" + "="*60)
        print(f"📊 安全数据库映射验证报告")
        print(f"="*60)
        
        print(f"\n📈 总体统计:")
        print(f"   验证厂商: {summary['total_studios']} 个")
        print(f"   总映射数: {summary['total_mappings']} 个")
        print(f"   验证成功: {summary['total_verified']} 个")
        print(f"   总成功率: {summary['overall_success_rate']:.1%}")
        print(f"   验证耗时: {summary['verification_time']:.1f} 秒")
        
        print(f"\n🛡️ 安全统计:")
        print(f"   总请求数: {summary['total_requests']}")
        print(f"   平均请求频率: {summary['avg_requests_per_hour']:.1f}/小时")
        print(f"   连续错误数: {safety_stats['consecutive_errors']}")
        
        print(f"\n📋 厂商状态分布:")
        print(f"   🟢 优秀 (≥80%): {status_dist['excellent']} 个")
        print(f"   🟡 良好 (≥50%): {status_dist['good']} 个") 
        print(f"   🟠 较差 (≥20%): {status_dist['poor']} 个")
        print(f"   🔴 失败 (<20%): {status_dist['failed']} 个")
        print(f"   ❌ 异常: {status_dist['error']} 个")
        print(f"   ⚪ 无映射: {status_dist['no_mappings']} 个")

def main():
    """主函数"""
    print("🛡️ 安全数据库映射验证工具")
    print("=" * 60)
    
    print("💡 安全特性:")
    print("   - 智能请求间隔 (2-5秒随机)")
    print("   - 批次处理 (每50个厂商休息5分钟)")
    print("   - 频率限制 (最大1000请求/小时)")
    print("   - 错误处理 (连续5个错误自动冷却)")
    print("   - 减少测试番号 (只用2个而非4个)")
    
    # 用户确认
    user_input = input("\n是否开始安全验证？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("验证已取消")
        return
    
    # 询问验证数量限制
    limit_input = input("验证数量限制 (建议50-100，回车=50): ").strip()
    limit = 50  # 默认50个
    
    if limit_input.isdigit():
        limit = int(limit_input)
    
    print(f"🎯 将验证 {limit} 个厂商")
    
    # 执行验证
    verifier = SafeDatabaseVerifier()
    
    # 1. 初始化爬虫
    if not verifier.initialize_crawler():
        print("❌ 爬虫初始化失败，验证终止")
        return
    
    # 2. 加载映射数据
    mappings = verifier.load_database_mappings()
    if not mappings:
        print("❌ 映射数据加载失败，验证终止")
        return
    
    # 3. 执行安全验证
    print(f"\n🛡️ 开始安全验证...")
    results = verifier.safe_batch_verification(mappings, limit=limit)
    
    # 4. 生成报告
    report = verifier.generate_report()
    
    # 5. 显示报告
    verifier.print_report(report)
    
    # 6. 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"safe_verification_report_{timestamp}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细报告已保存: {report_file}")
    except Exception as e:
        print(f"\n❌ 报告保存失败: {e}")

if __name__ == "__main__":
    main()
