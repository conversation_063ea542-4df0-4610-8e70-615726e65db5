#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DMM索引构建工具
用于批量构建番号索引，提高搜索速度
"""

import argparse
import time
from fast_dmm_search import FastDMMSearch


def build_popular_studios_index():
    """构建热门厂商的索引"""
    search_engine = FastDMMSearch()
    
    # 热门厂商列表
    popular_studios = [
        'MILK', 'SSIS', 'STARS', 'PRED', 'IPX', 'MIDE', 'MIAA', 'MIDV',
        'FSDSS', 'MIMK', 'PPPD', 'JUFE', 'EBOD', 'MEYD', 'JUL', 'MVSD',
        'WANZ', 'WAAA', 'CAWD', 'CJOD', 'DASD', 'DASS', 'DVAJ', 'HODV',
        'HUNTB', 'IENF', 'LULU', 'NHDTB', 'ROYD', 'SAME', 'SDAB', 'SDDE',
        'SDMM', 'SDMU', 'SDNM', 'STAR', 'SW', 'TIKB', 'VENX', 'VEC'
    ]
    
    print("开始构建热门厂商索引...")
    print(f"厂商列表: {', '.join(popular_studios)}")
    
    # 为每个厂商构建1-1000的索引
    for studio in popular_studios:
        if studio in search_engine.studio_mappings:
            print(f"\n构建 {studio} 厂商索引 (1-1000)...")
            start_time = time.time()
            
            built_count = 0
            for number in range(1, 1001):
                if search_engine.build_index_for_code(studio, number):
                    built_count += 1
                
                if built_count % 50 == 0:
                    print(f"  {studio}: 已构建 {built_count}/1000")
            
            end_time = time.time()
            print(f"  {studio}: 完成，共构建 {built_count} 个索引，耗时 {end_time - start_time:.2f}秒")
        else:
            print(f"  跳过 {studio}：未在映射中找到")
    
    # 显示最终统计
    stats = search_engine.get_statistics()
    print(f"\n索引构建完成！")
    print(f"总索引数: {stats['total_videos']}")
    print(f"总变体数: {stats['total_variants']}")


def build_specific_studio_index(studio: str, start_num: int, end_num: int):
    """构建特定厂商的索引"""
    search_engine = FastDMMSearch()
    
    if studio.upper() not in search_engine.studio_mappings:
        print(f"错误: 厂商 {studio} 未在映射中找到")
        return
    
    studio = studio.upper()
    print(f"构建 {studio} 厂商索引 ({start_num}-{end_num})...")
    
    start_time = time.time()
    built_count = 0
    
    for number in range(start_num, end_num + 1):
        if search_engine.build_index_for_code(studio, number):
            built_count += 1
        
        if built_count % 50 == 0:
            print(f"已构建 {built_count}/{end_num - start_num + 1}")
    
    end_time = time.time()
    print(f"完成！共构建 {built_count} 个索引，耗时 {end_time - start_time:.2f}秒")


def verify_existing_index():
    """验证现有索引的URL有效性"""
    search_engine = FastDMMSearch()
    
    import sqlite3
    conn = sqlite3.connect(search_engine.db_file)
    cursor = conn.cursor()
    
    # 获取未验证的记录
    cursor.execute('''
        SELECT id, dmm_cid, url FROM video_index 
        WHERE verified = FALSE
        LIMIT 100
    ''')
    
    unverified = cursor.fetchall()
    conn.close()
    
    if not unverified:
        print("所有索引都已验证")
        return
    
    print(f"开始验证 {len(unverified)} 个未验证的索引...")
    
    verified_count = 0
    for video_id, dmm_cid, url in unverified:
        if search_engine.verify_dmm_url(url):
            search_engine.mark_as_verified(dmm_cid)
            verified_count += 1
            print(f"✓ 验证成功: {dmm_cid}")
        else:
            print(f"✗ 验证失败: {dmm_cid}")
        
        time.sleep(0.1)  # 避免请求过快
    
    print(f"验证完成！成功验证 {verified_count}/{len(unverified)} 个索引")


def show_statistics():
    """显示数据库统计信息"""
    search_engine = FastDMMSearch()
    stats = search_engine.get_statistics()
    
    print("=== DMM索引数据库统计 ===")
    print(f"总视频数: {stats['total_videos']:,}")
    print(f"已验证视频数: {stats['verified_videos']:,}")
    print(f"总变体数: {stats['total_variants']:,}")
    print(f"厂商数: {stats['unique_studios']}")
    print(f"验证率: {stats['verification_rate']:.2%}")
    
    if stats['total_videos'] > 0:
        print(f"平均每个视频的变体数: {stats['total_variants'] / stats['total_videos']:.1f}")


def main():
    parser = argparse.ArgumentParser(description='DMM索引构建工具')
    parser.add_argument('action', choices=['popular', 'studio', 'verify', 'stats'], 
                       help='操作类型')
    parser.add_argument('--studio', type=str, help='厂商名称（用于studio操作）')
    parser.add_argument('--start', type=int, default=1, help='起始番号（用于studio操作）')
    parser.add_argument('--end', type=int, default=1000, help='结束番号（用于studio操作）')
    
    args = parser.parse_args()
    
    if args.action == 'popular':
        build_popular_studios_index()
    elif args.action == 'studio':
        if not args.studio:
            print("错误: 请指定厂商名称 --studio")
            return
        build_specific_studio_index(args.studio, args.start, args.end)
    elif args.action == 'verify':
        verify_existing_index()
    elif args.action == 'stats':
        show_statistics()


if __name__ == "__main__":
    main()
