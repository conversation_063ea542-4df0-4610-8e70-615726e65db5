#!/usr/bin/env python3
"""
正式番号映射提取脚本
从实际CSV文件中提取番号前缀与DMM CID前缀的映射关系
"""

import re
import json
import time
from collections import defaultdict
from pathlib import Path


class CSVMappingExtractor:
    """CSV番号映射提取器"""
    
    def __init__(self):
        self.mappings = defaultdict(set)  # 使用set避免重复
        self.statistics = {
            "total_lines": 0,
            "matched_lines": 0,
            "unique_studios": 0,
            "mapping_patterns": defaultdict(int)
        }
    
    def extract_from_csv(self, csv_file_path: str):
        """从CSV文件中提取映射关系"""
        print(f"🔍 开始分析文件: {csv_file_path}")
        
        if not Path(csv_file_path).exists():
            print(f"❌ 文件不存在: {csv_file_path}")
            return False
        
        try:
            with open(csv_file_path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()
                lines = content.split('\n')
                
                print(f"📊 文件总行数: {len(lines)}")
                
                for line_num, line in enumerate(lines, 1):
                    self.statistics["total_lines"] += 1
                    
                    # 显示进度
                    if line_num % 5000 == 0:
                        print(f"   处理进度: {line_num}/{len(lines)} 行 ({line_num/len(lines)*100:.1f}%)")
                    
                    # 提取映射关系
                    if self._extract_mapping_from_line(line):
                        self.statistics["matched_lines"] += 1
                
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return False
        
        print("✅ 文件分析完成")
        print(f"   总行数: {self.statistics['total_lines']}")
        print(f"   匹配行数: {self.statistics['matched_lines']}")
        print(f"   匹配率: {self.statistics['matched_lines']/self.statistics['total_lines']*100:.2f}%")
        
        return True
    
    def _extract_mapping_from_line(self, line: str) -> bool:
        """从单行文本中提取映射关系"""
        if not line.strip():
            return False
        
        # 定义番号匹配模式
        code_patterns = [
            r'([A-Z]+)-(\d+)',              # GAJK-024
            r'([A-Z]+)(\d+)',               # GAJK024 (无连字符)
            r'([A-Z]+)[-_](\d+)',           # GAJK_024 (下划线)
        ]
        
        # 定义CID匹配模式（针对实际数据格式优化）
        cid_patterns = [
            # 主要格式：最后一个括号内的CID
            r'\(([a-z]+)(\d{5,})\)(?![^(]*\([^)]*[a-z])',     # (gajk00024)
            r'\((h_\d+[a-z]+)(\d{5,})\)(?![^(]*\([^)]*[a-z])', # (h_1240milk00251)
            r'\((\d+[a-z]+)(\d{5,})\)(?![^(]*\([^)]*[a-z])',   # (1sdmm00456)
        ]
        
        # 查找所有番号
        all_code_matches = []
        for pattern in code_patterns:
            matches = re.findall(pattern, line, re.IGNORECASE)
            all_code_matches.extend([(m[0].upper(), m[1]) for m in matches])
        
        if not all_code_matches:
            return False
        
        # 查找对应的CID
        found_mapping = False
        line_lower = line.lower()
        processed_mappings = set()  # 避免重复处理同一个映射
        
        for code_prefix, code_number in all_code_matches:
            # 尝试不同的CID模式
            for cid_pattern in cid_patterns:
                cid_matches = re.findall(cid_pattern, line_lower)
                
                for cid_match in cid_matches:
                    if len(cid_match) == 2:
                        cid_prefix, cid_number = cid_match
                        
                        # 创建映射标识符，避免重复处理
                        mapping_key = f"{code_prefix}-{code_number}:{cid_prefix}{cid_number}"
                        if mapping_key in processed_mappings:
                            continue
                        
                        # 验证是否为有效映射
                        if self._is_valid_mapping(code_prefix, code_number, cid_prefix, cid_number):
                            self.mappings[code_prefix].add(cid_prefix)
                            self.statistics["mapping_patterns"][f"{code_prefix} -> {cid_prefix}"] += 1
                            processed_mappings.add(mapping_key)
                            found_mapping = True
                            
                            # 调试输出（仅前几个）
                            if self.statistics["matched_lines"] < 5:
                                print(f"   发现映射: {code_prefix}-{code_number} -> {cid_prefix}{cid_number}")
                            
                            # 找到有效映射后，跳出内层循环
                            break
                
                # 如果已经找到映射，跳出CID模式循环
                if found_mapping:
                    break
        
        return found_mapping
    
    def _is_valid_mapping(self, code_prefix: str, code_number: str, cid_prefix: str, cid_number: str) -> bool:
        """验证映射关系是否有效"""
        try:
            # 基本验证
            if not code_prefix or not cid_prefix:
                return False
            
            # 过滤明显无效的CID前缀
            if len(cid_prefix) < 2 or len(cid_prefix) > 20:
                return False
            
            # 数字部分验证（允许补零）
            code_num = int(code_number)
            cid_num = int(cid_number)
            
            # 数字应该匹配（考虑补零）
            if code_num != cid_num:
                return False
            
            # CID数字部分长度验证（通常是3-5位）
            if len(cid_number) < 3 or len(cid_number) > 6:
                return False
            
            # CID前缀应该包含番号前缀的小写形式
            code_lower = code_prefix.lower()
            
            # 检查各种CID前缀格式的有效性
            return self._check_cid_prefix_validity(code_lower, cid_prefix)
            
        except (ValueError, AttributeError):
            return False
    
    def _check_cid_prefix_validity(self, code_lower: str, cid_prefix: str) -> bool:
        """检查CID前缀的有效性"""
        # 1. 直接匹配（最常见）
        if cid_prefix == code_lower:
            return True
        
        # 2. h_数字+番号前缀格式（如 h_1240milk）
        h_pattern = rf"^h_\d+{re.escape(code_lower)}$"
        if re.match(h_pattern, cid_prefix):
            return True
        
        # 3. 数字+番号前缀格式（如 1sdmm）
        num_pattern = rf"^\d+{re.escape(code_lower)}$"
        if re.match(num_pattern, cid_prefix):
            return True
        
        # 4. 以番号前缀结尾（如 xxxgajk）
        if cid_prefix.endswith(code_lower) and len(cid_prefix) > len(code_lower):
            # 确保前面的部分是合理的（数字或h_数字）
            prefix_part = cid_prefix[:-len(code_lower)]
            if re.match(r'^(h_)?\d+$', prefix_part):
                return True
        
        # 5. 包含番号前缀但有其他合理的前后缀
        if code_lower in cid_prefix:
            # 检查是否是合理的包含关系
            parts = cid_prefix.split(code_lower)
            if len(parts) == 2:
                prefix_part, suffix_part = parts
                # 前缀应该是空、数字或h_数字
                # 后缀应该是空或少量字母
                if (not prefix_part or re.match(r'^(h_)?\d+$', prefix_part)) and \
                   (not suffix_part or re.match(r'^[a-z]{0,3}$', suffix_part)):
                    return True
        
        return False
    
    def generate_mappings_dict(self) -> dict:
        """生成最终的映射字典（单一映射版本）"""
        final_mappings = {}

        for studio, cid_prefixes in self.mappings.items():
            if len(cid_prefixes) == 1:
                # 只有一个映射，直接使用
                final_mappings[studio] = list(cid_prefixes)[0]
            else:
                # 多个映射，选择最常见的或最合理的
                # 优先选择最短的（通常是最基本的格式）
                best_prefix = min(cid_prefixes, key=len)
                final_mappings[studio] = best_prefix

                print(f"⚠️ {studio} 有多个映射: {list(cid_prefixes)}, 选择: {best_prefix}")

        self.statistics["unique_studios"] = len(final_mappings)
        return final_mappings

    def generate_all_mappings_dict(self) -> dict:
        """生成包含所有映射关系的完整字典"""
        all_mappings = {}
        single_mappings = {}
        multi_mappings = {}

        for studio, cid_prefixes in self.mappings.items():
            cid_list = sorted(list(cid_prefixes))  # 排序以保持一致性

            if len(cid_prefixes) == 1:
                single_mappings[studio] = cid_list[0]
                all_mappings[studio] = {
                    "primary": cid_list[0],
                    "alternatives": [],
                    "count": 1,
                    "type": "single"
                }
            else:
                # 选择主要映射（最短的）
                primary = min(cid_prefixes, key=len)
                alternatives = [cid for cid in cid_list if cid != primary]

                multi_mappings[studio] = {
                    "primary": primary,
                    "alternatives": alternatives,
                    "all": cid_list
                }

                all_mappings[studio] = {
                    "primary": primary,
                    "alternatives": alternatives,
                    "count": len(cid_prefixes),
                    "type": "multiple"
                }

        return {
            "all_mappings": all_mappings,
            "single_mappings": single_mappings,
            "multi_mappings": multi_mappings,
            "statistics": {
                "total_studios": len(all_mappings),
                "single_mapping_studios": len(single_mappings),
                "multi_mapping_studios": len(multi_mappings)
            }
        }
    
    def save_results(self, output_file: str = "studio_mappings_extracted.txt",
                    json_file: str = "studio_mappings_extracted.json",
                    all_mappings_file: str = "studio_mappings_all.json"):
        """保存提取结果"""
        # 获取单一映射和完整映射
        final_mappings = self.generate_mappings_dict()
        all_mappings_data = self.generate_all_mappings_dict()

        # 按字母顺序排序
        sorted_mappings = dict(sorted(final_mappings.items()))

        # 保存为文本文件（简化版本）
        print(f"💾 保存映射关系到: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# 番号前缀与DMM CID前缀映射关系\n")
            f.write(f"# 提取时间: {self._get_timestamp()}\n")
            f.write(f"# 总计: {len(sorted_mappings)} 个厂商映射\n\n")

            for studio, cid_prefix in sorted_mappings.items():
                f.write(f"{studio}: {cid_prefix}\n")

        # 保存详细的文本文件（包含所有映射）
        detailed_file = output_file.replace('.txt', '_detailed.txt')
        print(f"💾 保存详细映射关系到: {detailed_file}")
        with open(detailed_file, 'w', encoding='utf-8') as f:
            f.write("# 番号前缀与DMM CID前缀映射关系（详细版本）\n")
            f.write(f"# 提取时间: {self._get_timestamp()}\n")
            f.write(f"# 总计: {len(sorted_mappings)} 个厂商映射\n")
            f.write(f"# 单一映射: {all_mappings_data['statistics']['single_mapping_studios']} 个\n")
            f.write(f"# 多重映射: {all_mappings_data['statistics']['multi_mapping_studios']} 个\n\n")

            # 单一映射部分
            f.write("=" * 60 + "\n")
            f.write("单一映射厂商\n")
            f.write("=" * 60 + "\n")
            for studio, cid_prefix in sorted(all_mappings_data['single_mappings'].items()):
                f.write(f"{studio}: {cid_prefix}\n")

            # 多重映射部分
            f.write(f"\n{'=' * 60}\n")
            f.write("多重映射厂商\n")
            f.write("=" * 60 + "\n")
            for studio, mapping_info in sorted(all_mappings_data['multi_mappings'].items()):
                f.write(f"{studio}:\n")
                f.write(f"  主要映射: {mapping_info['primary']}\n")
                f.write(f"  备选映射: {', '.join(mapping_info['alternatives'])}\n")
                f.write(f"  所有映射: {', '.join(mapping_info['all'])}\n")
                f.write("\n")

        # 保存为JSON文件（用于FastDMM）
        print(f"💾 保存JSON配置到: {json_file}")

        # 创建完整的FastDMM配置格式
        json_config = {
            "studio_mappings": sorted_mappings,
            "search_patterns": {
                "number_padding": [3, 4, 5],
                "prefix_variations": ["", "0", "00"],
                "separator_variations": ["-", ""]
            },
            "extraction_info": {
                "source_file": "CSV数据提取",
                "timestamp": self._get_timestamp(),
                "total_lines_processed": self.statistics["total_lines"],
                "matched_lines": self.statistics["matched_lines"],
                "match_rate": f"{self.statistics['matched_lines']/self.statistics['total_lines']*100:.2f}%",
                "unique_studios_found": self.statistics["unique_studios"],
                "extraction_patterns": len(self.statistics["mapping_patterns"])
            },
            "quality_metrics": {
                "confidence_level": "high" if self.statistics["matched_lines"] > 1000 else "medium",
                "data_completeness": f"{len(sorted_mappings)} studios mapped",
                "validation_status": "auto-extracted"
            }
        }

        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_config, f, ensure_ascii=False, indent=2, sort_keys=False)

        # 保存完整映射数据（包含所有映射关系）
        print(f"💾 保存完整映射数据到: {all_mappings_file}")
        complete_config = {
            "mappings": all_mappings_data['all_mappings'],
            "statistics": all_mappings_data['statistics'],
            "extraction_info": {
                "source_file": "CSV数据提取",
                "timestamp": self._get_timestamp(),
                "total_lines_processed": self.statistics["total_lines"],
                "matched_lines": self.statistics["matched_lines"],
                "match_rate": f"{self.statistics['matched_lines']/self.statistics['total_lines']*100:.2f}%"
            },
            "single_mappings": all_mappings_data['single_mappings'],
            "multi_mappings": all_mappings_data['multi_mappings']
        }

        with open(all_mappings_file, 'w', encoding='utf-8') as f:
            json.dump(complete_config, f, ensure_ascii=False, indent=2, sort_keys=False)

        return sorted_mappings, all_mappings_data
    
    def print_statistics(self):
        """打印统计信息"""
        all_mappings_data = self.generate_all_mappings_dict()

        print(f"\n📊 提取统计:")
        print(f"   总行数: {self.statistics['total_lines']}")
        print(f"   匹配行数: {self.statistics['matched_lines']}")
        print(f"   匹配率: {self.statistics['matched_lines']/self.statistics['total_lines']*100:.2f}%")
        print(f"   发现厂商: {len(self.mappings)}")
        print(f"   单一映射厂商: {all_mappings_data['statistics']['single_mapping_studios']}")
        print(f"   多重映射厂商: {all_mappings_data['statistics']['multi_mapping_studios']}")
        print(f"   有效映射: {self.statistics['unique_studios']}")

        # 显示最常见的映射模式
        print(f"\n🔥 热门映射模式 (前10):")
        sorted_patterns = sorted(self.statistics["mapping_patterns"].items(),
                               key=lambda x: x[1], reverse=True)
        for pattern, count in sorted_patterns[:10]:
            print(f"   {pattern}: {count}次")

        # 显示多重映射的详细信息
        if all_mappings_data['statistics']['multi_mapping_studios'] > 0:
            print(f"\n🔀 多重映射详情:")
            multi_mappings = all_mappings_data['multi_mappings']
            # 按映射数量排序，显示前10个
            sorted_multi = sorted(multi_mappings.items(),
                                key=lambda x: len(x[1]['all']), reverse=True)
            for studio, mapping_info in sorted_multi[:10]:
                print(f"   {studio}: {len(mapping_info['all'])}个映射 -> {', '.join(mapping_info['all'])}")

            if len(sorted_multi) > 10:
                print(f"   ... 还有 {len(sorted_multi) - 10} 个多重映射厂商")
    
    def print_preview(self, limit: int = 20):
        """预览提取的映射关系"""
        all_mappings_data = self.generate_all_mappings_dict()
        final_mappings = self.generate_mappings_dict()
        sorted_mappings = dict(sorted(final_mappings.items()))

        print(f"\n📋 映射关系预览 (前{limit}个):")
        print("-" * 40)

        count = 0
        for studio, cid_prefix in sorted_mappings.items():
            if count >= limit:
                break

            # 检查是否有多重映射
            if studio in all_mappings_data['multi_mappings']:
                alternatives = all_mappings_data['multi_mappings'][studio]['alternatives']
                print(f"   {studio}: {cid_prefix} (还有: {', '.join(alternatives)})")
            else:
                print(f"   {studio}: {cid_prefix}")
            count += 1

        if len(sorted_mappings) > limit:
            print(f"   ... 还有 {len(sorted_mappings) - limit} 个映射")

        # 显示多重映射统计
        multi_count = all_mappings_data['statistics']['multi_mapping_studios']
        if multi_count > 0:
            print(f"\n💡 其中 {multi_count} 个厂商有多重映射关系")
    
    def analyze_multi_mappings(self):
        """分析多重映射的详细信息"""
        all_mappings_data = self.generate_all_mappings_dict()
        multi_mappings = all_mappings_data['multi_mappings']

        if not multi_mappings:
            print("\n✅ 没有发现多重映射")
            return

        print(f"\n🔍 多重映射分析:")
        print(f"   发现 {len(multi_mappings)} 个厂商有多重映射")

        # 按映射数量分组
        mapping_count_groups = defaultdict(list)
        for studio, mapping_info in multi_mappings.items():
            count = len(mapping_info['all'])
            mapping_count_groups[count].append((studio, mapping_info))

        # 显示各组统计
        print(f"\n📈 按映射数量分组:")
        for count in sorted(mapping_count_groups.keys(), reverse=True):
            studios = mapping_count_groups[count]
            print(f"   {count}个映射: {len(studios)} 个厂商")

            # 显示前5个例子
            for studio, mapping_info in studios[:5]:
                print(f"     - {studio}: {', '.join(mapping_info['all'])}")

            if len(studios) > 5:
                print(f"     ... 还有 {len(studios) - 5} 个")

        # 分析映射模式
        print(f"\n🔬 映射模式分析:")
        pattern_types = {
            "direct": 0,      # 直接映射 (abc -> abc)
            "h_prefix": 0,    # h_数字前缀 (abc -> h_123abc)
            "num_prefix": 0,  # 数字前缀 (abc -> 123abc)
            "mixed": 0        # 混合模式
        }

        for studio, mapping_info in multi_mappings.items():
            studio_lower = studio.lower()
            patterns_found = set()

            for cid in mapping_info['all']:
                if cid == studio_lower:
                    patterns_found.add("direct")
                elif cid.startswith("h_") and cid.endswith(studio_lower):
                    patterns_found.add("h_prefix")
                elif re.match(rf"^\d+{re.escape(studio_lower)}$", cid):
                    patterns_found.add("num_prefix")
                else:
                    patterns_found.add("mixed")

            if len(patterns_found) == 1:
                pattern_types[list(patterns_found)[0]] += 1
            else:
                pattern_types["mixed"] += 1

        for pattern, count in pattern_types.items():
            if count > 0:
                print(f"   {pattern}: {count} 个厂商")

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    print("🚀 番号前缀与DMM CID前缀映射关系提取器")
    print("=" * 60)
    
    # 输入文件路径
    input_file = r"/vol1/1000/脚本项目/x1080x.12万.title.csv"
    
    # 检查文件是否存在
    if not Path(input_file).exists():
        print(f"❌ 文件不存在: {input_file}")
        print("请确认文件路径是否正确")
        return
    
    # 创建提取器
    extractor = CSVMappingExtractor()
    
    # 提取映射关系
    if not extractor.extract_from_csv(input_file):
        print("❌ 提取失败")
        return
    
    # 显示统计信息
    extractor.print_statistics()

    # 分析多重映射
    extractor.analyze_multi_mappings()

    # 预览结果
    extractor.print_preview()
    
    # 保存结果
    mappings, all_mappings_data = extractor.save_results()

    print(f"\n🎉 提取完成！")
    print(f"   发现 {len(mappings)} 个厂商映射关系")
    print(f"   单一映射: {all_mappings_data['statistics']['single_mapping_studios']} 个")
    print(f"   多重映射: {all_mappings_data['statistics']['multi_mapping_studios']} 个")
    print("   结果已保存到:")
    print("   - studio_mappings_extracted.txt (简化版本)")
    print("   - studio_mappings_extracted_detailed.txt (详细版本)")
    print("   - studio_mappings_extracted.json (FastDMM格式)")
    print("   - studio_mappings_all.json (完整数据)")

    print("\n💡 使用建议:")
    print("   1. 查看 studio_mappings_extracted_detailed.txt 了解所有映射关系")
    print("   2. 检查多重映射厂商的映射选择是否合理")
    print("   3. 将 studio_mappings_extracted.json 合并到 mmp/dmm_studio_mappings.json")
    print("   4. 运行 python fast_dmm_manager.py build 重建索引")


if __name__ == "__main__":
    main()
