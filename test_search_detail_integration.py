#!/usr/bin/env python3
"""
测试搜索详情模块集成
"""
import sys
import os

# 添加项目路径
sys.path.append('.')

def test_search_detail_module():
    """测试搜索详情模块"""
    print("🧪 测试搜索详情模块集成...")
    
    try:
        from modules.search_detail import SearchDetailModule
        
        # 创建搜索详情模块实例
        print("1. 创建搜索详情模块...")
        search_module = SearchDetailModule(enable_fast_search=True)
        print("   ✅ 搜索详情模块创建成功")
        
        # 测试用例
        test_cases = [
            ('SSIS-001', 'single', '单一映射番号'),
            ('MOND-123', 'multiple', '多重映射番号'),
            ('GDRD-046', 'single', '单一映射番号'),
            ('AVOP-456', 'multiple', '复杂多重映射')
        ]
        
        print("\n2. 测试智能搜索功能...")
        for code, expected_type, description in test_cases:
            print(f"\n   测试: {code} ({description})")
            
            try:
                # 调用增强搜索
                result = search_module.search_dmm_enhanced(code)
                
                print(f"   结果类型: {result.get('search_type', 'unknown')}")
                print(f"   成功: {result['success']}")
                
                if result['success']:
                    if result.get('search_type') == 'multiple':
                        print(f"   找到 {result['total_count']} 个映射:")
                        print(f"   主要结果: {result['primary_result']['cid']}")
                        if result['alternative_results']:
                            print(f"   备选数量: {len(result['alternative_results'])}")
                    else:
                        print(f"   CID: {result['cid']}")
                        print(f"   URL: {result['url']}")
                else:
                    print(f"   ❌ 搜索失败: {result['message']}")
                    
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
        
        print("\n✅ 搜索详情模块测试完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 搜索详情模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_poster_functionality():
    """测试海报功能"""
    print("\n🖼️ 测试海报功能...")
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_module = SearchDetailModule(enable_fast_search=True)
        
        # 测试海报获取
        test_code = "SSIS-001"
        print(f"   测试海报获取: {test_code}")
        
        poster_result = search_module.get_poster_for_search(test_code)
        
        if poster_result['success']:
            print(f"   ✅ 海报获取成功")
            print(f"   海报大小: {len(poster_result.get('poster_bytes', b''))} bytes")
            if poster_result.get('thumb_bytes'):
                print(f"   缩略图大小: {len(poster_result['thumb_bytes'])} bytes")
        else:
            print(f"   ⚠️ 海报获取失败: {poster_result['message']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 海报功能测试失败: {e}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔄 测试向后兼容性...")
    
    try:
        from modules.search_detail import SearchDetailModule
        
        search_module = SearchDetailModule(enable_fast_search=True)
        
        # 测试原有的search_dmm方法
        test_code = "SSIS-001"
        print(f"   测试原有search_dmm方法: {test_code}")
        
        result = search_module.search_dmm(test_code)
        print(f"   结果: {result[:100]}...")  # 只显示前100个字符
        
        print("   ✅ 向后兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 向后兼容性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔬 搜索详情模块集成测试")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        'mmp/fast_dmm.db',
        'studio_mappings_all.json',
        'modules/search_detail.py',
        'modules/fast_dmm_search.py'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return
    
    # 运行测试
    tests = [
        ("搜索详情模块", test_search_detail_module),
        ("海报功能", test_poster_functionality),
        ("向后兼容性", test_backward_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{len(results)} 测试通过")
    
    if passed_count == len(results):
        print("🎉 搜索详情模块集成成功！")
        print("\n📋 下一步:")
        print("1. 修改 modules/search_detail_ui.py")
        print("2. 测试完整的UI界面")
        print("3. 完成系统集成")
    else:
        print("⚠️ 部分测试失败，请检查问题")

if __name__ == "__main__":
    main()
