#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DMM搜索测试脚本
演示快速搜索功能
"""

import time
from fast_dmm_search import FastDMMSearch


def test_search_performance():
    """测试搜索性能"""
    search_engine = FastDMMSearch()
    
    # 测试用例：各种格式的番号
    test_cases = [
        # MILK系列测试
        "MILK-251",
        "MILK251", 
        "MILK00251",
        "MILK-0251",
        "MILK-00251",
        
        # SSIS系列测试
        "SSIS-001",
        "SSIS001",
        "SSIS-0001",
        "SSIS00001",
        
        # STARS系列测试
        "STARS-123",
        "STARS123",
        "STARS-0123",
        "STARS00123",
        
        # 其他厂商测试
        "PRED-456",
        "IPX-789",
        "MIDE-100",
        "JUFE-200",
        "EBOD-300",
        
        # 不存在的番号
        "UNKNOWN-999",
        "TEST-123"
    ]
    
    print("=== DMM番号搜索性能测试 ===\n")
    
    total_time = 0
    successful_searches = 0
    
    for i, code in enumerate(test_cases, 1):
        print(f"测试 {i:2d}: {code:<12}", end=" ")
        
        start_time = time.time()
        results = search_engine.smart_search(code)
        end_time = time.time()
        
        search_time = end_time - start_time
        total_time += search_time
        
        if results:
            successful_searches += 1
            result = results[0]  # 取第一个结果
            print(f"✓ {search_time*1000:6.2f}ms - {result.dmm_cid} ({result.source})")
            
            # 显示详细信息
            if len(results) > 1:
                print(f"           找到 {len(results)} 个结果")
            
        else:
            print(f"✗ {search_time*1000:6.2f}ms - 未找到")
    
    print(f"\n=== 测试总结 ===")
    print(f"总测试数: {len(test_cases)}")
    print(f"成功搜索: {successful_searches}")
    print(f"成功率: {successful_searches/len(test_cases)*100:.1f}%")
    print(f"总耗时: {total_time*1000:.2f}ms")
    print(f"平均耗时: {total_time/len(test_cases)*1000:.2f}ms")


def test_variant_generation():
    """测试变体生成功能"""
    search_engine = FastDMMSearch()
    
    print("=== 番号变体生成测试 ===\n")
    
    test_codes = ["MILK-251", "SSIS-1", "STARS-123"]
    
    for code in test_codes:
        parsed = search_engine.parse_code(code)
        if parsed:
            studio, number = parsed
            variants = search_engine.generate_all_variants(studio, number)
            dmm_cid = search_engine.generate_dmm_cid(studio, number)
            
            print(f"原始番号: {code}")
            print(f"解析结果: 厂商={studio}, 数字={number}")
            print(f"DMM CID: {dmm_cid}")
            print(f"生成变体数: {len(variants)}")
            print("变体列表:")
            for i, variant in enumerate(sorted(variants), 1):
                print(f"  {i:2d}. {variant}")
            print()


def interactive_search():
    """交互式搜索"""
    search_engine = FastDMMSearch()
    
    print("=== 交互式DMM番号搜索 ===")
    print("输入番号进行搜索，输入 'quit' 退出")
    print("示例: MILK-251, SSIS001, STARS-123\n")
    
    while True:
        try:
            code = input("请输入番号: ").strip()
            
            if code.lower() in ['quit', 'exit', 'q']:
                break
            
            if not code:
                continue
            
            print(f"\n搜索: {code}")
            start_time = time.time()
            
            results = search_engine.smart_search(code)
            
            end_time = time.time()
            search_time = end_time - start_time
            
            if results:
                print(f"找到 {len(results)} 个结果 (耗时: {search_time*1000:.2f}ms)")
                print("-" * 60)
                
                for i, result in enumerate(results, 1):
                    print(f"结果 {i}:")
                    print(f"  番号: {result.code}")
                    print(f"  DMM CID: {result.dmm_cid}")
                    print(f"  URL: {result.url}")
                    print(f"  置信度: {result.confidence:.2f}")
                    print(f"  来源: {result.source}")
                    print()
            else:
                print(f"未找到结果 (耗时: {search_time*1000:.2f}ms)")
                
                # 尝试解析并给出建议
                parsed = search_engine.parse_code(code)
                if parsed:
                    studio, number = parsed
                    if studio not in search_engine.studio_mappings:
                        print(f"提示: 厂商 '{studio}' 不在支持列表中")
                        print("支持的厂商:", ", ".join(sorted(search_engine.studio_mappings.keys())[:10]), "...")
                else:
                    print("提示: 番号格式不正确，请使用如 'MILK-251' 或 'MILK251' 的格式")
            
            print("=" * 60)
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"错误: {e}")
    
    print("\n搜索结束")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        mode = sys.argv[1]
        if mode == 'performance':
            test_search_performance()
        elif mode == 'variants':
            test_variant_generation()
        elif mode == 'interactive':
            interactive_search()
        else:
            print("用法: python test_search.py [performance|variants|interactive]")
    else:
        # 默认运行性能测试
        test_search_performance()
        print("\n" + "="*60 + "\n")
        test_variant_generation()


if __name__ == "__main__":
    main()
