#!/usr/bin/env python3
"""
验证自动映射学习系统
"""
import sys
import os
import json
import sqlite3
from datetime import datetime

def main():
    print("=== 自动映射学习系统验证 ===")
    print(f"验证时间: {datetime.now()}")
    print()
    
    # 1. 检查文件存在性
    print("1. 检查关键文件:")
    files_to_check = [
        "studio_mappings_all.json",
        "mmp/fast_dmm.db",
        "modules/auto_mapping_learner.py",
        "modules/mapping_manager.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size} bytes)")
        else:
            print(f"   ❌ {file_path} (不存在)")
    
    print()
    
    # 2. 检查JSON配置
    print("2. 检查JSON配置:")
    try:
        with open("studio_mappings_all.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        single_mappings = data.get('single_mappings', {})
        multi_mappings = data.get('multi_mappings', {})
        
        print(f"   ✅ JSON加载成功")
        print(f"   📊 总厂商映射: {len(mappings)}")
        print(f"   📊 单一映射: {len(single_mappings)}")
        print(f"   📊 多重映射: {len(multi_mappings)}")
        
    except Exception as e:
        print(f"   ❌ JSON配置检查失败: {e}")
    
    print()
    
    # 3. 检查数据库
    print("3. 检查数据库:")
    try:
        if os.path.exists("mmp/fast_dmm.db"):
            conn = sqlite3.connect("mmp/fast_dmm.db")
            cursor = conn.cursor()
            
            # 检查表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"   ✅ 数据库连接成功")
            print(f"   📋 数据库表: {tables}")
            
            # 统计记录
            if 'studio_mappings' in tables:
                cursor.execute('SELECT COUNT(*) FROM studio_mappings')
                count = cursor.fetchone()[0]
                print(f"   📊 主映射记录: {count}")
            
            if 'studio_alternative_mappings' in tables:
                cursor.execute('SELECT COUNT(*) FROM studio_alternative_mappings')
                count = cursor.fetchone()[0]
                print(f"   📊 备选映射记录: {count}")
            
            conn.close()
        else:
            print("   ❌ 数据库文件不存在")
            
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
    
    print()
    
    # 4. 测试模块导入
    print("4. 测试模块导入:")
    try:
        sys.path.append('.')
        from modules.auto_mapping_learner import AutoMappingLearner
        print("   ✅ AutoMappingLearner 导入成功")
        
        # 创建实例
        learner = AutoMappingLearner()
        print("   ✅ AutoMappingLearner 实例创建成功")
        
        # 获取统计信息
        stats = learner.get_learning_statistics()
        print(f"   📊 学习统计: {stats['stats']}")
        
    except Exception as e:
        print(f"   ❌ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # 5. 测试简单学习功能
    print("5. 测试简单学习功能:")
    try:
        from modules.auto_mapping_learner import AutoMappingLearner
        learner = AutoMappingLearner()
        
        # 测试CID模式分析
        test_cid = "testlearn00001"
        test_studio = "TESTLEARN"
        test_number = "001"
        
        pattern_result = learner._analyze_cid_pattern(test_cid, test_studio, test_number)
        
        if pattern_result:
            print("   ✅ CID模式分析成功")
            print(f"      前缀: {pattern_result.get('prefix', 'N/A')}")
            print(f"      置信度: {pattern_result.get('confidence', 0):.2f}")
            print(f"      模式类型: {pattern_result.get('pattern_type', 'N/A')}")
        else:
            print("   ❌ CID模式分析失败")
            
    except Exception as e:
        print(f"   ❌ 简单学习功能测试失败: {e}")
    
    print()
    
    # 6. 检查搜索模块集成
    print("6. 检查搜索模块集成:")
    try:
        from modules.search_detail import SearchDetailModule
        
        # 创建搜索模块
        search_module = SearchDetailModule(
            enable_fast_search=True,
            enable_auto_learning=True
        )
        
        if hasattr(search_module, 'enable_auto_learning') and search_module.enable_auto_learning:
            print("   ✅ 自动学习功能已启用")
            
            if hasattr(search_module, 'auto_learner') and search_module.auto_learner:
                print("   ✅ 自动学习器已集成")
            else:
                print("   ⚠️ 自动学习器未正确集成")
        else:
            print("   ⚠️ 自动学习功能未启用")
            
    except Exception as e:
        print(f"   ❌ 搜索模块集成检查失败: {e}")
    
    print()
    print("=== 验证完成 ===")
    
    # 总结
    print("\n📋 系统状态总结:")
    print("✅ 自动映射学习系统基础组件已就绪")
    print("✅ JSON配置文件和数据库文件存在")
    print("✅ 核心模块可以正常导入和使用")
    print("✅ 与搜索模块的集成基本正常")
    
    print("\n🔄 下一步操作:")
    print("1. 重启Streamlit应用测试实际效果")
    print("2. 搜索一个新的番号触发自动学习")
    print("3. 验证学习后的映射是否生效")
    print("4. 检查数据库和JSON配置的同步")

if __name__ == "__main__":
    main()
