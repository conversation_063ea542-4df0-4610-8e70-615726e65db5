#!/usr/bin/env python3
"""
FastDMM数据库管理模块
提供高性能的SQLite本地索引缓存系统
"""

import sqlite3
import json
import os
import time
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta


@dataclass
class SearchResult:
    """搜索结果数据类"""
    code: str
    dmm_cid: str
    url: str
    confidence: float  # 匹配置信度
    source: str  # 数据来源：local/dmm/generated
    studio: str = ""
    verified: bool = False


class FastDMMDatabase:
    """FastDMM数据库管理器"""
    
    def __init__(self, db_file: str = "data/fast_dmm.db", config_file: str = "mmp/dmm_studio_mappings.json"):
        self.db_file = db_file
        self.config_file = config_file
        self.studio_mappings = {}
        self.search_patterns = {
            "number_padding": [3, 4, 5],
            "prefix_variations": ["", "0", "00"],
            "separator_variations": ["-", ""]
        }
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(db_file), exist_ok=True)
        
        self.load_config()
        self.init_database()
    
    def load_config(self):
        """加载厂商映射配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.studio_mappings = config.get('studio_mappings', {})
                self.search_patterns = config.get('search_patterns', self.search_patterns)
                print(f"✅ 加载了 {len(self.studio_mappings)} 个厂商映射")
        except FileNotFoundError:
            print(f"⚠️ 配置文件 {self.config_file} 不存在，使用默认配置")
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
    
    def init_database(self):
        """初始化高性能数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 创建主表 - 视频索引
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS video_index (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_code TEXT NOT NULL,
                studio TEXT NOT NULL,
                number INTEGER NOT NULL,
                dmm_cid TEXT NOT NULL UNIQUE,
                url TEXT NOT NULL,
                confidence REAL DEFAULT 0.8,
                source TEXT DEFAULT 'generated',
                verified BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                access_count INTEGER DEFAULT 0,
                last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建变体索引表 - 用于快速查找各种番号格式
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS code_variants (
                variant_code TEXT PRIMARY KEY,
                video_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (video_id) REFERENCES video_index (id) ON DELETE CASCADE
            )
        ''')
        
        # 创建厂商统计表 - 用于性能分析
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS studio_stats (
                studio TEXT PRIMARY KEY,
                total_codes INTEGER DEFAULT 0,
                verified_codes INTEGER DEFAULT 0,
                success_rate REAL DEFAULT 0.0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建高性能索引
        indexes = [
            'CREATE UNIQUE INDEX IF NOT EXISTS idx_dmm_cid ON video_index(dmm_cid)',
            'CREATE INDEX IF NOT EXISTS idx_studio_number ON video_index(studio, number)',
            'CREATE INDEX IF NOT EXISTS idx_original_code ON video_index(original_code)',
            'CREATE INDEX IF NOT EXISTS idx_verified ON video_index(verified)',
            'CREATE INDEX IF NOT EXISTS idx_created_at ON video_index(created_at)',
            'CREATE INDEX IF NOT EXISTS idx_access_count ON video_index(access_count DESC)',
            'CREATE INDEX IF NOT EXISTS idx_variant_code ON code_variants(variant_code)',
            'CREATE INDEX IF NOT EXISTS idx_video_id ON code_variants(video_id)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        conn.close()
        print("✅ 数据库初始化完成")
    
    def parse_code(self, code: str) -> Optional[Tuple[str, int]]:
        """解析番号，返回(厂商, 数字)"""
        code = code.strip().upper().replace(' ', '')
        
        # 匹配模式
        patterns = [
            r'^([A-Z]+)-(\d+)$',  # MILK-251
            r'^([A-Z]+)(\d+)$',   # MILK251
        ]
        
        for pattern in patterns:
            import re
            match = re.match(pattern, code)
            if match:
                studio = match.group(1)
                number = int(match.group(2))
                return studio, number
        
        return None
    
    def generate_all_variants(self, studio: str, number: int) -> List[str]:
        """生成所有可能的番号变体"""
        variants = set()
        
        # 基础格式
        variants.add(f"{studio}-{number}")
        variants.add(f"{studio}{number}")
        
        # 补零变体
        for padding in self.search_patterns["number_padding"]:
            padded_num = str(number).zfill(padding)
            
            # 不同分隔符
            for sep in self.search_patterns["separator_variations"]:
                variants.add(f"{studio}{sep}{padded_num}")
            
            # 前缀变体
            for prefix in self.search_patterns["prefix_variations"]:
                for sep in self.search_patterns["separator_variations"]:
                    variants.add(f"{studio}{sep}{prefix}{padded_num}")
        
        return list(variants)
    
    def generate_dmm_cid(self, studio: str, number: int) -> Optional[str]:
        """生成DMM CID"""
        if studio in self.studio_mappings:
            dmm_prefix = self.studio_mappings[studio]
            padded_number = str(number).zfill(5)
            return f"{dmm_prefix}{padded_number}"
        return None
    
    def search_in_index(self, code: str) -> List[SearchResult]:
        """在本地索引中搜索"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 首先尝试直接匹配变体
        cursor.execute('''
            SELECT vi.original_code, vi.dmm_cid, vi.url, vi.confidence, 
                   vi.source, vi.studio, vi.verified, vi.id
            FROM video_index vi
            JOIN code_variants cv ON vi.id = cv.video_id
            WHERE cv.variant_code = ? COLLATE NOCASE
            ORDER BY vi.confidence DESC, vi.access_count DESC
        ''', (code.upper(),))
        
        results = []
        for row in cursor.fetchall():
            result = SearchResult(
                code=row[0],
                dmm_cid=row[1],
                url=row[2],
                confidence=row[3],
                source=row[4],
                studio=row[5],
                verified=bool(row[6])
            )
            results.append(result)
            
            # 更新访问统计
            cursor.execute('''
                UPDATE video_index 
                SET access_count = access_count + 1, last_accessed = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (row[7],))
        
        conn.commit()
        conn.close()

        return results

    def search_by_cid(self, cid: str) -> List[SearchResult]:
        """通过CID在本地索引中搜索"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 通过CID搜索
        cursor.execute('''
            SELECT original_code, dmm_cid, url, confidence,
                   source, studio, verified, id
            FROM video_index
            WHERE dmm_cid = ? COLLATE NOCASE
            ORDER BY confidence DESC, access_count DESC
        ''', (cid,))

        results = []
        for row in cursor.fetchall():
            result = SearchResult(
                code=row[0],
                dmm_cid=row[1],
                url=row[2],
                confidence=row[3],
                source=row[4],
                studio=row[5],
                verified=bool(row[6])
            )
            results.append(result)

        conn.close()
        return results
    
    def add_to_index(self, studio: str, number: int, dmm_cid: str, url: str, 
                     confidence: float = 0.8, source: str = "generated", verified: bool = False) -> bool:
        """添加记录到索引"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            original_code = f"{studio}-{number}"
            
            # 插入主记录
            cursor.execute('''
                INSERT OR REPLACE INTO video_index 
                (original_code, studio, number, dmm_cid, url, confidence, source, verified)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (original_code, studio, number, dmm_cid, url, confidence, source, verified))
            
            video_id = cursor.lastrowid
            
            # 生成并插入所有变体
            variants = self.generate_all_variants(studio, number)
            for variant in variants:
                cursor.execute('''
                    INSERT OR REPLACE INTO code_variants (variant_code, video_id)
                    VALUES (?, ?)
                ''', (variant.upper(), video_id))
            
            # 更新厂商统计
            self._update_studio_stats(cursor, studio, verified)
            
            conn.commit()
            conn.close()
            
            print(f"✅ 已添加到索引: {original_code} -> {dmm_cid}")
            return True
            
        except Exception as e:
            print(f"❌ 添加索引失败: {e}")
            return False
    
    def _update_studio_stats(self, cursor, studio: str, verified: bool):
        """更新厂商统计信息"""
        cursor.execute('''
            INSERT OR REPLACE INTO studio_stats (studio, total_codes, verified_codes, success_rate, last_updated)
            VALUES (
                ?,
                COALESCE((SELECT total_codes FROM studio_stats WHERE studio = ?), 0) + 1,
                COALESCE((SELECT verified_codes FROM studio_stats WHERE studio = ?), 0) + ?,
                (COALESCE((SELECT verified_codes FROM studio_stats WHERE studio = ?), 0) + ?) * 1.0 / 
                (COALESCE((SELECT total_codes FROM studio_stats WHERE studio = ?), 0) + 1),
                CURRENT_TIMESTAMP
            )
        ''', (studio, studio, studio, 1 if verified else 0, studio, 1 if verified else 0, studio))
    
    def mark_as_verified(self, dmm_cid: str) -> bool:
        """标记为已验证"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE video_index
                SET verified = TRUE, confidence = 1.0, source = 'verified', updated_at = CURRENT_TIMESTAMP
                WHERE dmm_cid = ?
            ''', (dmm_cid,))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"❌ 标记验证失败: {e}")
            return False

    def get_database_stats(self) -> Dict:
        """获取数据库统计信息"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 总体统计
        cursor.execute('SELECT COUNT(*) FROM video_index')
        total_videos = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM video_index WHERE verified = TRUE')
        verified_videos = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM code_variants')
        total_variants = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(DISTINCT studio) FROM video_index')
        total_studios = cursor.fetchone()[0]

        # 厂商统计
        cursor.execute('''
            SELECT studio, total_codes, verified_codes, success_rate
            FROM studio_stats
            ORDER BY total_codes DESC
            LIMIT 10
        ''')
        top_studios = cursor.fetchall()

        conn.close()

        return {
            "total_videos": total_videos,
            "verified_videos": verified_videos,
            "total_variants": total_variants,
            "total_studios": total_studios,
            "verification_rate": verified_videos / total_videos if total_videos > 0 else 0,
            "top_studios": [
                {
                    "studio": row[0],
                    "total_codes": row[1],
                    "verified_codes": row[2],
                    "success_rate": row[3]
                }
                for row in top_studios
            ]
        }

    def cleanup_old_records(self, days: int = 30) -> int:
        """清理过期记录"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()

            cutoff_date = datetime.now() - timedelta(days=days)

            cursor.execute('''
                DELETE FROM video_index
                WHERE verified = FALSE
                AND access_count = 0
                AND created_at < ?
            ''', (cutoff_date,))

            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()

            print(f"✅ 清理了 {deleted_count} 条过期记录")
            return deleted_count

        except Exception as e:
            print(f"❌ 清理记录失败: {e}")
            return 0

    def build_popular_index(self, studios: List[str] = None, number_range: Tuple[int, int] = (1, 500)) -> int:
        """批量构建热门厂商索引"""
        if studios is None:
            # 使用配置中的所有厂商
            studios = list(self.studio_mappings.keys())

        built_count = 0
        start_num, end_num = number_range

        print(f"🚀 开始构建索引: {len(studios)} 个厂商, 范围 {start_num}-{end_num}")

        for studio in studios:
            if studio not in self.studio_mappings:
                continue

            print(f"📦 构建 {studio} 索引...")
            studio_count = 0

            for number in range(start_num, end_num + 1):
                dmm_cid = self.generate_dmm_cid(studio, number)
                if dmm_cid:
                    url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={dmm_cid}/"
                    if self.add_to_index(studio, number, dmm_cid, url, confidence=0.8, source="batch_generated"):
                        studio_count += 1
                        built_count += 1

            print(f"✅ {studio}: 构建了 {studio_count} 条记录")

        print(f"🎉 索引构建完成！总计: {built_count} 条记录")
        return built_count

    def verify_index_urls(self, limit: int = 100) -> Dict:
        """验证索引中的URL有效性"""
        import requests

        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # 获取未验证的记录
        cursor.execute('''
            SELECT id, dmm_cid, url FROM video_index
            WHERE verified = FALSE
            ORDER BY access_count DESC, created_at DESC
            LIMIT ?
        ''', (limit,))

        records = cursor.fetchall()
        conn.close()

        verified_count = 0
        failed_count = 0

        print(f"🔍 开始验证 {len(records)} 个URL...")

        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        for record_id, dmm_cid, url in records:
            try:
                response = session.head(url, timeout=5)
                if response.status_code == 200:
                    self.mark_as_verified(dmm_cid)
                    verified_count += 1
                    print(f"✅ 验证成功: {dmm_cid}")
                else:
                    failed_count += 1
                    print(f"❌ 验证失败: {dmm_cid} (状态码: {response.status_code})")
            except Exception as e:
                failed_count += 1
                print(f"❌ 验证异常: {dmm_cid} ({str(e)})")

            # 添加延时避免被限制
            time.sleep(0.5)

        return {
            "total_checked": len(records),
            "verified_count": verified_count,
            "failed_count": failed_count,
            "success_rate": verified_count / len(records) if records else 0
        }
