#!/usr/bin/env python3
"""
快速DMM爬虫测试 - 简化版测试脚本
"""
import sys
import time
sys.path.append('.')

def quick_test():
    """快速测试DMM爬虫核心功能"""
    print("🚀 快速DMM爬虫测试")
    print("=" * 40)
    
    print("⚠️ 注意: 此测试将访问真实的DMM网站")
    user_input = input("是否继续？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("测试已取消")
        return
    
    try:
        from modules.dmm_search_crawler import DMMSearchCrawler
        
        print("\n🔍 初始化爬虫...")
        crawler = DMMSearchCrawler()
        
        # 测试ID-021（你提供的真实案例）
        print("\n📝 测试 ID-021 (基于你提供的真实DMM搜索结果)")
        
        start_time = time.time()
        mappings = crawler._crawl_single_number("ID", 21)
        end_time = time.time()
        
        print(f"\n📊 爬取结果:")
        print(f"   耗时: {end_time - start_time:.2f} 秒")
        print(f"   发现映射: {len(mappings)} 个")
        
        if mappings:
            print(f"\n📋 发现的CID:")
            for i, mapping in enumerate(mappings, 1):
                print(f"   {i:2d}. {mapping['cid']} -> {mapping['prefix']} (置信度: {mapping['confidence']:.2f})")
            
            # 验证是否包含预期的CID
            expected_cids = [
                "5533id00021", "5532id00021", "5531id00021", "5530id00021",
                "5529id00021", "h_113id00021", "5526id00021", "5524id00021",
                "5525id00021", "5522id00021", "5521id00021", "5519id00021"
            ]
            
            found_cids = [m['cid'] for m in mappings]
            matched_count = sum(1 for cid in expected_cids if cid in found_cids)
            
            print(f"\n🎯 验证结果:")
            print(f"   预期CID: {len(expected_cids)} 个")
            print(f"   匹配CID: {matched_count} 个")
            print(f"   匹配率: {matched_count/len(expected_cids)*100:.1f}%")
            
            if matched_count >= len(expected_cids) * 0.8:  # 80%匹配率
                print("   ✅ 爬取质量良好")
            else:
                print("   ⚠️ 爬取质量需要改进")
                print("   未匹配的预期CID:")
                for cid in expected_cids:
                    if cid not in found_cids:
                        print(f"      - {cid}")
        else:
            print("   ❌ 未发现任何映射")
            print("   可能原因:")
            print("   - 网络连接问题")
            print("   - DMM页面结构变化")
            print("   - 访问被限制")
        
        return len(mappings) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = quick_test()
    
    print("\n" + "=" * 40)
    
    if success:
        print("🎉 快速测试通过！")
        print("\n💡 下一步:")
        print("   1. 运行完整测试: python dmm_crawler_test.py")
        print("   2. 测试其他厂商的爬取")
        print("   3. 验证映射数据库更新")
    else:
        print("❌ 快速测试失败")
        print("\n🛠️ 建议:")
        print("   1. 检查网络连接")
        print("   2. 确认DMM网站可访问")
        print("   3. 查看详细错误信息")

if __name__ == "__main__":
    main()
