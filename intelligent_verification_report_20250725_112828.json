{"summary": {"total_studios": 10, "total_prefixes": 11, "total_valid_prefixes": 11, "overall_success_rate": 1.0, "verification_time": 18.909992218017578, "total_requests": 13, "avg_requests_per_minute": 41.24803389695795, "timestamp": "2025-07-25T11:28:28.444696"}, "status_distribution": {"excellent": 10, "good": 0, "poor": 0, "failed": 0, "error": 0, "no_mappings": 0}, "range_discoveries": {"0-99": ["GDRD:gdrd", "NSFS:nsfs", "GMA:gma", "GAJK:gajk", "NGHJ:nghj", "DNJR:dnjr", "HOWS:hows", "CHRV:chrv", "MOND:18mond", "ALDN:aldn"], "200-299": ["MOND:mond"]}, "intelligent_config": {"min_delay": 0.1, "max_delay": 0.5, "max_requests_per_minute": 80, "batch_size": 25, "batch_delay": 15, "test_numbers": [1, 100, 275, 775, 200, 300, 500, 50, 800, 999], "max_tests_per_prefix": 4, "early_stop": true}, "detailed_results": {"GDRD": {"studio": "GDRD", "total_prefixes": 1, "valid_prefixes": ["gdrd"], "invalid_prefixes": [], "prefix_details": {"gdrd": {"prefix": "gdrd", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.3408992290496826, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "NSFS": {"studio": "NSFS", "total_prefixes": 1, "valid_prefixes": ["nsfs"], "invalid_prefixes": [], "prefix_details": {"nsfs": {"prefix": "nsfs", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.4376740455627441, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "GMA": {"studio": "GMA", "total_prefixes": 1, "valid_prefixes": ["gma"], "invalid_prefixes": [], "prefix_details": {"gma": {"prefix": "gma", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.3039960861206055, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "GAJK": {"studio": "GAJK", "total_prefixes": 1, "valid_prefixes": ["gajk"], "invalid_prefixes": [], "prefix_details": {"gajk": {"prefix": "gajk", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.446336269378662, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "NGHJ": {"studio": "NGHJ", "total_prefixes": 1, "valid_prefixes": ["nghj"], "invalid_prefixes": [], "prefix_details": {"nghj": {"prefix": "nghj", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.5909926891326904, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "DNJR": {"studio": "DNJR", "total_prefixes": 1, "valid_prefixes": ["dnjr"], "invalid_prefixes": [], "prefix_details": {"dnjr": {"prefix": "dnjr", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.267265796661377, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "HOWS": {"studio": "HOWS", "total_prefixes": 1, "valid_prefixes": ["hows"], "invalid_prefixes": [], "prefix_details": {"hows": {"prefix": "hows", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.3177227973937988, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "CHRV": {"studio": "CHRV", "total_prefixes": 1, "valid_prefixes": ["chrv"], "invalid_prefixes": [], "prefix_details": {"chrv": {"prefix": "chrv", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.3386304378509521, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MOND": {"studio": "MOND", "total_prefixes": 2, "valid_prefixes": ["mond", "18mond"], "invalid_prefixes": [], "prefix_details": {"mond": {"prefix": "mond", "successful_numbers": [275], "failed_numbers": [1, 100], "total_tests": 3, "success_count": 1, "is_valid": true, "confidence": 0.3333333333333333}, "18mond": {"prefix": "18mond", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 5.1087446212768555, "status": "excellent", "total_requests": 4, "verification_method": "intelligent_range_probing"}, "ALDN": {"studio": "ALDN", "total_prefixes": 1, "valid_prefixes": ["aldn"], "invalid_prefixes": [], "prefix_details": {"aldn": {"prefix": "aldn", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.7550251483917236, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}}}