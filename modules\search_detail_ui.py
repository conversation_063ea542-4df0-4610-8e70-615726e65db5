#!/usr/bin/env python3
"""
搜索详情页UI模块
提供完整的搜索详情页界面功能
"""

import streamlit as st
try:
    from .search_detail import SearchDetailModule
    from .ui_utils import UIUtils
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from modules.search_detail import SearchDetailModule
    from modules.ui_utils import UIUtils


class SearchDetailUI:
    """搜索详情页UI类"""
    
    def __init__(self):
        """初始化UI模块"""
        # 使用session_state缓存搜索模块，避免重复初始化
        if "search_detail_module" not in st.session_state:
            print("🔧 首次初始化搜索详情模块...")
            st.session_state.search_detail_module = SearchDetailModule()
            print("✅ 搜索详情模块已缓存到session_state")
        else:
            print("♻️ 从session_state获取已缓存的搜索详情模块")

        self.search_module = st.session_state.search_detail_module
        self.ui_utils = UIUtils()
    
    def render(self):
        """渲染搜索详情页界面"""
        st.markdown("### 🔍 增强搜索详情页")
        st.info("🚀 输入番号搜索DMM详情页，支持海报显示、自动重命名、手动输入等功能")

        # 初始化session state
        self._init_session_state()

        # 主要输入区域
        self._render_input_section()

        # 搜索按钮区域
        self._render_button_section()

        # 手动输入URL模式
        self._render_manual_url_section()

        # 显示搜索结果
        self._render_search_results()

        # 文件重命名功能
        self._render_rename_section()
    
    def _init_session_state(self):
        """初始化session state"""
        if "search_result" not in st.session_state:
            st.session_state.search_result = None
        if "poster_result" not in st.session_state:
            st.session_state.poster_result = None
        if "manual_url_mode" not in st.session_state:
            st.session_state.manual_url_mode = False
    
    def _render_input_section(self):
        """渲染输入区域"""
        col1, col2 = st.columns([3, 1])

        with col1:
            self.code = st.text_input(
                "📝 请输入番号（例如：IPX-123）", 
                placeholder="IPX-123", 
                key="search_code_input"
            )

        with col2:
            st.markdown("**功能选项：**")
            self.show_poster = st.checkbox("🖼️ 显示海报", value=True, key="show_poster_option")
            self.auto_save_data = st.checkbox("💾 自动保存数据", value=True, key="auto_save_option")
    
    def _render_button_section(self):
        """渲染按钮区域"""
        col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 1])

        with col_btn1:
            self.search_clicked = st.button("🔍 搜索DMM详情页", use_container_width=True, type="primary")

        with col_btn2:
            manual_mode_clicked = st.button("✏️ 手动输入URL", use_container_width=True)

        with col_btn3:
            clear_clicked = st.button("🗑️ 清空结果", use_container_width=True)

        # 处理按钮点击
        if clear_clicked:
            st.session_state.search_result = None
            st.session_state.poster_result = None
            st.session_state.manual_url_mode = False
            st.rerun()

        if manual_mode_clicked:
            st.session_state.manual_url_mode = not st.session_state.manual_url_mode
            st.rerun()
    
    def _render_manual_url_section(self):
        """渲染手动输入URL区域"""
        if not st.session_state.manual_url_mode:
            return
        
        st.markdown("---")
        st.markdown("#### ✏️ 手动输入详情页URL")
        st.info("当自动搜索失败时，您可以手动输入DMM详情页URL")

        manual_url = st.text_input(
            "🔗 请输入完整的DMM详情页URL",
            placeholder="https://www.dmm.co.jp/digital/videoa/-/detail/=/cid=xxxxx/",
            key="manual_url_input"
        )

        if st.button("🔍 解析手动URL", use_container_width=True):
            if manual_url.strip():
                with st.spinner("正在解析URL..."):
                    manual_result = self.search_module.parse_manual_dmm_url(manual_url.strip())
                    if manual_result["success"]:
                        # 从成功的CID中学习新的前缀规则
                        cid = manual_result["cid"]
                        learn_result = self.search_module.learn_from_successful_cid(cid)
                        print(f"🎓 学习结果: {learn_result}")

                        # 转换为标准格式
                        st.session_state.search_result = {
                            "success": True,
                            "code": self.code.strip() if self.code.strip() else "手动输入",
                            "cid": manual_result["cid"],
                            "label": manual_result["label"],
                            "url": manual_result["url"],
                            "message": manual_result["message"],
                            "learn_message": learn_result
                        }
                        st.success("✅ URL解析成功！")
                        if "已学习新的" in learn_result:
                            st.info(f"🎓 {learn_result}")
                        st.rerun()
                    else:
                        st.error(f"❌ {manual_result['message']}")
            else:
                st.warning("请输入有效的URL！")
    
    def _render_search_results(self):
        """渲染搜索结果 - 现在支持智能多结果显示"""
        # 执行搜索
        if self.search_clicked:
            self._handle_search()

        # 显示搜索结果
        if st.session_state.search_result:
            result = st.session_state.search_result

            st.markdown("---")

            if result["success"]:
                if result.get("search_type") == "multiple":
                    # 多重映射结果
                    self._render_multiple_choice_results(result)
                else:
                    # 单一结果（保持原有显示方式）
                    self._render_single_result(result)
            else:
                # 失败结果显示
                st.markdown("#### ❌ 搜索失败")
                st.error(result["message"])

                # 提供手动输入建议
                if not st.session_state.manual_url_mode:
                    st.info("💡 提示：如果自动搜索失败，您可以点击 '✏️ 手动输入URL' 按钮手动输入详情页链接")
    
    def _render_rename_section(self):
        """渲染文件重命名区域"""
        if st.session_state.search_result and st.session_state.search_result["success"]:
            self.ui_utils.show_rename_section(st.session_state.search_result)
    
    def _handle_search(self):
        """处理搜索逻辑"""
        if not self.code.strip():
            st.warning("请先输入番号！")
            return

        self.ui_utils.add_event("搜索详情页", "运行中", f"番号：{self.code}", progress=0)

        with st.spinner("🔍 正在搜索DMM详情页..."):
            # 执行搜索
            search_result = self.search_module.search_dmm_enhanced(self.code.strip())
            print(f"搜索结果: {search_result}")

            # 只有搜索成功时才更新结果，失败时保留原有结果并给出提示
            if search_result["success"]:
                st.session_state.search_result = search_result

                # 保存选项状态到不同的键
                st.session_state.current_show_poster = self.show_poster
                st.session_state.current_auto_save = self.auto_save_data

                # 如果搜索成功且需要显示海报
                if self.show_poster:
                    self._handle_poster_fetch(search_result)

                # 自动保存数据
                if self.auto_save_data:
                    self._handle_data_save(search_result)

                self.ui_utils.update_last_event(status="完成", progress=100, result="✅ 搜索成功，海报和数据处理完成")
            else:
                # 搜索失败，显示错误但不覆盖已有结果
                st.error(f"❌ 自动搜索失败: {search_result['message']}")
                self.ui_utils.update_last_event(status="失败", progress=100, result=search_result["message"])

        st.rerun()
    
    def _handle_poster_fetch(self, search_result):
        """处理海报获取"""
        with st.spinner("🖼️ 正在获取海报..."):
            try:
                cid = search_result.get("cid", "")
                print(f"准备获取海报，CID: {cid}")
                # 直接使用搜索结果中的CID获取海报，避免重复搜索
                poster_result = self.search_module.get_poster_with_cid(cid)
                st.session_state.poster_result = poster_result
                print(f"海报获取结果: {poster_result}")
            except Exception as e:
                error_msg = f"海报获取异常: {str(e)}"
                print(error_msg)
                import traceback
                traceback.print_exc()
                st.session_state.poster_result = {
                    "success": False,
                    "message": error_msg,
                    "poster_bytes": None,
                    "thumb_bytes": None
                }
    
    def _handle_data_save(self, search_result):
        """处理数据保存"""
        try:
            print(f"准备保存JSON数据: {search_result}")

            # 检查是否是多重结果
            if search_result.get("search_type") == "multiple":
                print("⚠️ 多重结果暂不自动保存，等待用户确认选择")
                st.session_state.search_result["save_message"] = "💡 多重结果需要确认选择后才能保存"
                return

            # 单一结果，正常保存
            save_result = self.search_module.save_dmm_data_to_json(search_result)
            print(f"JSON保存结果: {save_result}")
            if save_result["success"]:
                st.session_state.search_result["save_message"] = save_result["message"]
            else:
                st.session_state.search_result["save_message"] = f"❌ 保存失败: {save_result['message']}"
        except Exception as e:
            error_msg = f"JSON保存异常: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            st.session_state.search_result["save_message"] = f"❌ {error_msg}"

    def _render_multiple_choice_results(self, result: dict):
        """渲染多选择结果界面"""
        st.markdown("#### 🎯 智能检测到多个可能的映射")

        # 显示检测信息
        primary_count = len([r for r in result["results"] if r["is_primary"]])
        alternative_count = result["total_count"] - primary_count

        st.info(f"🧠 系统检测到该番号存在多个映射关系：{primary_count} 个主要映射，{alternative_count} 个备选映射")

        # 自动选择主要映射作为默认选项
        default_selection = 0

        # 创建选择选项
        options = []
        for i, res in enumerate(result["results"]):
            priority_icon = "🎯" if res["is_primary"] else "🔄"
            confidence_text = f"{res['confidence']:.0%}"
            mapping_desc = "主要映射" if res["is_primary"] else "备选映射"
            option_text = f"{priority_icon} {res['cid']} ({mapping_desc}, 置信度: {confidence_text})"
            options.append(option_text)

        # 选择框
        selected_index = st.selectbox(
            "请选择正确的映射结果：",
            range(len(options)),
            index=default_selection,
            format_func=lambda x: options[x],
            key="intelligent_result_selector",
            help="系统已自动选择最可能正确的主要映射，如果不正确请手动选择"
        )

        # 显示选中结果的详细信息
        if selected_index is not None:
            selected_result = result["results"][selected_index]

            col1, col2 = st.columns([3, 1])

            with col1:
                st.markdown("##### 📋 当前选择的结果")

                # 显示结果信息
                st.markdown(f"**番号:** {selected_result['code']}")
                st.markdown(f"**CID:** {selected_result['cid']}")
                st.markdown(f"**详情页:** [点击访问]({selected_result['url']})")

                # 额外显示映射信息
                mapping_type_text = "🎯 主要映射" if selected_result["is_primary"] else "🔄 备选映射"
                st.markdown(f"**映射类型:** {mapping_type_text}")
                st.markdown(f"**置信度:** {selected_result['confidence']:.0%}")

            with col2:
                # 确认按钮
                if st.button("✅ 确认选择", type="primary", key="confirm_intelligent_selection"):
                    # 将选中的结果转换为单一结果格式
                    confirmed_result = {
                        "success": True,
                        "search_type": "single",
                        "code": selected_result["code"],
                        "cid": selected_result["cid"],
                        "url": selected_result["url"],
                        "label": selected_result["label"],
                        "confidence": selected_result["confidence"],
                        "source": f"{selected_result['source']}_confirmed",
                        "mapping_confirmed": True
                    }

                    st.session_state.search_result = confirmed_result

                    # 🎓 多重结果确认选择时的自动学习
                    if (self.search_module.enable_auto_learning and
                        self.search_module.auto_learner and
                        not selected_result.get('is_primary', False)):  # 如果选择的不是主映射

                        try:
                            print(f"🎓 多重结果确认选择，尝试学习: {selected_result['code']} -> {selected_result['cid']}")
                            learning_result = self.search_module.auto_learner.learn_from_successful_search(
                                code=selected_result['code'],
                                found_cid=selected_result['cid'],
                                search_source="multi_mapping_confirmed"
                            )

                            if learning_result["success"]:
                                print(f"✅ 多重结果学习成功: {learning_result['message']}")
                                confirmed_result["learning_message"] = f"🎓 {learning_result['message']}"

                                # 刷新映射管理器缓存
                                if hasattr(self.search_module.fast_search, 'mapping_manager'):
                                    self.search_module.fast_search.mapping_manager._load_to_memory_cache()
                                    print("♻️ 映射管理器缓存已刷新")
                            else:
                                print(f"💡 多重结果学习跳过: {learning_result['message']}")
                                confirmed_result["learning_message"] = f"💡 {learning_result['message']}"

                        except Exception as e:
                            print(f"❌ 多重结果自动学习异常: {e}")
                            confirmed_result["learning_message"] = f"❌ 学习失败: {e}"

                    # 如果启用了自动保存，保存确认的结果
                    if st.session_state.get("current_auto_save", True):
                        self._handle_data_save(confirmed_result)

                    st.success("✅ 已确认选择！")
                    st.rerun()

            # 显示海报（如果启用）
            if st.session_state.get("current_show_poster", True):
                st.markdown("---")
                st.markdown("##### 🖼️ 海报预览")

                # 获取选中结果的海报
                try:
                    with st.spinner("🖼️ 正在获取海报..."):
                        poster_result = self.search_module.get_poster_with_cid(selected_result['cid'])
                        if poster_result["success"]:
                            self.ui_utils.show_poster_display(poster_result)
                        else:
                            st.info("🖼️ 未获取到海报图片")
                except Exception as e:
                    st.error(f"海报获取失败: {str(e)}")

    def _render_single_result(self, result: dict):
        """渲染单一结果（保持原有显示方式）"""
        st.markdown("#### 📋 搜索结果")

        # 成功结果显示
        col_info, col_poster = st.columns([1, 1])

        with col_info:
            st.success("✅ 搜索成功！")
            self.ui_utils.show_search_result_info(result)

        with col_poster:
            # 显示海报
            if st.session_state.get("poster_result"):
                self.ui_utils.show_poster_display(st.session_state.poster_result)
            elif st.session_state.get("current_show_poster", True):
                st.info("🖼️ 未获取到海报图片")
