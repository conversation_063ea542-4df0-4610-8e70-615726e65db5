#!/usr/bin/env python3
"""
DMM全站爬虫 - 从列表页爬取所有作品数据
目标: 爬取 https://video.dmm.co.jp/av/list/?page=1 到 page=417
提取: CID、番号、标题、详情页URL、厂商等信息
"""
import requests
import sqlite3
import time
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, parse_qs
import logging
import random

class DMMFullCrawler:
    """DMM全站爬虫"""
    
    def __init__(self, db_file: str = "dmm_full_database.db"):
        self.db_file = db_file
        self.session = requests.Session()
        
        # 优化的请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
        
        # 爬取配置
        self.config = {
            'start_page': 1,
            'end_page': 417,
            'min_delay': 2.0,       # 最小延迟2秒
            'max_delay': 5.0,       # 最大延迟5秒
            'batch_size': 50,       # 每50页休息一次
            'batch_delay': 60,      # 批次休息60秒
            'retry_times': 3,       # 重试次数
            'timeout': 15,          # 请求超时
        }
        
        # 统计信息
        self.stats = {
            'total_pages': 0,
            'total_items': 0,
            'success_pages': 0,
            'failed_pages': 0,
            'start_time': None,
            'errors': []
        }
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('dmm_crawler.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库
        self.init_database()
        
        # 处理年龄验证
        self.handle_age_verification()
    
    def init_database(self):
        """初始化数据库"""
        print("📊 初始化数据库...")
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 创建主表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dmm_works (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cid TEXT UNIQUE NOT NULL,
                    title TEXT,
                    number TEXT,
                    studio TEXT,
                    detail_url TEXT,
                    thumbnail_url TEXT,
                    price TEXT,
                    release_date TEXT,
                    duration TEXT,
                    page_number INTEGER,
                    crawl_time TEXT,
                    raw_data TEXT,
                    UNIQUE(cid)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_cid ON dmm_works(cid)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_number ON dmm_works(number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_studio ON dmm_works(studio)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_page ON dmm_works(page_number)')
            
            # 创建爬取进度表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_progress (
                    page_number INTEGER PRIMARY KEY,
                    status TEXT,
                    items_count INTEGER,
                    crawl_time TEXT,
                    error_message TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            print("✅ 数据库初始化完成")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
    
    def handle_age_verification(self):
        """处理年龄验证"""
        print("🔐 处理年龄验证...")
        
        try:
            # 访问年龄验证页面
            age_check_url = "https://www.dmm.co.jp/age_check/=/declared=yes/?rurl=https%3A%2F%2Fvideo.dmm.co.jp%2F"
            
            response = self.session.get(age_check_url, timeout=self.config['timeout'])
            
            if response.status_code == 200:
                # 设置年龄验证相关的cookies
                self.session.cookies.set('age_check_done', '1', domain='.dmm.co.jp')
                self.session.cookies.set('ckcy', '1', domain='.dmm.co.jp')
                print("✅ 年龄验证完成")
                return True
            else:
                print(f"⚠️ 年龄验证响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 年龄验证失败: {e}")
            return False
    
    def safe_delay(self):
        """安全延迟"""
        delay = random.uniform(self.config['min_delay'], self.config['max_delay'])
        time.sleep(delay)
    
    def extract_work_info(self, item_element, page_number: int) -> Optional[Dict]:
        """从单个作品元素中提取信息"""
        try:
            work_info = {
                'page_number': page_number,
                'crawl_time': datetime.now().isoformat()
            }

            # 调试：打印元素的基本信息
            element_classes = item_element.get('class', [])
            print(f"        🔍 处理元素: {item_element.name} class={element_classes}")

            # 提取详情页链接和CID - 尝试多种选择器
            link_selectors = [
                'a[href*="/detail/"]',
                'a[href*="cid="]',
                'a',
                '.tmb a',
                '.item a'
            ]

            link_element = None
            for selector in link_selectors:
                link_element = item_element.select_one(selector)
                if link_element and link_element.get('href'):
                    print(f"        ✅ 找到链接: {selector}")
                    break

            if link_element and link_element.get('href'):
                href = link_element['href']
                detail_url = urljoin('https://video.dmm.co.jp', href)
                work_info['detail_url'] = detail_url

                print(f"        🔗 详情页URL: {detail_url}")

                # 从URL中提取CID - 尝试多种模式
                cid_patterns = [
                    r'/cid=([^/&]+)',
                    r'cid=([^/&]+)',
                    r'/([^/]+)/$',  # 最后一段路径
                ]

                cid = None
                for pattern in cid_patterns:
                    cid_match = re.search(pattern, detail_url)
                    if cid_match:
                        cid = cid_match.group(1)
                        print(f"        🎯 提取CID: {cid} (模式: {pattern})")
                        break

                if cid:
                    work_info['cid'] = cid
                else:
                    print(f"        ❌ 无法提取CID from: {detail_url}")
                    return None
            else:
                print(f"        ❌ 未找到链接元素")
                return None
            
            # 提取标题
            title_element = item_element.find('span', class_='txt') or item_element.find('p', class_='txt')
            if title_element:
                work_info['title'] = title_element.get_text(strip=True)
            
            # 提取缩略图
            img_element = item_element.find('img')
            if img_element and img_element.get('src'):
                work_info['thumbnail_url'] = img_element['src']
            
            # 提取价格信息
            price_element = item_element.find('span', class_='price') or item_element.find('p', class_='price')
            if price_element:
                work_info['price'] = price_element.get_text(strip=True)
            
            # 从标题或其他地方提取番号
            title = work_info.get('title', '')
            number_match = re.search(r'([A-Z]+[-_]?\d+)', title.upper())
            if number_match:
                work_info['number'] = number_match.group(1)
            
            # 提取厂商信息（如果有的话）
            studio_element = item_element.find('span', class_='maker') or item_element.find('p', class_='maker')
            if studio_element:
                work_info['studio'] = studio_element.get_text(strip=True)
            
            # 保存原始HTML数据
            work_info['raw_data'] = str(item_element)
            
            return work_info
            
        except Exception as e:
            self.logger.error(f"提取作品信息失败: {e}")
            return None
    
    def crawl_single_page(self, page_number: int) -> Tuple[bool, List[Dict]]:
        """爬取单页数据"""
        url = f"https://video.dmm.co.jp/av/list/?page={page_number}"

        for attempt in range(self.config['retry_times']):
            try:
                print(f"   🔍 爬取第 {page_number} 页 (尝试 {attempt + 1}/{self.config['retry_times']})")

                response = self.session.get(url, timeout=self.config['timeout'])

                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')

                    # 调试：保存HTML到文件查看结构
                    if page_number <= 15:  # 保存更多页面用于调试
                        with open(f'debug_page_{page_number}.html', 'w', encoding='utf-8') as f:
                            f.write(response.text)
                        print(f"      🔍 调试：已保存 debug_page_{page_number}.html")

                        # 打印响应的基本信息
                        print(f"      📊 响应状态: {response.status_code}")
                        print(f"      📊 响应长度: {len(response.text)} 字符")
                        print(f"      📊 Content-Type: {response.headers.get('Content-Type', 'Unknown')}")

                        # 检查是否被重定向
                        if response.history:
                            print(f"      🔄 发生重定向: {len(response.history)} 次")
                            for i, resp in enumerate(response.history):
                                print(f"         重定向 {i+1}: {resp.status_code} -> {resp.url}")

                        # 打印页面开头内容
                        preview = response.text[:500].replace('\n', ' ').replace('\r', ' ')
                        print(f"      📄 页面预览: {preview}...")

                    # 调试：打印页面基本信息
                    title = soup.find('title')
                    print(f"      📄 页面标题: {title.text if title else 'None'}")

                    # 尝试多种可能的选择器
                    selectors_to_try = [
                        ('li', {'class': 'tmb'}),
                        ('div', {'class': 'tmb'}),
                        ('li', {'class': 'item'}),
                        ('div', {'class': 'item'}),
                        ('li', {}),  # 所有li元素
                        ('div', {'class': 'productBox'}),
                        ('div', {'class': 'product'}),
                        ('article', {}),
                    ]

                    work_items = []
                    for tag, attrs in selectors_to_try:
                        items = soup.find_all(tag, attrs) if attrs else soup.find_all(tag)
                        if items:
                            print(f"      🎯 找到 {len(items)} 个 {tag} 元素 (属性: {attrs})")
                            work_items = items
                            break

                    if not work_items:
                        print(f"      ❌ 未找到作品列表元素")
                        # 调试：打印页面的主要结构
                        main_divs = soup.find_all('div', limit=10)
                        print(f"      🔍 页面主要div元素: {[div.get('class') for div in main_divs]}")
                        return True, []

                    works_data = []

                    for i, item in enumerate(work_items[:10]):  # 只处理前10个用于调试
                        work_info = self.extract_work_info(item, page_number)
                        if work_info and work_info.get('cid'):
                            works_data.append(work_info)
                        elif i < 3:  # 调试前3个元素
                            print(f"      🔍 调试元素 {i}: {str(item)[:200]}...")

                    print(f"      ✅ 提取到 {len(works_data)} 个作品")
                    return True, works_data

                else:
                    print(f"      ⚠️ HTTP状态码: {response.status_code}")

            except Exception as e:
                print(f"      ❌ 爬取异常: {e}")
                if attempt < self.config['retry_times'] - 1:
                    print(f"      🔄 等待重试...")
                    time.sleep(5)

        return False, []
    
    def save_works_to_db(self, works_data: List[Dict]):
        """保存作品数据到数据库"""
        if not works_data:
            return
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            for work in works_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO dmm_works 
                    (cid, title, number, studio, detail_url, thumbnail_url, price, 
                     release_date, duration, page_number, crawl_time, raw_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    work.get('cid'),
                    work.get('title'),
                    work.get('number'),
                    work.get('studio'),
                    work.get('detail_url'),
                    work.get('thumbnail_url'),
                    work.get('price'),
                    work.get('release_date'),
                    work.get('duration'),
                    work.get('page_number'),
                    work.get('crawl_time'),
                    work.get('raw_data')
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")
    
    def update_crawl_progress(self, page_number: int, status: str, items_count: int = 0, error_message: str = None):
        """更新爬取进度"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO crawl_progress 
                (page_number, status, items_count, crawl_time, error_message)
                VALUES (?, ?, ?, ?, ?)
            ''', (page_number, status, items_count, datetime.now().isoformat(), error_message))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"更新进度失败: {e}")
    
    def get_crawl_progress(self) -> Dict:
        """获取爬取进度"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM crawl_progress WHERE status = "success"')
            completed_pages = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM crawl_progress WHERE status = "failed"')
            failed_pages = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM dmm_works')
            total_works = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'completed_pages': completed_pages,
                'failed_pages': failed_pages,
                'total_works': total_works,
                'total_pages': self.config['end_page'] - self.config['start_page'] + 1
            }
            
        except Exception as e:
            self.logger.error(f"获取进度失败: {e}")
            return {}
    
    def start_crawling(self, resume: bool = True):
        """开始爬取"""
        print("🚀 开始DMM全站爬取")
        print("=" * 60)
        
        print(f"📋 爬取配置:")
        print(f"   页面范围: {self.config['start_page']} - {self.config['end_page']}")
        print(f"   预计作品数: {(self.config['end_page'] - self.config['start_page'] + 1) * 120}")
        print(f"   请求间隔: {self.config['min_delay']}-{self.config['max_delay']} 秒")
        print(f"   批次大小: {self.config['batch_size']} 页")
        
        # 获取已完成的页面
        completed_pages = set()
        if resume:
            try:
                conn = sqlite3.connect(self.db_file)
                cursor = conn.cursor()
                cursor.execute('SELECT page_number FROM crawl_progress WHERE status = "success"')
                completed_pages = {row[0] for row in cursor.fetchall()}
                conn.close()
                print(f"📊 恢复爬取，已完成 {len(completed_pages)} 页")
            except:
                pass
        
        self.stats['start_time'] = datetime.now()
        
        for page_num in range(self.config['start_page'], self.config['end_page'] + 1):
            if resume and page_num in completed_pages:
                print(f"⏭️ 跳过已完成页面: {page_num}")
                continue
            
            print(f"\n📄 处理第 {page_num} 页 ({page_num - self.config['start_page'] + 1}/{self.config['end_page'] - self.config['start_page'] + 1})")
            
            # 爬取单页
            success, works_data = self.crawl_single_page(page_num)
            
            if success:
                # 保存数据
                self.save_works_to_db(works_data)
                self.update_crawl_progress(page_num, 'success', len(works_data))
                
                self.stats['success_pages'] += 1
                self.stats['total_items'] += len(works_data)
                
                print(f"   ✅ 第 {page_num} 页完成，保存 {len(works_data)} 个作品")
                
            else:
                self.update_crawl_progress(page_num, 'failed', 0, "爬取失败")
                self.stats['failed_pages'] += 1
                print(f"   ❌ 第 {page_num} 页失败")
            
            # 显示进度统计
            if page_num % 10 == 0:
                self.print_progress_stats()
            
            # 批次休息
            if page_num % self.config['batch_size'] == 0:
                print(f"😴 批次休息 {self.config['batch_delay']} 秒...")
                time.sleep(self.config['batch_delay'])
            else:
                self.safe_delay()
        
        # 最终统计
        self.print_final_stats()
    
    def print_progress_stats(self):
        """打印进度统计"""
        progress = self.get_crawl_progress()
        
        print(f"\n📊 进度统计:")
        print(f"   已完成页面: {progress.get('completed_pages', 0)}")
        print(f"   失败页面: {progress.get('failed_pages', 0)}")
        print(f"   总作品数: {progress.get('total_works', 0)}")
        
        if self.stats['start_time']:
            elapsed = datetime.now() - self.stats['start_time']
            print(f"   已用时间: {elapsed}")
    
    def print_final_stats(self):
        """打印最终统计"""
        print("\n" + "=" * 60)
        print("🎉 DMM全站爬取完成")
        print("=" * 60)
        
        progress = self.get_crawl_progress()
        
        print(f"📊 最终统计:")
        print(f"   成功页面: {progress.get('completed_pages', 0)}")
        print(f"   失败页面: {progress.get('failed_pages', 0)}")
        print(f"   总作品数: {progress.get('total_works', 0)}")
        
        if self.stats['start_time']:
            elapsed = datetime.now() - self.stats['start_time']
            print(f"   总用时: {elapsed}")
        
        print(f"💾 数据库文件: {self.db_file}")
        print("✅ 爬取任务完成")

def main():
    """主函数"""
    print("🕷️ DMM全站爬虫")
    print("=" * 60)
    
    print("📋 任务说明:")
    print("   - 爬取DMM视频列表页 1-417 页")
    print("   - 每页约120个作品，总计约50,000个作品")
    print("   - 提取CID、番号、标题、详情页等信息")
    print("   - 保存到SQLite数据库")
    
    # 用户确认
    user_input = input("\n是否开始爬取？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("爬取已取消")
        return
    
    # 询问是否恢复爬取
    resume_input = input("是否恢复之前的爬取进度？(Y/n): ").strip().lower()
    resume = resume_input != 'n'
    
    # 开始爬取
    crawler = DMMFullCrawler()
    crawler.start_crawling(resume=resume)

if __name__ == "__main__":
    main()
