import os
import re
import aiohttp
from bs4 import BeautifulSoup
import asyncio
import json

def normalize_dmm_code(text):
    code = text.replace(" ", "").replace("-", "00").replace("_", "00")
    return code.lower()

def generate_cid_try_list(prefix, num):
    num5 = f"{num:05d}"
    try_list = [f"{prefix}{num}", f"{prefix}{num5}"]

    # 优先查找 prefix_h_prefix_map.json
    try:
        with open("prefix_h_prefix_map.json", "r", encoding="utf-8") as f:
            prefix_h_map = json.load(f)
    except Exception:
        prefix_h_map = {}

    h_prefixes = prefix_h_map.get(prefix, None)
    if h_prefixes is not None:
        for n in h_prefixes:
            h_prefix = f"h_{int(n):03d}"
            try_list.append(f"{h_prefix}{prefix}{num5}")
    else:
        # 没有映射时遍历所有 h_prefix_numbers
        try:
            with open("h_prefix_numbers.json", "r", encoding="utf-8") as f:
                h_prefix_numbers = json.load(f)
        except Exception:
            h_prefix_numbers = []
        for n in h_prefix_numbers:
            h_prefix = f"h_{int(n):03d}"
            try_list.append(f"{h_prefix}{prefix}{num5}")

    # 读取 number_prefixes.json
    try:
        with open("number_prefixes.json", "r", encoding="utf-8") as f:
            number_prefixes = json.load(f)
    except Exception:
        number_prefixes = []
    for pre in number_prefixes:
        try_list.append(f"{pre}{prefix}{num5}")
    return list(dict.fromkeys(try_list))

async def fetch_cid_detail(session, try_cid, headers):
    detail_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={try_cid}/?i3_ref=search&i3_ord=1&i3_pst=1"
    try:
        async with session.get(detail_url, headers=headers, timeout=5) as resp:
            html = await resp.text()
            print(f"调试: cid={try_cid}, status={resp.status}, title={re.search(r'<title>(.*?)</title>', html, re.I|re.S).group(1) if re.search(r'<title>(.*?)</title>', html, re.I|re.S) else '无标题'}")
        soup = BeautifulSoup(html, "html.parser")
        label_tag = soup.find("a", href=re.compile(r"/digital/videoa/-/list/=/article=maker/id="))
        label = label_tag.text.strip() if label_tag else None
        if label or "ページが見つかりません" not in html:
            # 自动维护 h_prefix_numbers 和 number_prefixes
            print(f"成功找到详情页，开始更新配置文件: try_cid={try_cid}")

            # 处理h_prefix格式 (如: h_1462cawd00849)
            h_prefix_match = re.match(r"h_(\d{3,4})(.+)", try_cid)
            if h_prefix_match:
                h_num = int(h_prefix_match.group(1))
                remaining_part = h_prefix_match.group(2)

                print(f"检测到h_prefix格式: h_num={h_num}, remaining={remaining_part}")

                # 更新 h_prefix_numbers.json
                try:
                    with open("h_prefix_numbers.json", "r", encoding="utf-8") as f:
                        h_numbers = json.load(f)
                except Exception:
                    h_numbers = []

                if h_num not in h_numbers:
                    h_numbers.append(h_num)
                    h_numbers.sort()
                    with open("h_prefix_numbers.json", "w", encoding="utf-8") as f:
                        json.dump(h_numbers, f, ensure_ascii=False, indent=2)
                    print(f"成功添加h_prefix: {h_num}")

                # 更新 prefix_h_prefix_map.json
                try:
                    with open("prefix_h_prefix_map.json", "r", encoding="utf-8") as f:
                        prefix_h_map = json.load(f)
                except Exception:
                    prefix_h_map = {}

                # 提取实际前缀（去掉数字部分）
                prefix_match = re.match(r"^([a-z]+)", remaining_part)
                if prefix_match:
                    actual_prefix = prefix_match.group(1)

                    if actual_prefix not in prefix_h_map:
                        prefix_h_map[actual_prefix] = []
                    if h_num not in prefix_h_map[actual_prefix]:
                        prefix_h_map[actual_prefix].append(h_num)
                        prefix_h_map[actual_prefix].sort()
                        with open("prefix_h_prefix_map.json", "w", encoding="utf-8") as f:
                            json.dump(prefix_h_map, f, ensure_ascii=False, indent=2)
                        print(f"成功添加前缀映射: {actual_prefix} -> {h_num}")

            # 处理数字前缀格式 (如: 1cawd00849, 140cawd00849)
            number_prefix_match = re.match(r"^(\d{1,4})([a-z]+)", try_cid)
            if number_prefix_match:
                num_prefix = number_prefix_match.group(1)

                print(f"检测到数字前缀格式: num_prefix={num_prefix}")

                try:
                    with open("number_prefixes.json", "r", encoding="utf-8") as f:
                        number_prefixes = json.load(f)
                except Exception:
                    number_prefixes = []

                if num_prefix not in number_prefixes:
                    number_prefixes.append(num_prefix)
                    # 按数字大小排序，非数字的放在后面
                    number_prefixes.sort(key=lambda x: int(x) if x.isdigit() else float('inf'))
                    with open("number_prefixes.json", "w", encoding="utf-8") as f:
                        json.dump(number_prefixes, f, ensure_ascii=False, indent=2)
                    print(f"成功添加数字前缀: {num_prefix}")
            return {
                "cid": try_cid,
                "label": label,
                "url": detail_url
            }
    except Exception as e:
        print(f"fetch_cid_detail异常: {e}")
        return None
    return None

async def search_dmm_detail(code):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Cookie": "age_check_done=1; cklg=ja"
    }
    code = code.strip()
    cid = normalize_dmm_code(code)
    prefix_match = re.match(r"^([a-z0-9]+?)(\d+)$", cid)
    if not prefix_match:
        return None
    prefix = prefix_match.group(1)
    num = int(prefix_match.group(2))
    try_list = generate_cid_try_list(prefix, num)
    async with aiohttp.ClientSession() as session:
        for try_cid in try_list:
            result = await fetch_cid_detail(session, try_cid, headers)
            if result:
                return result
    return None

async def process_file(file, session, headers, root):
    # 支持 -4K、-C、-4K-C、-cd1~cd9、-4K-cd1、-C-cd1、-4K-C-cd1 等各种后缀
    match = re.match(
        r'^([A-Z0-9]+-\d+)'                  # base_code: 如 C-2564
        r'((?:-(?:4K|C|4K-C))?'              # 可选 -4K/-C/-4K-C
        r'(?:-cd\d+)?'                       # 可选 -cd1~cd99
        r')\.mp4$', file, re.I)
    if not match:
        return f"{file} 已经规范化，跳过"
    base_code = match.group(1)
    suffix = file[len(base_code):-4]
    cid = normalize_dmm_code(base_code)
    prefix_match = re.match(r"^([a-z0-9]+?)(\d+)$", cid)
    if not prefix_match:
        return f"{file} CID前缀不匹配，跳过"
    prefix = prefix_match.group(1)
    num = int(prefix_match.group(2))
    try_list = generate_cid_try_list(prefix, num)
    print(f"处理文件: {file}, prefix: {prefix}, num: {num}, try_list: {try_list}")
    found = None
    for try_cid in try_list:
        result = await fetch_cid_detail(session, try_cid, headers)
        if result:
            found = result['cid']
            break
    if not found:
        return f"{file} 未找到DMM详情，跳过"
    if suffix:
        new_name = f"{base_code} ({found}){suffix}.mp4"
    else:
        new_name = f"{base_code} ({found}).mp4"
    old_path = os.path.join(root, file)
    new_path = os.path.join(root, new_name)
    if old_path != new_path:
        try:
            os.rename(old_path, new_path)
            return f"已重命名: {file} -> {new_name}"
        except Exception as e:
            return f"重命名失败: {file} -> {new_name}，错误: {e}"
    else:
        return f"无需重命名: {file}"

async def rename_files_with_dmm_info(search_dir, max_concurrent=3):
    msg_lines = []
    headers = {
        "User-Agent": "...",
        "Cookie": "age_check_done=1; cklg=ja"
    }
    sem = asyncio.Semaphore(max_concurrent)

    async def sem_task(file, session, headers, root):
        async with sem:
            result = await process_file(file, session, headers, root)
            await asyncio.sleep(1)  # 每个任务后延时，进一步降低风控概率
            return result

    tasks = []
    async with aiohttp.ClientSession() as session:
        for root, dirs, files in os.walk(search_dir):
            for file in files:
                if not file.lower().endswith('.mp4'):
                    continue
                tasks.append(sem_task(file, session, headers, root))
        results = await asyncio.gather(*tasks)
        msg_lines.extend(results)
    return msg_lines

async def fetch_dmm_info(session, url):
    async with session.get(url) as resp:
        return await resp.text()

async def batch_fetch(urls):
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_dmm_info(session, url) for url in urls]
        return await asyncio.gather(*tasks)