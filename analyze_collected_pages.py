#!/usr/bin/env python3
"""
分析收集的DMM页面工具
"""
import os
import re
import json
from bs4 import BeautifulSoup

def analyze_html_file(file_path: str):
    """分析单个HTML文件"""
    print(f"\n🔍 分析文件: {file_path}")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # 1. 基本信息
        print("📊 基本信息:")
        title = soup.find('title')
        if title:
            print(f"   标题: {title.get_text().strip()}")
        
        print(f"   文件大小: {len(content)} 字符")
        print(f"   HTML标签数: {len(soup.find_all())}")
        
        # 2. 链接分析
        print("\n🔗 链接分析:")
        all_links = soup.find_all('a', href=True)
        print(f"   总链接数: {len(all_links)}")
        
        # 分类链接
        cid_links = [link for link in all_links if 'cid=' in link.get('href', '')]
        detail_links = [link for link in all_links if '/detail/' in link.get('href', '')]
        videoa_links = [link for link in all_links if '/videoa/' in link.get('href', '')]
        
        print(f"   CID链接: {len(cid_links)}")
        print(f"   详情链接: {len(detail_links)}")
        print(f"   VideoA链接: {len(videoa_links)}")
        
        # 显示链接示例
        if cid_links:
            print("   CID链接示例:")
            for i, link in enumerate(cid_links[:5], 1):
                href = link.get('href', '')
                text = link.get_text().strip()[:50]
                print(f"      {i}. {href}")
                if text:
                    print(f"         文本: {text}")
        
        # 3. CID文本搜索
        print("\n🎯 CID文本搜索:")
        
        # ID厂商的CID模式
        id_patterns = [
            r'\b(\d+id\d{5})\b',      # 5531id00021
            r'\b(h_\d+id\d{5})\b',    # h_113id00021
            r'\b([a-z]+id\d{5})\b',   # mbddid00021
        ]
        
        found_cids = set()
        for pattern in id_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            found_cids.update([match.lower() for match in matches])
        
        print(f"   找到的CID: {len(found_cids)}")
        if found_cids:
            for i, cid in enumerate(sorted(found_cids), 1):
                print(f"      {i}. {cid}")
        
        # 4. JavaScript分析
        print("\n📜 JavaScript分析:")
        script_tags = soup.find_all('script')
        print(f"   Script标签数: {len(script_tags)}")
        
        js_cids = set()
        js_with_cid = 0
        
        for script in script_tags:
            script_content = script.get_text()
            if 'cid' in script_content.lower():
                js_with_cid += 1
                
                # 查找JavaScript中的CID
                for pattern in id_patterns:
                    matches = re.findall(pattern, script_content, re.IGNORECASE)
                    js_cids.update([match.lower() for match in matches])
        
        print(f"   包含'cid'的Script: {js_with_cid}")
        print(f"   JavaScript中的CID: {len(js_cids)}")
        
        if js_cids:
            for i, cid in enumerate(sorted(js_cids), 1):
                print(f"      {i}. {cid}")
        
        # 5. JSON数据搜索
        print("\n📋 JSON数据搜索:")
        
        # 查找可能的JSON数据
        json_patterns = [
            r'var\s+\w+\s*=\s*(\{[^}]*cid[^}]*\})',
            r'var\s+\w+\s*=\s*(\[[^\]]*cid[^\]]*\])',
            r'"cid"\s*:\s*"([^"]+)"',
            r"'cid'\s*:\s*'([^']+)'",
        ]
        
        json_data = []
        for pattern in json_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            json_data.extend(matches)
        
        print(f"   找到的JSON数据: {len(json_data)}")
        for i, data in enumerate(json_data[:5], 1):  # 只显示前5个
            print(f"      {i}. {data[:100]}...")
        
        # 6. CSS类名分析
        print("\n🎨 CSS类名分析:")
        
        # 查找可能的产品容器类名
        all_elements = soup.find_all(attrs={'class': True})
        class_names = set()
        
        for element in all_elements:
            classes = element.get('class', [])
            for cls in classes:
                if any(keyword in cls.lower() for keyword in ['product', 'item', 'card', 'tile', 'thumb']):
                    class_names.add(cls)
        
        print(f"   相关CSS类名: {len(class_names)}")
        for cls in sorted(class_names)[:10]:  # 只显示前10个
            print(f"      .{cls}")
        
        # 7. 表单和输入分析
        print("\n📝 表单分析:")
        forms = soup.find_all('form')
        inputs = soup.find_all('input')
        
        print(f"   表单数: {len(forms)}")
        print(f"   输入框数: {len(inputs)}")
        
        # 8. 特殊标记搜索
        print("\n🔍 特殊标记搜索:")
        
        # 查找可能的数据属性
        data_attrs = []
        for element in soup.find_all(attrs=lambda x: x and any(attr.startswith('data-') for attr in x.keys())):
            for attr, value in element.attrs.items():
                if attr.startswith('data-') and 'cid' in str(value).lower():
                    data_attrs.append(f"{attr}={value}")
        
        print(f"   包含CID的data属性: {len(data_attrs)}")
        for attr in data_attrs[:5]:
            print(f"      {attr}")
        
        # 9. 总结
        print("\n📊 分析总结:")
        total_cids = len(found_cids | js_cids)
        print(f"   总发现CID数: {total_cids}")
        print(f"   HTML中的CID: {len(found_cids)}")
        print(f"   JavaScript中的CID: {len(js_cids)}")
        print(f"   JSON数据片段: {len(json_data)}")
        print(f"   相关CSS类: {len(class_names)}")
        
        if total_cids > 0:
            print("   ✅ 页面包含CID数据")
        else:
            print("   ❌ 页面不包含CID数据")
        
        return {
            'file': file_path,
            'total_cids': total_cids,
            'html_cids': list(found_cids),
            'js_cids': list(js_cids),
            'json_data': json_data,
            'css_classes': list(class_names),
            'links': len(all_links),
            'cid_links': len(cid_links)
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_all_collected_pages():
    """分析所有收集的页面"""
    print("🛠️ DMM页面分析工具")
    print("=" * 80)
    
    # 预期的文件列表
    expected_files = [
        'dmm_search_id00021.html',
        'dmm_detail_5533id00021.html',
        'dmm_detail_5531id00021.html',
        'dmm_detail_h_113id00021.html',
        'dmm_search_ssis00001.html'
    ]
    
    print("📋 预期分析的文件:")
    for file in expected_files:
        exists = "✅" if os.path.exists(file) else "❌"
        print(f"   {exists} {file}")
    
    # 分析存在的文件
    results = []
    for file in expected_files:
        if os.path.exists(file):
            result = analyze_html_file(file)
            if result:
                results.append(result)
    
    # 生成分析报告
    if results:
        print("\n" + "=" * 80)
        print("📊 综合分析报告")
        print("=" * 80)
        
        total_files = len(results)
        files_with_cids = sum(1 for r in results if r['total_cids'] > 0)
        
        print(f"分析文件数: {total_files}")
        print(f"包含CID的文件: {files_with_cids}")
        
        if files_with_cids > 0:
            print("\n✅ 发现CID数据的文件:")
            for result in results:
                if result['total_cids'] > 0:
                    print(f"   📄 {result['file']}: {result['total_cids']} 个CID")
            
            print("\n💡 建议的优化方向:")
            
            # 根据分析结果给出建议
            has_js_cids = any(r['js_cids'] for r in results)
            has_html_cids = any(r['html_cids'] for r in results)
            has_json_data = any(r['json_data'] for r in results)
            
            if has_js_cids:
                print("   🔧 JavaScript渲染：考虑使用Selenium或类似工具")
            if has_html_cids:
                print("   🔧 HTML解析：更新CSS选择器")
            if has_json_data:
                print("   🔧 JSON数据：解析JavaScript中的JSON数据")
            
        else:
            print("\n❌ 所有文件都不包含CID数据")
            print("💡 可能的原因:")
            print("   - 页面完全依赖JavaScript渲染")
            print("   - 数据通过AJAX异步加载")
            print("   - 需要特殊的认证或参数")
            print("   - 反爬虫机制阻止")
    
    else:
        print("\n❌ 没有找到可分析的文件")
        print("💡 请先收集页面文件，然后重新运行分析")

def main():
    """主函数"""
    analyze_all_collected_pages()

if __name__ == "__main__":
    main()
