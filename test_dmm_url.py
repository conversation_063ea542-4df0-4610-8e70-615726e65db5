#!/usr/bin/env python3
"""
测试DMM URL访问
"""
import requests
from bs4 import BeautifulSoup

def test_dmm_url():
    """测试DMM URL访问"""
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    }
    
    # 测试不同的URL
    urls_to_test = [
        "https://video.dmm.co.jp/av/list/?page=1",
        "https://www.dmm.co.jp/digital/videoa/-/list/",
        "https://www.dmm.co.jp/digital/videoa/-/list/=/page=1/",
        "https://video.dmm.co.jp/",
    ]
    
    session = requests.Session()
    session.headers.update(headers)
    
    # 先处理年龄验证
    print("🔐 处理年龄验证...")
    try:
        age_check_url = "https://www.dmm.co.jp/age_check/=/declared=yes/?rurl=https%3A%2F%2Fvideo.dmm.co.jp%2F"
        response = session.get(age_check_url, timeout=10)
        session.cookies.set('age_check_done', '1', domain='.dmm.co.jp')
        session.cookies.set('ckcy', '1', domain='.dmm.co.jp')
        print("✅ 年龄验证完成")
    except Exception as e:
        print(f"❌ 年龄验证失败: {e}")
    
    for i, url in enumerate(urls_to_test, 1):
        print(f"\n🔍 测试URL {i}: {url}")
        
        try:
            response = session.get(url, timeout=15)
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应长度: {len(response.text)} 字符")
            print(f"   Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            
            if response.history:
                print(f"   重定向次数: {len(response.history)}")
                for j, resp in enumerate(response.history):
                    print(f"      重定向 {j+1}: {resp.status_code} -> {resp.url}")
            
            # 解析HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            title = soup.find('title')
            print(f"   页面标题: {title.text if title else 'None'}")
            
            # 查找可能的作品列表元素
            possible_selectors = [
                ('li', 'tmb'),
                ('div', 'tmb'),
                ('li', 'item'),
                ('div', 'item'),
                ('article', None),
                ('div', 'product'),
            ]
            
            for tag, class_name in possible_selectors:
                if class_name:
                    elements = soup.find_all(tag, class_=class_name)
                else:
                    elements = soup.find_all(tag)
                
                if elements:
                    print(f"   找到 {len(elements)} 个 {tag}.{class_name or ''} 元素")
                    
                    # 检查前几个元素是否包含链接
                    for k, elem in enumerate(elements[:3]):
                        links = elem.find_all('a', href=True)
                        if links:
                            print(f"      元素 {k+1}: 包含 {len(links)} 个链接")
                            for link in links[:2]:
                                href = link.get('href', '')
                                if 'cid=' in href or '/detail/' in href:
                                    print(f"         链接: {href}")
            
            # 保存HTML文件
            with open(f'test_url_{i}.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"   已保存: test_url_{i}.html")
            
            # 打印页面开头
            preview = response.text[:300].replace('\n', ' ').replace('\r', ' ')
            print(f"   页面预览: {preview}...")
            
        except Exception as e:
            print(f"   ❌ 访问失败: {e}")

if __name__ == "__main__":
    test_dmm_url()
