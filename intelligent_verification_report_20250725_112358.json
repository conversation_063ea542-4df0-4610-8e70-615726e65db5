{"summary": {"total_studios": 10, "total_prefixes": 11, "total_valid_prefixes": 0, "overall_success_rate": 0.0, "verification_time": 0.0026679039001464844, "total_requests": 0, "avg_requests_per_minute": 0.0, "timestamp": "2025-07-25T11:23:58.124310"}, "status_distribution": {"excellent": 0, "good": 0, "poor": 0, "failed": 0, "error": 10, "no_mappings": 0}, "range_discoveries": {}, "intelligent_config": {"min_delay": 0.1, "max_delay": 0.5, "max_requests_per_minute": 80, "batch_size": 25, "batch_delay": 15, "test_numbers": [1, 50, 100, 200, 275, 300, 500, 775, 800, 999], "max_tests_per_prefix": 4, "early_stop": true}, "detailed_results": {"GDRD": {"studio": "GDRD", "total_prefixes": 1, "valid_prefixes": [], "invalid_prefixes": [], "prefix_details": {}, "success_rate": 0.0, "verification_time": 0, "status": "error", "total_requests": 0, "verification_method": "intelligent_range_probing", "error": "'probe_ranges'"}, "NSFS": {"studio": "NSFS", "total_prefixes": 1, "valid_prefixes": [], "invalid_prefixes": [], "prefix_details": {}, "success_rate": 0.0, "verification_time": 0, "status": "error", "total_requests": 0, "verification_method": "intelligent_range_probing", "error": "'probe_ranges'"}, "GMA": {"studio": "GMA", "total_prefixes": 1, "valid_prefixes": [], "invalid_prefixes": [], "prefix_details": {}, "success_rate": 0.0, "verification_time": 0, "status": "error", "total_requests": 0, "verification_method": "intelligent_range_probing", "error": "'probe_ranges'"}, "GAJK": {"studio": "GAJK", "total_prefixes": 1, "valid_prefixes": [], "invalid_prefixes": [], "prefix_details": {}, "success_rate": 0.0, "verification_time": 0, "status": "error", "total_requests": 0, "verification_method": "intelligent_range_probing", "error": "'probe_ranges'"}, "NGHJ": {"studio": "NGHJ", "total_prefixes": 1, "valid_prefixes": [], "invalid_prefixes": [], "prefix_details": {}, "success_rate": 0.0, "verification_time": 0, "status": "error", "total_requests": 0, "verification_method": "intelligent_range_probing", "error": "'probe_ranges'"}, "DNJR": {"studio": "DNJR", "total_prefixes": 1, "valid_prefixes": [], "invalid_prefixes": [], "prefix_details": {}, "success_rate": 0.0, "verification_time": 0, "status": "error", "total_requests": 0, "verification_method": "intelligent_range_probing", "error": "'probe_ranges'"}, "HOWS": {"studio": "HOWS", "total_prefixes": 1, "valid_prefixes": [], "invalid_prefixes": [], "prefix_details": {}, "success_rate": 0.0, "verification_time": 0, "status": "error", "total_requests": 0, "verification_method": "intelligent_range_probing", "error": "'probe_ranges'"}, "CHRV": {"studio": "CHRV", "total_prefixes": 1, "valid_prefixes": [], "invalid_prefixes": [], "prefix_details": {}, "success_rate": 0.0, "verification_time": 0, "status": "error", "total_requests": 0, "verification_method": "intelligent_range_probing", "error": "'probe_ranges'"}, "MOND": {"studio": "MOND", "total_prefixes": 2, "valid_prefixes": [], "invalid_prefixes": [], "prefix_details": {}, "success_rate": 0.0, "verification_time": 0, "status": "error", "total_requests": 0, "verification_method": "intelligent_range_probing", "error": "'probe_ranges'"}, "ALDN": {"studio": "ALDN", "total_prefixes": 1, "valid_prefixes": [], "invalid_prefixes": [], "prefix_details": {}, "success_rate": 0.0, "verification_time": 0, "status": "error", "total_requests": 0, "verification_method": "intelligent_range_probing", "error": "'probe_ranges'"}}}