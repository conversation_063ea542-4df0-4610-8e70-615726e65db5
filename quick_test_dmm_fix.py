#!/usr/bin/env python3
"""
快速测试DMM搜索爬虫修正效果
"""
import sys
sys.path.append('.')

def main():
    print("🔍 快速测试DMM搜索爬虫修正效果")
    print("=" * 50)
    
    try:
        from modules.dmm_search_crawler import DMMSearchCrawler
        
        # 你提供的真实DMM搜索结果
        real_dmm_results = [
            "5533id00021",
            "5532id00021", 
            "5531id00021",
            "5530id00021",
            "5529id00021",
            "h_113id00021",
            "5526id00021",
            "5524id00021",
            "5525id00021",
            "5522id00021",
            "5521id00021",
            "5519id00021"
        ]
        
        crawler = DMMSearchCrawler()
        studio = "ID"
        number = 21
        
        print(f"📋 测试真实DMM搜索结果 (共{len(real_dmm_results)}个):")
        
        valid_count = 0
        invalid_cids = []
        
        for i, cid in enumerate(real_dmm_results, 1):
            print(f"   {i:2d}. 测试CID: {cid}")
            
            try:
                is_relevant = crawler._is_highly_relevant_cid(cid, studio, number)
                
                if is_relevant:
                    valid_count += 1
                    prefix = crawler._extract_prefix_from_cid(cid, studio, number)
                    confidence = crawler._calculate_confidence(cid, studio, number)
                    print(f"       ✅ 通过 -> 前缀: {prefix}, 置信度: {confidence:.2f}")
                else:
                    invalid_cids.append(cid)
                    print(f"       ❌ 被过滤")
                    
            except Exception as e:
                print(f"       ❌ 异常: {e}")
                invalid_cids.append(cid)
        
        print(f"\n📊 筛选结果:")
        print(f"   有效CID: {valid_count}/{len(real_dmm_results)} ({valid_count/len(real_dmm_results)*100:.1f}%)")
        print(f"   被过滤: {len(invalid_cids)} 个")
        
        if invalid_cids:
            print(f"   被过滤的CID: {invalid_cids}")
        
        # 期望结果：所有真实的DMM结果都应该通过筛选
        if valid_count == len(real_dmm_results):
            print(f"\n🎉 完美！所有真实DMM结果都通过了筛选")
            print(f"✅ 修正成功：严格按照番号正则筛选")
            return True
        else:
            print(f"\n⚠️ 有 {len(invalid_cids)} 个真实结果被错误过滤")
            print(f"❌ 需要进一步调整筛选逻辑")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🔄 下一步操作:")
        print("1. 可以安全运行真实DMM爬虫测试")
        print("2. 验证其他厂商的搜索结果")
        print("3. 检查映射数据库更新效果")
    else:
        print("\n🛠️ 需要进一步调整:")
        print("1. 检查被过滤的CID模式")
        print("2. 调整相关性检查逻辑")
        print("3. 确保所有真实结果都能通过")
