#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DMM搜索系统演示
"""

import json
import re
from typing import List, Dict, Optional, Tuple


class SimpleDMMSearch:
    """简化版DMM搜索演示"""
    
    def __init__(self):
        # 简化的厂商映射
        self.studio_mappings = {
            'MILK': 'h_1240milk',
            'SSIS': 'h_1116ssis', 
            'STARS': 'h_1116stars',
            'PRED': 'h_1116pred',
            'IPX': 'h_1116ipx',
            'MIDE': 'h_1116mide',
            'JUFE': 'h_1160jufe',
            'EBOD': 'h_1160ebod',
            'CAWD': 'h_1160cawd',
            'DASD': 'h_1160dasd'
        }
    
    def parse_code(self, code: str) -> Optional[Tuple[str, int]]:
        """解析番号"""
        code = code.strip().upper().replace(' ', '')
        
        patterns = [
            r'^([A-Z]+)-(\d+)$',  # MILK-251
            r'^([A-Z]+)(\d+)$',   # MILK251
        ]
        
        for pattern in patterns:
            match = re.match(pattern, code)
            if match:
                studio = match.group(1)
                number = int(match.group(2))
                return studio, number
        
        return None
    
    def generate_dmm_cid(self, studio: str, number: int) -> Optional[str]:
        """生成DMM CID"""
        if studio in self.studio_mappings:
            dmm_prefix = self.studio_mappings[studio]
            padded_number = str(number).zfill(5)
            return f"{dmm_prefix}{padded_number}"
        return None
    
    def generate_variants(self, studio: str, number: int) -> List[str]:
        """生成番号变体"""
        variants = []
        
        # 基础格式
        variants.append(f"{studio}-{number}")
        variants.append(f"{studio}{number}")
        
        # 补零格式
        for width in [3, 4, 5]:
            padded = str(number).zfill(width)
            variants.append(f"{studio}-{padded}")
            variants.append(f"{studio}{padded}")
            variants.append(f"{studio}0{padded}")
            variants.append(f"{studio}00{padded}")
        
        return list(set(variants))
    
    def search(self, code: str) -> Dict:
        """搜索番号"""
        result = {
            'input': code,
            'parsed': None,
            'dmm_cid': None,
            'url': None,
            'variants': [],
            'success': False
        }
        
        # 解析番号
        parsed = self.parse_code(code)
        if not parsed:
            return result
        
        studio, number = parsed
        result['parsed'] = {'studio': studio, 'number': number}
        
        # 生成DMM CID
        dmm_cid = self.generate_dmm_cid(studio, number)
        if dmm_cid:
            result['dmm_cid'] = dmm_cid
            result['url'] = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={dmm_cid}/"
            result['success'] = True
        
        # 生成变体
        result['variants'] = self.generate_variants(studio, number)
        
        return result


def demo_search():
    """演示搜索功能"""
    search_engine = SimpleDMMSearch()
    
    print("=== DMM番号搜索系统演示 ===\n")
    
    # 测试用例
    test_cases = [
        "MILK-251",    # 你提到的例子
        "MILK251",     # 无分隔符
        "MILK00251",   # 补零格式
        "SSIS-001",    # 其他厂商
        "UNKNOWN-123"  # 不支持的厂商
    ]
    
    for code in test_cases:
        print(f"搜索: {code}")
        print("-" * 40)
        
        result = search_engine.search(code)
        
        if result['parsed']:
            studio = result['parsed']['studio']
            number = result['parsed']['number']
            print(f"解析结果: 厂商={studio}, 数字={number}")
        else:
            print("解析失败: 番号格式不正确")
            print()
            continue
        
        if result['success']:
            print(f"✓ DMM CID: {result['dmm_cid']}")
            print(f"✓ 详情页: {result['url']}")
        else:
            print(f"✗ 厂商 '{result['parsed']['studio']}' 不在支持列表中")
        
        print(f"生成的变体数: {len(result['variants'])}")
        print("变体示例:", ", ".join(result['variants'][:5]))
        if len(result['variants']) > 5:
            print(f"... 还有 {len(result['variants']) - 5} 个变体")
        
        print()


def explain_dmm_mechanism():
    """解释DMM搜索机制"""
    print("=== DMM网站快速搜索的原理分析 ===\n")
    
    print("1. 预建数据库索引:")
    print("   - DMM网站维护了一个包含所有番号变体的数据库")
    print("   - 每个视频的多种格式都被预先索引")
    print("   - 例如: MILK-251, MILK251, MILK00251 都指向同一个视频\n")
    
    print("2. 厂商代码映射:")
    print("   - 每个厂商都有固定的DMM内部ID前缀")
    print("   - MILK → h_1240milk")
    print("   - SSIS → h_1116ssis")
    print("   - 数字部分统一补零到5位\n")
    
    print("3. URL生成规则:")
    print("   - 格式: https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={dmm_cid}/")
    print("   - MILK-251 → h_1240milk00251")
    print("   - 最终URL: https://www.dmm.co.jp/digital/videoa/-/detail/=/cid=h_1240milk00251/\n")
    
    print("4. 为什么DMM搜索很快:")
    print("   ✓ 预建索引，无需实时计算")
    print("   ✓ 数据库优化，支持快速查找")
    print("   ✓ 多重索引，覆盖所有变体")
    print("   ✓ 内存缓存，减少磁盘IO\n")
    
    print("5. 我们的解决方案:")
    print("   ✓ 模拟DMM的索引机制")
    print("   ✓ 本地SQLite数据库缓存")
    print("   ✓ 智能变体生成算法")
    print("   ✓ 批量预建索引功能")


def show_supported_studios():
    """显示支持的厂商列表"""
    search_engine = SimpleDMMSearch()
    
    print("=== 支持的厂商列表 ===\n")
    
    studios = list(search_engine.studio_mappings.keys())
    studios.sort()
    
    for i, studio in enumerate(studios, 1):
        dmm_prefix = search_engine.studio_mappings[studio]
        print(f"{i:2d}. {studio:<8} → {dmm_prefix}")
    
    print(f"\n总计支持 {len(studios)} 个厂商")
    print("\n注意: 这只是演示版本，完整版本支持50+厂商")


def main():
    """主函数"""
    print("DMM番号搜索系统 - 演示版本\n")
    
    while True:
        print("请选择功能:")
        print("1. 搜索演示")
        print("2. 原理解释") 
        print("3. 支持厂商")
        print("4. 退出")
        
        try:
            choice = input("\n请输入选项 (1-4): ").strip()
            print()
            
            if choice == '1':
                demo_search()
            elif choice == '2':
                explain_dmm_mechanism()
            elif choice == '3':
                show_supported_studios()
            elif choice == '4':
                print("再见!")
                break
            else:
                print("无效选项，请重新选择")
            
            input("\n按回车键继续...")
            print("\n" + "="*60 + "\n")
            
        except KeyboardInterrupt:
            print("\n\n再见!")
            break
        except Exception as e:
            print(f"错误: {e}")


if __name__ == "__main__":
    main()
