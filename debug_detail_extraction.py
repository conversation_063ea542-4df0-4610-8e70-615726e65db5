#!/usr/bin/env python3
"""
调试详细信息提取 - 专门用于分析DMM页面结构
"""
import requests
import re
from bs4 import BeautifulSoup

def debug_single_page(cid: str = "cawd00797"):
    """调试单个页面的详细信息提取"""
    
    detail_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/"
    
    print(f"🔍 调试页面: {detail_url}")
    print("=" * 80)
    
    # 设置请求
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    })
    
    # 处理年龄验证
    try:
        age_check_url = "https://www.dmm.co.jp/age_check/=/declared=yes/?rurl=https%3A%2F%2Fwww.dmm.co.jp%2F"
        session.get(age_check_url, timeout=10)
    except:
        pass
    
    # 获取页面
    try:
        response = session.get(detail_url, timeout=30)
        
        if response.status_code == 200:
            print(f"✅ 页面访问成功 ({len(response.text)} 字符)")
            
            # 解析HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 保存完整HTML用于分析
            with open(f'debug_full_page_{cid}.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"📄 完整页面已保存到: debug_full_page_{cid}.html")
            
            # 分析页面结构
            print(f"\n📊 页面结构分析:")
            print("-" * 60)
            
            # 1. 查找所有表格
            tables = soup.find_all('table')
            print(f"🔍 找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables):
                print(f"\n表格 {i+1}:")
                rows = table.find_all('tr')
                print(f"  行数: {len(rows)}")
                
                for j, row in enumerate(rows[:5]):  # 只显示前5行
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        header = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        print(f"    行{j+1}: {header[:20]} | {value[:50]}")
            
            # 2. 查找定义列表
            dl_elements = soup.find_all('dl')
            print(f"\n🔍 找到 {len(dl_elements)} 个定义列表")
            
            for i, dl in enumerate(dl_elements):
                print(f"\n定义列表 {i+1}:")
                dts = dl.find_all('dt')
                for j, dt in enumerate(dts[:3]):  # 只显示前3个
                    dt_text = dt.get_text(strip=True)
                    dd = dt.find_next_sibling('dd')
                    dd_text = dd.get_text(strip=True) if dd else "无"
                    print(f"    项{j+1}: {dt_text[:20]} | {dd_text[:50]}")
            
            # 3. 搜索特定字段
            print(f"\n🔍 搜索特定字段:")
            print("-" * 60)
            
            fields_to_search = [
                '配信開始日', '商品発売日', '収録時間', '出演者', 
                '監督', 'シリーズ', 'メーカー', 'レーベル', 'ジャンル', '平均評価'
            ]
            
            for field in fields_to_search:
                print(f"\n🔍 搜索字段: {field}")
                
                # 在整个页面中搜索
                elements = soup.find_all(string=lambda text: text and field in text)
                print(f"  找到 {len(elements)} 个匹配元素")
                
                for k, element in enumerate(elements[:3]):  # 只显示前3个
                    parent = element.parent
                    if parent:
                        parent_text = parent.get_text(strip=True)
                        print(f"    匹配{k+1}: {parent_text[:100]}")
                        
                        # 检查是否在表格中
                        if parent.name in ['th', 'td']:
                            next_cell = parent.find_next_sibling(['td', 'th'])
                            if next_cell:
                                value = next_cell.get_text(strip=True)
                                print(f"      → 表格值: {value[:50]}")
                        
                        # 检查是否在定义列表中
                        if parent.name == 'dt':
                            dd = parent.find_next_sibling('dd')
                            if dd:
                                value = dd.get_text(strip=True)
                                print(f"      → 定义值: {value[:50]}")
                        
                        # 检查冒号分隔
                        if ':' in parent_text or '：' in parent_text:
                            parts = re.split(r'[:]|[：]', parent_text, 1)
                            if len(parts) > 1 and field in parts[0]:
                                value = parts[1].strip()
                                print(f"      → 冒号值: {value[:50]}")
            
            # 4. 查找评分
            print(f"\n🔍 搜索评分信息:")
            print("-" * 60)
            
            rating_patterns = [
                r'平均評価.*?(\d+\.?\d*)',
                r'評価.*?(\d+\.?\d*)',
                r'(\d+\.?\d*)点',
                r'★.*?(\d+\.?\d*)'
            ]
            
            page_text = soup.get_text()
            for pattern in rating_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    print(f"  模式 '{pattern}' 找到: {matches[:5]}")
            
        else:
            print(f"❌ 页面访问失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 调试异常: {e}")

def main():
    """主函数"""
    print("🔍 DMM详细信息提取调试工具")
    print("=" * 80)
    
    cid = input("请输入要调试的CID (默认: cawd00797): ").strip()
    if not cid:
        cid = "cawd00797"
    
    debug_single_page(cid)
    
    print(f"\n✅ 调试完成!")
    print(f"📄 请查看生成的HTML文件进行进一步分析")

if __name__ == "__main__":
    main()
