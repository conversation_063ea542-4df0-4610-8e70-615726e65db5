# 🎓 自动映射学习系统指南

## 📋 概述

自动映射学习系统是一个智能化的映射管理解决方案，当遇到智能搜索失败但备用搜索成功的情况时，系统会自动学习并更新映射数据库，确保下次搜索能够直接成功。

## 🎯 解决的问题

### 问题场景
```
用户搜索: NEO-834
智能搜索: 失败（映射不存在）
备用搜索: 成功找到 433neo00834
结果: 用户得到结果，但下次搜索同类番号仍会失败
```

### 解决方案
```
用户搜索: NEO-834
智能搜索: 失败（映射不存在）
备用搜索: 成功找到 433neo00834
自动学习: NEO -> 433neo 映射已学习
下次搜索: NEO-835 直接成功！
```

## 🏗️ 系统架构

### 核心组件

1. **AutoMappingLearner** (`modules/auto_mapping_learner.py`)
   - 映射模式分析
   - 学习决策逻辑
   - 配置文件更新
   - 数据库同步

2. **SearchDetailModule** (`modules/search_detail.py`)
   - 集成自动学习器
   - 触发学习时机
   - 缓存刷新机制

3. **配置文件** (`studio_mappings_all.json`)
   - 映射数据存储
   - 自动备份机制
   - 版本控制

4. **学习日志** (`mapping_learning_log.json`)
   - 学习历史记录
   - 统计信息
   - 调试信息

## 🔄 工作流程

### 1. 触发条件
```python
# 当满足以下条件时触发自动学习：
if (智能搜索失败 and 备用搜索成功 and 自动学习启用):
    执行自动学习()
```

### 2. 学习过程

#### 步骤1: 番号解析
```python
输入: "NEO-834"
解析: studio="NEO", number="834"
```

#### 步骤2: CID模式分析
```python
输入CID: "433neo00834"
分析结果:
- 模式类型: "数字前缀"
- 提取前缀: "433neo"
- 置信度: 0.8
```

#### 步骤3: 学习决策
```python
检查现有映射:
- NEO不存在 -> 学习新映射
- NEO已存在但不同 -> 添加备选映射
- NEO已存在且相同 -> 跳过学习
```

#### 步骤4: 执行学习
```python
更新配置文件:
{
  "NEO": {
    "primary": "433neo",
    "alternatives": [],
    "count": 1,
    "type": "single"
  }
}
```

#### 步骤5: 同步更新
```python
1. 更新JSON配置文件
2. 同步到SQLite数据库
3. 刷新内存缓存
4. 记录学习日志
```

## 🎨 支持的映射模式

### 1. 数字前缀模式
```
示例: 433neo00834
模式: (\d+)([a-z]+)\d+
提取: 433neo
```

### 2. h_前缀模式
```
示例: h_1240milk00251
模式: (h_\d+)([a-z]+)\d+
提取: h_1240milk
```

### 3. 简单前缀模式
```
示例: ssis00001
模式: ([a-z]+)\d+
提取: ssis
```

### 4. 复杂前缀模式
```
示例: mbddneo00834
模式: ([a-z]+)([a-z]+)\d+
提取: mbddneo (智能判断)
```

## 📊 学习策略

### 置信度计算
```python
基础置信度 = 0.5

if 前缀包含厂商名:
    置信度 += 0.3

模式置信度:
- 数字前缀: 0.8
- h_前缀: 0.9
- 简单前缀: 0.7
- 复杂前缀: 0.6

最终置信度 = max(基础置信度, 模式置信度)
```

### 学习决策
```python
if 厂商不存在:
    action = "new_studio"  # 添加新厂商
elif 映射不同:
    action = "add_alternative"  # 添加备选映射
else:
    action = "skip"  # 跳过学习
```

## 🛡️ 安全机制

### 1. 备份机制
```python
# 每次更新前自动创建备份
backup_file = f"studio_mappings_all.json.backup.{timestamp}"
```

### 2. 异常处理
```python
try:
    执行学习()
except Exception as e:
    记录错误日志()
    不影响主搜索流程()
```

### 3. 验证机制
```python
# 学习后验证映射是否正确添加
验证配置文件()
验证数据库同步()
验证缓存刷新()
```

## 📈 性能优化

### 1. 缓存策略
```python
# 学习器实例缓存到session_state
if "auto_mapping_learner" not in st.session_state:
    st.session_state.auto_mapping_learner = AutoMappingLearner()
```

### 2. 批量更新
```python
# 一次性更新所有相关数据结构
更新mappings()
更新single_mappings()
更新multi_mappings()
同步数据库()
```

### 3. 延迟加载
```python
# 只在需要时初始化学习器
if enable_auto_learning and AUTO_LEARNING_AVAILABLE:
    初始化学习器()
```

## 🔧 配置选项

### 启用/禁用自动学习
```python
# 在SearchDetailModule初始化时
search_module = SearchDetailModule(
    enable_fast_search=True,
    enable_auto_learning=True  # 控制自动学习
)
```

### 学习器配置
```python
learner = AutoMappingLearner(
    config_file="studio_mappings_all.json",  # 映射配置文件
    db_file="mmp/fast_dmm.db",              # 数据库文件
    learning_log_file="mapping_learning_log.json"  # 学习日志
)
```

## 📊 监控和调试

### 学习统计
```python
stats = learner.get_learning_statistics()
print(f"总学习次数: {stats['stats']['total_learned']}")
print(f"新厂商: {stats['stats']['new_studios']}")
print(f"更新映射: {stats['stats']['updated_mappings']}")
```

### 学习日志
```json
{
  "stats": {
    "total_learned": 5,
    "new_studios": 3,
    "updated_mappings": 2,
    "last_update": "2025-07-25T10:30:00"
  },
  "last_learned": {
    "studio": "NEO",
    "prefix": "433neo",
    "example_code": "NEO-834",
    "example_cid": "433neo00834",
    "source": "dmm_tools_fallback",
    "action": "new_studio"
  }
}
```

## 🚀 使用示例

### 基本使用
```python
from modules.search_detail import SearchDetailModule

# 创建搜索模块（自动学习已启用）
search_module = SearchDetailModule()

# 搜索番号（如果智能搜索失败但备用搜索成功，会自动学习）
result = search_module.search_dmm_enhanced("NEO-834")

if result["success"]:
    print(f"搜索成功: {result['cid']}")
    if "learning_message" in result:
        print(f"学习结果: {result['learning_message']}")
```

### 手动学习
```python
from modules.auto_mapping_learner import AutoMappingLearner

learner = AutoMappingLearner()
result = learner.learn_from_successful_search(
    code="NEO-834",
    found_cid="433neo00834",
    search_source="manual"
)

print(f"学习结果: {result['message']}")
```

## 🎯 预期效果

### 学习前
```
NEO-834 搜索: 智能搜索失败 -> 备用搜索成功 (慢)
NEO-835 搜索: 智能搜索失败 -> 备用搜索成功 (慢)
NEO-836 搜索: 智能搜索失败 -> 备用搜索成功 (慢)
```

### 学习后
```
NEO-834 搜索: 智能搜索失败 -> 备用搜索成功 -> 自动学习
NEO-835 搜索: 智能搜索成功 (快)
NEO-836 搜索: 智能搜索成功 (快)
```

## 🔄 测试和验证

### 运行测试
```bash
python test_auto_learning.py
```

### 验证学习效果
1. 搜索一个新厂商的番号
2. 观察是否触发自动学习
3. 搜索同厂商的其他番号
4. 验证是否直接成功

---

**🎉 自动映射学习系统让你的搜索体验越来越好！**
