2025-07-25 12:28:01,444 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/df206024c37ba2cb136bcbb3bfd7651f
2025-07-25 12:28:01,449 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f0f5d589670>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/df206024c37ba2cb136bcbb3bfd7651f
2025-07-25 12:28:01,451 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f0f5d589700>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/df206024c37ba2cb136bcbb3bfd7651f
2025-07-25 12:31:25,608 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fd5a08afb60>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/065863ca1fa1b162a76b3aad1aac430c
2025-07-25 12:31:25,609 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fd5a08aff80>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/065863ca1fa1b162a76b3aad1aac430c
2025-07-25 12:31:25,616 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fd5a08afa40>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/065863ca1fa1b162a76b3aad1aac430c
2025-07-25 12:54:06,796 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/54e2999ee59d7b2484d375f8218268ba
2025-07-25 12:54:06,799 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7ffba9740cb0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/54e2999ee59d7b2484d375f8218268ba
2025-07-25 12:54:06,801 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7ffba9681a60>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/54e2999ee59d7b2484d375f8218268ba
2025-07-25 12:56:14,463 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/07dec5677e0183eee786f26298b35d87
2025-07-25 12:56:14,465 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f77acf498b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/07dec5677e0183eee786f26298b35d87
2025-07-25 12:56:14,467 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f77ace7dfd0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/07dec5677e0183eee786f26298b35d87
2025-07-25 13:01:51,928 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/c1b6d00cb907c38917d2bacdcaf649dc
2025-07-25 13:01:51,930 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f5bb13da030>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/c1b6d00cb907c38917d2bacdcaf649dc
2025-07-25 13:01:51,934 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f5baff99190>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/c1b6d00cb907c38917d2bacdcaf649dc
2025-07-25 13:10:06,366 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/3db48ef89eeb980ab683c738fd4bf5bf
2025-07-25 13:10:06,368 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f0aeb34d1c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/3db48ef89eeb980ab683c738fd4bf5bf
2025-07-25 13:10:06,371 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f0aeb34d340>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/3db48ef89eeb980ab683c738fd4bf5bf
2025-07-25 13:17:09,952 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/8a8d50715163bfb303bcf519592d5c25
2025-07-25 13:17:09,955 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbdabbc0500>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/8a8d50715163bfb303bcf519592d5c25
2025-07-25 13:17:09,957 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbdabbc0680>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/8a8d50715163bfb303bcf519592d5c25
2025-07-25 13:24:48,382 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f2ef7ef80e0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/07d0bcf2fc661ba540f49fa8a6854020
2025-07-25 13:24:48,385 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f2ef82781a0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/07d0bcf2fc661ba540f49fa8a6854020
2025-07-25 13:24:48,388 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f2ef7ef8e90>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/07d0bcf2fc661ba540f49fa8a6854020
2025-07-25 13:35:51,755 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/3d4475b24400007f4c70a676e8a7fd52
2025-07-25 13:35:51,758 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7ff139f73800>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/3d4475b24400007f4c70a676e8a7fd52
2025-07-25 13:35:51,761 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7ff139648ad0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/3d4475b24400007f4c70a676e8a7fd52
2025-07-25 13:49:14,576 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/4581b025a3b8b36653c4a150a6acac51
2025-07-25 13:49:14,579 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fb8556f8ef0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/4581b025a3b8b36653c4a150a6acac51
2025-07-25 13:49:14,581 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fb8556f9190>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/4581b025a3b8b36653c4a150a6acac51
2025-07-25 13:53:30,180 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/3efe28ca138dcee5d2a70875eeb6f501
2025-07-25 13:53:30,184 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fec3ebf3dd0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/3efe28ca138dcee5d2a70875eeb6f501
2025-07-25 13:53:30,186 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fec3ebf3f50>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/3efe28ca138dcee5d2a70875eeb6f501
2025-07-25 14:05:41,939 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/1102224647afcefb07bf2ce8f44a3bb2
2025-07-25 14:05:41,941 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa4ea55faa0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/1102224647afcefb07bf2ce8f44a3bb2
2025-07-25 14:05:41,943 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa4ea40ca40>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/1102224647afcefb07bf2ce8f44a3bb2
2025-07-25 14:11:22,340 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/29408db443d99d6445aaf753a47e5fba
2025-07-25 14:11:22,343 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f9e4a307770>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/29408db443d99d6445aaf753a47e5fba
2025-07-25 14:11:22,345 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f9e4a148470>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/29408db443d99d6445aaf753a47e5fba
2025-07-25 14:15:07,456 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/816dc547301007fa715e33adf669fbf5
2025-07-25 14:15:07,459 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fef8ed325a0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/816dc547301007fa715e33adf669fbf5
2025-07-25 14:15:07,461 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fef8ec33ef0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/816dc547301007fa715e33adf669fbf5
2025-07-25 14:15:49,872 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/330cd29ac61dff5a4f7b7641c18bef1b
2025-07-25 14:15:49,875 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f28c2b52030>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/330cd29ac61dff5a4f7b7641c18bef1b
2025-07-25 14:15:49,878 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f28c211cce0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/330cd29ac61dff5a4f7b7641c18bef1b
2025-07-25 14:18:48,255 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': /session/5444a841d57d9e88642972edfd81a707
2025-07-25 14:18:48,257 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f52f3bd8cb0>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/5444a841d57d9e88642972edfd81a707
2025-07-25 14:18:48,259 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f52f3bd8e30>: Failed to establish a new connection: [Errno 111] Connection refused')': /session/5444a841d57d9e88642972edfd81a707
