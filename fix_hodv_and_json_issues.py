#!/usr/bin/env python3
"""
修复HODV映射和JSON保存问题
"""
import json
import os
from datetime import datetime

def fix_json_files():
    """修复JSON文件格式问题"""
    print("🔧 修复JSON文件格式问题...")
    
    # 需要检查的JSON文件
    json_files = [
        "h_prefix_numbers.json",
        "number_prefixes.json"
    ]
    
    for file_path in json_files:
        print(f"\n📁 检查文件: {file_path}")
        
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"   当前数据类型: {type(data)}")
                print(f"   当前数据: {data}")
                
                # 如果是列表，转换为字典
                if isinstance(data, list):
                    print("   ⚠️ 发现列表格式，转换为字典格式")
                    
                    if file_path == "number_prefixes.json":
                        # number_prefixes.json 应该保持列表格式
                        print("   ✅ number_prefixes.json 保持列表格式")
                    else:
                        # h_prefix_numbers.json 应该是字典格式
                        new_data = {}
                        # 创建备份
                        backup_file = f"{file_path}.backup.{int(datetime.now().timestamp())}"
                        with open(backup_file, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        print(f"   📦 已创建备份: {backup_file}")
                        
                        # 保存新格式
                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump(new_data, f, ensure_ascii=False, indent=2)
                        print(f"   ✅ 已转换为字典格式")
                
                elif isinstance(data, dict):
                    print("   ✅ 格式正确（字典）")
                else:
                    print(f"   ⚠️ 未知格式: {type(data)}")
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON格式错误: {e}")
                # 创建新的空文件
                if file_path == "number_prefixes.json":
                    new_data = []
                else:
                    new_data = {}
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(new_data, f, ensure_ascii=False, indent=2)
                print(f"   ✅ 已重新创建文件")
                
            except Exception as e:
                print(f"   ❌ 处理失败: {e}")
        else:
            print(f"   📝 文件不存在，创建新文件")
            if file_path == "number_prefixes.json":
                new_data = []
            else:
                new_data = {}
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(new_data, f, ensure_ascii=False, indent=2)
            print(f"   ✅ 已创建新文件")

def fix_hodv_mapping():
    """修复HODV映射"""
    print("\n🔧 修复HODV映射...")
    
    try:
        # 加载映射配置
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        
        # 检查当前HODV映射
        if 'HODV' in mappings:
            current_mapping = mappings['HODV']
            print(f"   当前HODV映射: {current_mapping}")
            
            # 检查是否包含正确的映射
            correct_prefix = "5642hodv"
            current_primary = current_mapping.get('primary', '')
            current_alternatives = current_mapping.get('alternatives', [])
            
            if correct_prefix == current_primary:
                print(f"   ✅ 主映射已正确: {correct_prefix}")
            elif correct_prefix in current_alternatives:
                print(f"   ✅ 正确映射在备选项中: {correct_prefix}")
            else:
                print(f"   ⚠️ 需要添加正确映射: {correct_prefix}")
                
                # 添加到备选映射
                if correct_prefix not in current_alternatives:
                    current_alternatives.append(correct_prefix)
                    
                    # 更新映射
                    mappings['HODV'] = {
                        "primary": current_primary,
                        "alternatives": current_alternatives,
                        "count": len(current_alternatives) + 1,
                        "type": "multiple"
                    }
                    
                    # 更新分类映射
                    single_mappings = data.get('single_mappings', {})
                    multi_mappings = data.get('multi_mappings', {})
                    
                    if 'HODV' in single_mappings:
                        del single_mappings['HODV']
                    
                    multi_mappings['HODV'] = mappings['HODV']
                    
                    # 保存更新
                    data['mappings'] = mappings
                    data['single_mappings'] = single_mappings
                    data['multi_mappings'] = multi_mappings
                    
                    # 创建备份
                    backup_file = f"studio_mappings_all.json.backup.hodv_fix.{int(datetime.now().timestamp())}"
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    print(f"   📦 已创建备份: {backup_file}")
                    
                    # 保存主文件
                    with open('studio_mappings_all.json', 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    print(f"   ✅ HODV映射已更新:")
                    print(f"      主映射: {mappings['HODV']['primary']}")
                    print(f"      备选映射: {mappings['HODV']['alternatives']}")
                    print(f"      类型: {mappings['HODV']['type']}")
        else:
            print("   ❌ HODV映射不存在，创建新映射")
            
            # 创建新的HODV映射
            mappings['HODV'] = {
                "primary": "5642hodv",
                "alternatives": ["41hodv"],  # 保留原有的作为备选
                "count": 2,
                "type": "multiple"
            }
            
            # 更新分类映射
            multi_mappings = data.get('multi_mappings', {})
            multi_mappings['HODV'] = mappings['HODV']
            
            # 保存更新
            data['mappings'] = mappings
            data['multi_mappings'] = multi_mappings
            
            with open('studio_mappings_all.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 已创建HODV映射: {mappings['HODV']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 修复HODV映射失败: {e}")
        return False

def fix_multiple_result_json_save():
    """修复多重结果JSON保存问题"""
    print("\n🔧 修复多重结果JSON保存问题...")
    
    print("   问题分析:")
    print("   - 多重结果格式与单一结果不同")
    print("   - 需要在确认选择后提取正确的CID")
    print("   - JSON保存逻辑需要适配多重结果格式")
    
    print("   建议修复:")
    print("   1. 在UI确认选择后，转换为标准单一结果格式")
    print("   2. 确保结果包含必要的 'code' 和 'cid' 字段")
    print("   3. 优化JSON保存逻辑的错误处理")
    
    return True

def test_fixes():
    """测试修复效果"""
    print("\n🧪 测试修复效果...")
    
    # 测试JSON文件格式
    test_files = ["h_prefix_numbers.json", "number_prefixes.json"]
    
    for file_path in test_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            expected_type = list if file_path == "number_prefixes.json" else dict
            if isinstance(data, expected_type):
                print(f"   ✅ {file_path}: 格式正确 ({type(data).__name__})")
            else:
                print(f"   ❌ {file_path}: 格式错误 ({type(data).__name__})")
                
        except Exception as e:
            print(f"   ❌ {file_path}: 测试失败 - {e}")
    
    # 测试HODV映射
    try:
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        hodv_mapping = data.get('mappings', {}).get('HODV', {})
        if hodv_mapping:
            alternatives = hodv_mapping.get('alternatives', [])
            if "5642hodv" in [hodv_mapping.get('primary')] + alternatives:
                print(f"   ✅ HODV映射: 包含正确前缀 5642hodv")
            else:
                print(f"   ❌ HODV映射: 缺少正确前缀 5642hodv")
        else:
            print(f"   ❌ HODV映射: 不存在")
            
    except Exception as e:
        print(f"   ❌ HODV映射测试失败: {e}")

def main():
    """主函数"""
    print("🛠️ 修复HODV映射和JSON保存问题")
    print("=" * 60)
    
    # 修复JSON文件格式
    fix_json_files()
    
    # 修复HODV映射
    hodv_success = fix_hodv_mapping()
    
    # 修复多重结果JSON保存
    json_save_success = fix_multiple_result_json_save()
    
    # 测试修复效果
    test_fixes()
    
    print("\n" + "=" * 60)
    print("📋 修复总结:")
    
    if hodv_success:
        print("✅ HODV映射修复: 成功")
    else:
        print("❌ HODV映射修复: 失败")
    
    if json_save_success:
        print("✅ JSON保存修复: 成功")
    else:
        print("❌ JSON保存修复: 失败")
    
    print("\n🔄 下一步操作:")
    print("1. 重启Streamlit应用")
    print("2. 测试HODV-21987搜索")
    print("3. 验证是否显示正确的CID: 5642hodv21987")
    print("4. 测试多重结果的确认选择功能")
    print("5. 验证JSON保存是否正常")
    
    print("\n📋 预期效果:")
    print("   ✅ HODV-21987 应该显示 5642hodv21987 选项")
    print("   ✅ JSON保存不再报错")
    print("   ✅ 多重结果确认选择正常工作")

if __name__ == "__main__":
    main()
