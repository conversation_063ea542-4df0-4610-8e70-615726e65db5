# 🕷️ DMM爬虫测试使用指南

## 📋 概述

本指南提供了两个DMM爬虫测试脚本，用于安全地测试DMM官网数据收集功能：

1. **`quick_dmm_test.py`** - 快速测试脚本（推荐首次使用）
2. **`dmm_crawler_test.py`** - 完整功能测试脚本

## 🚀 快速开始

### 1. 快速测试（推荐）

```bash
python quick_dmm_test.py
```

**功能**：
- ✅ 测试ID-021的爬取（基于你提供的真实DMM搜索结果）
- ✅ 验证爬取质量和匹配率
- ✅ 快速验证核心功能是否正常

**预期结果**：
```
🚀 快速DMM爬虫测试
📝 测试 ID-021 (基于你提供的真实DMM搜索结果)
📊 爬取结果:
   耗时: 5.23 秒
   发现映射: 12 个
📋 发现的CID:
    1. 5533id00021 -> 5533id (置信度: 0.85)
    2. 5532id00021 -> 5532id (置信度: 0.85)
    3. 5531id00021 -> 5531id (置信度: 0.85)
    ... (共12个)
🎯 验证结果:
   预期CID: 12 个
   匹配CID: 12 个
   匹配率: 100.0%
   ✅ 爬取质量良好
```

### 2. 完整功能测试

```bash
python dmm_crawler_test.py
```

**功能菜单**：
```
📋 可用测试:
   1. URL构建验证
   2. 单个番号搜索
   3. 批量爬取测试
   4. 映射保存功能
   0. 运行所有测试
```

## 🔧 测试功能详解

### 1. URL构建验证
- 验证搜索URL的正确构建
- 测试不同厂商的URL格式
- 确保搜索关键词正确编码

### 2. 单个番号搜索
- 测试指定番号的爬取
- 支持自定义厂商和番号
- 显示详细的爬取过程和结果

**示例**：
```
🎯 开始测试: ID-021
   厂商: ID, 番号: 21
📊 爬取结果:
   耗时: 4.56 秒
   发现映射: 12 个
📋 映射详情:
    1. CID: 5533id00021
       前缀: 5533id
       置信度: 0.85
       URL: https://www.dmm.co.jp/digital/videoa/-/detail/=/cid=5533id00021/
```

### 3. 批量爬取测试
- 测试连续多个番号的爬取
- 支持自定义厂商、起始番号和数量
- 建议数量不超过5个（避免对服务器造成压力）

### 4. 映射保存功能
- 测试发现的映射保存到数据库
- 验证JSON配置文件更新
- 确保数据持久化正常

## ⚠️ 重要注意事项

### 安全使用原则
1. **遵守网站条款**：请遵守DMM网站的使用条款
2. **合理频率**：建议设置3-5秒的请求间隔
3. **小规模测试**：避免大规模或高频率爬取
4. **尊重服务器**：如遇访问限制请立即停止
5. **仅供学习**：此脚本仅用于测试和学习目的

### 网络要求
- ✅ 能够访问DMM官网
- ✅ 稳定的网络连接
- ✅ 合理的网络延迟

### 系统要求
- ✅ Python 3.7+
- ✅ 已安装依赖：`requests`, `beautifulsoup4`
- ✅ 完整的项目文件结构

## 📊 测试结果解读

### 成功指标
- ✅ **发现映射数量**：ID-021应该发现12个映射
- ✅ **匹配率**：与预期CID的匹配率应≥80%
- ✅ **置信度**：大部分映射置信度应≥0.7
- ✅ **响应时间**：单次搜索应在10秒内完成

### 常见问题
1. **未发现映射**：
   - 检查网络连接
   - 确认DMM网站可访问
   - 验证搜索关键词格式

2. **匹配率低**：
   - 可能是页面结构变化
   - 检查CID提取逻辑
   - 验证相关性检查规则

3. **访问被限制**：
   - 降低请求频率
   - 更换网络环境
   - 检查User-Agent设置

## 🛠️ 故障排除

### 调试模式
在脚本中添加详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 手动验证
访问测试URL验证页面内容：
```
https://video.dmm.co.jp/av/list/?key=ID00021
```

### 检查依赖
```bash
pip install requests beautifulsoup4 lxml
```

## 📈 性能优化建议

### 1. 请求频率控制
```python
# 在爬虫中设置合理间隔
time.sleep(3)  # 3秒间隔
```

### 2. 错误重试机制
```python
# 网络异常时的重试
max_retries = 3
for attempt in range(max_retries):
    try:
        response = session.get(url)
        break
    except Exception as e:
        if attempt == max_retries - 1:
            raise e
        time.sleep(5)
```

### 3. 缓存机制
- 避免重复请求相同URL
- 缓存已验证的CID
- 使用本地数据库索引

## 📝 测试报告

测试完成后会生成详细报告：
```
dmm_crawler_test_results_20240101_120000.json
```

报告包含：
- 测试时间戳
- 各项测试结果
- 性能数据
- 错误信息

## 🎯 下一步计划

测试成功后可以：
1. 集成到主搜索系统
2. 设置定期爬取任务
3. 扩展到更多厂商
4. 优化爬取策略
5. 建立监控机制

## 📞 支持

如遇到问题：
1. 检查网络连接和DMM网站访问
2. 查看详细错误日志
3. 验证项目文件完整性
4. 确认依赖包版本兼容性

---

**🎉 祝你测试顺利！记住要遵守网站使用条款，合理使用爬虫功能。**
