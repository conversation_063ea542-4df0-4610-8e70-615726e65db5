#!/usr/bin/env python3
"""
简单测试自动映射学习功能
"""
import sys
import os
import sqlite3
sys.path.append('.')

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试自动映射学习基本功能")
    print("=" * 50)
    
    try:
        from modules.auto_mapping_learner import AutoMappingLearner
        
        # 创建学习器
        learner = AutoMappingLearner()
        print("✅ 自动映射学习器创建成功")
        
        # 测试学习一个新映射
        print("\n📝 测试学习新映射: TESTX-001 -> 999testx00001")
        
        result = learner.learn_from_successful_search(
            code="TESTX-001",
            found_cid="999testx00001", 
            search_source="test"
        )
        
        print(f"学习结果: {result}")
        
        if result["success"]:
            print("✅ 映射学习成功")
            
            # 验证是否写入数据库
            if os.path.exists("mmp/fast_dmm.db"):
                conn = sqlite3.connect("mmp/fast_dmm.db")
                cursor = conn.cursor()
                
                # 查询TESTX厂商
                cursor.execute('SELECT * FROM studio_mappings WHERE studio = ?', ('TESTX',))
                db_result = cursor.fetchone()
                
                if db_result:
                    print("✅ 数据库写入成功")
                    print(f"   数据库记录: {db_result}")
                else:
                    print("❌ 数据库写入失败")
                
                conn.close()
            else:
                print("⚠️ 数据库文件不存在")
        else:
            print(f"❌ 映射学习失败: {result['message']}")
        
        return result["success"]
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_sync():
    """测试数据库同步"""
    print("\n🔄 测试数据库同步功能")
    print("=" * 30)
    
    try:
        from modules.auto_mapping_learner import AutoMappingLearner
        
        learner = AutoMappingLearner()
        
        # 执行同步
        sync_result = learner.sync_database_with_config()
        
        if sync_result["success"]:
            print("✅ 数据库同步成功")
            stats = sync_result["stats"]
            print(f"   总厂商数: {stats['total_studios']}")
            print(f"   成功同步: {stats['synced_studios']}")
            print(f"   新增厂商: {stats['new_studios']}")
            print(f"   更新厂商: {stats['updated_studios']}")
            
            if stats['errors']:
                print(f"   错误数量: {len(stats['errors'])}")
                for error in stats['errors'][:3]:
                    print(f"     - {error}")
        else:
            print(f"❌ 数据库同步失败: {sync_result['message']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库同步测试失败: {e}")
        return False

def test_database_validation():
    """测试数据库验证"""
    print("\n🔍 测试数据库完整性验证")
    print("=" * 35)
    
    try:
        from modules.auto_mapping_learner import AutoMappingLearner
        
        learner = AutoMappingLearner()
        
        # 执行验证
        validation_result = learner.validate_database_integrity()
        
        if validation_result["success"]:
            print("✅ 数据库完整性验证完成")
            results = validation_result["results"]
            print(f"   总厂商数: {results['total_studios']}")
            print(f"   有效厂商: {results['valid_studios']}")
            print(f"   孤立备选映射: {results['orphaned_alternatives']}")
            print(f"   缺少主映射: {results['missing_primaries']}")
            print(f"   数量不一致: {results['inconsistent_counts']}")
            
            if validation_result["valid"]:
                print("✅ 数据库完整性良好")
            else:
                print("⚠️ 数据库存在完整性问题")
                if results['errors']:
                    print("   问题详情:")
                    for error in results['errors'][:3]:
                        print(f"     - {error}")
        else:
            print(f"❌ 数据库完整性验证失败: {validation_result['message']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库验证测试失败: {e}")
        return False

def check_database_status():
    """检查数据库状态"""
    print("\n📊 检查数据库状态")
    print("=" * 25)
    
    try:
        db_file = "mmp/fast_dmm.db"
        
        if os.path.exists(db_file):
            file_size = os.path.getsize(db_file)
            print(f"✅ 数据库文件存在: {file_size} bytes")
            
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 检查表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"📋 数据库表: {tables}")
            
            # 统计记录数
            if 'studio_mappings' in tables:
                cursor.execute('SELECT COUNT(*) FROM studio_mappings')
                main_count = cursor.fetchone()[0]
                print(f"📊 主映射记录数: {main_count}")
            
            if 'studio_alternative_mappings' in tables:
                cursor.execute('SELECT COUNT(*) FROM studio_alternative_mappings')
                alt_count = cursor.fetchone()[0]
                print(f"📊 备选映射记录数: {alt_count}")
            
            conn.close()
            return True
        else:
            print("❌ 数据库文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 检查数据库状态失败: {e}")
        return False

def test_integration_with_search():
    """测试与搜索模块的集成"""
    print("\n🔗 测试与搜索模块的集成")
    print("=" * 35)
    
    try:
        from modules.search_detail import SearchDetailModule
        
        # 创建搜索模块（启用自动学习）
        search_module = SearchDetailModule(
            enable_fast_search=True,
            enable_auto_learning=True
        )
        
        if search_module.enable_auto_learning and search_module.auto_learner:
            print("✅ 自动学习器已集成到搜索模块")
            
            # 获取学习统计
            stats = search_module.auto_learner.get_learning_statistics()
            print(f"   配置文件: {stats['config_file']}")
            print(f"   数据库文件: {stats['db_file']}")
            print(f"   日志文件: {stats['log_file']}")
            
            return True
        else:
            print("❌ 自动学习器未正确集成")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ 自动映射学习系统简单测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("数据库状态检查", check_database_status),
        ("基本功能测试", test_basic_functionality),
        ("数据库同步测试", test_database_sync),
        ("数据库验证测试", test_database_validation),
        ("搜索模块集成测试", test_integration_with_search)
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: 通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"📋 测试总结: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= 4:  # 至少4个测试通过
        print("\n🎉 自动映射数据库写入功能基本正常！")
        print("\n🔄 下一步操作:")
        print("1. 重启Streamlit应用")
        print("2. 测试NEO-834等实际番号的自动学习")
        print("3. 验证学习后的映射是否生效")
        print("4. 检查数据库和JSON配置的一致性")
        
        print("\n📋 功能特点:")
        print("   ✅ 自动学习新的厂商映射关系")
        print("   ✅ 同步更新JSON配置和SQLite数据库")
        print("   ✅ 支持数据库完整性验证")
        print("   ✅ 集成到搜索模块中")
        print("   ✅ 详细的学习日志和统计")
    else:
        print("\n⚠️ 部分功能存在问题，需要进一步调试")

if __name__ == "__main__":
    main()
