#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DMM番号搜索分析器
分析DMM网站的番号搜索机制并提供高效的搜索实现
"""

import re
import sqlite3
import requests
from typing import List, Dict, Optional, Set
from dataclasses import dataclass
import time


@dataclass
class VideoInfo:
    """视频信息数据类"""
    original_code: str
    dmm_cid: str
    title: str
    url: str
    studio: str


class DMMSearchAnalyzer:
    """DMM搜索机制分析器"""
    
    def __init__(self, db_path: str = "dmm_codes.db"):
        self.db_path = db_path
        self.init_database()
        
        # DMM厂商代码映射（部分常见的）
        self.studio_mappings = {
            'MILK': 'h_1240milk',
            'SSIS': 'h_1116ssis',
            'STARS': 'h_1116stars',
            'PRED': 'h_1116pred',
            'IPX': 'h_1116ipx',
            'MIDE': 'h_1116mide',
            'MIAA': 'h_1116miaa',
            'MIMK': 'h_1160mimk',
            'PPPD': 'h_1160pppd',
            'JUFE': 'h_1160jufe',
            'JUNY': 'h_1160juny',
            'EBOD': 'h_1160ebod',
            'MEYD': 'h_1160meyd',
            'JUL': 'h_1160jul',
            'JUY': 'h_1160juy',
            'JUX': 'h_1160jux',
            'MVSD': 'h_1160mvsd',
            'MVBD': 'h_1160mvbd',
            'WANZ': 'h_1160wanz',
            'WAAA': 'h_1160waaa',
            'CAWD': 'h_1160cawd',
            'CJOD': 'h_1160cjod',
            'DASD': 'h_1160dasd',
            'DASS': 'h_1160dass',
            'DVAJ': 'h_1160dvaj',
            'FSDSS': 'h_1116fsdss',
            'FLAV': 'h_1116flav',
            'HODV': 'h_1160hodv',
            'HUNTB': 'h_1160huntb',
            'HUNBL': 'h_1160hunbl',
            'IENF': 'h_1160ienf',
            'IENE': 'h_1160iene',
            'KTRA': 'h_1160ktra',
            'LULU': 'h_1160lulu',
            'MIDV': 'h_1116midv',
            'MUKC': 'h_1160mukc',
            'NHDTB': 'h_1160nhdtb',
            'NHDTA': 'h_1160nhdta',
            'ROYD': 'h_1160royd',
            'SAME': 'h_1160same',
            'SDAB': 'h_1160sdab',
            'SDDE': 'h_1160sdde',
            'SDEN': 'h_1160sden',
            'SDJS': 'h_1160sdjs',
            'SDMF': 'h_1160sdmf',
            'SDMM': 'h_1160sdmm',
            'SDMU': 'h_1160sdmu',
            'SDNM': 'h_1160sdnm',
            'SERO': 'h_1160sero',
            'SIVR': 'h_1160sivr',
            'SQTE': 'h_1160sqte',
            'STAR': 'h_1160star',
            'SW': 'h_1160sw',
            'TIKB': 'h_1160tikb',
            'TIKF': 'h_1160tikf',
            'TIKP': 'h_1160tikp',
            'VENX': 'h_1160venx',
            'VEC': 'h_1160vec',
            'VRTM': 'h_1160vrtm',
            'YMDD': 'h_1160ymdd',
        }
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建番号索引表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS video_codes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_code TEXT NOT NULL,
                normalized_code TEXT NOT NULL,
                dmm_cid TEXT NOT NULL,
                studio TEXT NOT NULL,
                title TEXT,
                url TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引以提高搜索速度
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_original_code ON video_codes(original_code)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_normalized_code ON video_codes(normalized_code)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_dmm_cid ON video_codes(dmm_cid)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_studio ON video_codes(studio)')
        
        conn.commit()
        conn.close()
    
    def parse_video_code(self, code: str) -> Dict[str, str]:
        """解析番号格式"""
        # 移除空格并转换为大写
        code = code.strip().upper()
        
        # 匹配模式：字母-数字 或 字母数字
        patterns = [
            r'^([A-Z]+)-(\d+)$',  # MILK-251
            r'^([A-Z]+)(\d+)$',   # MILK251
        ]
        
        for pattern in patterns:
            match = re.match(pattern, code)
            if match:
                studio = match.group(1)
                number = match.group(2)
                return {
                    'studio': studio,
                    'number': number,
                    'original': code
                }
        
        return {}
    
    def generate_code_variants(self, studio: str, number: str) -> List[str]:
        """生成番号的各种变体"""
        variants = []
        num_int = int(number)
        
        # 原始格式
        variants.append(f"{studio}-{number}")
        variants.append(f"{studio}{number}")
        
        # 补零格式（3位、4位、5位）
        for width in [3, 4, 5]:
            padded = str(num_int).zfill(width)
            variants.append(f"{studio}-{padded}")
            variants.append(f"{studio}{padded}")
            variants.append(f"{studio}0{padded}")
            variants.append(f"{studio}00{padded}")
        
        return list(set(variants))  # 去重
    
    def get_dmm_cid(self, studio: str, number: str) -> Optional[str]:
        """根据厂商和番号生成DMM的CID"""
        studio_upper = studio.upper()
        
        if studio_upper in self.studio_mappings:
            dmm_prefix = self.studio_mappings[studio_upper]
            # 补零到5位
            padded_number = str(int(number)).zfill(5)
            return f"{dmm_prefix}{padded_number}"
        
        return None
    
    def search_local_database(self, code: str) -> List[VideoInfo]:
        """在本地数据库中搜索"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 解析输入的番号
        parsed = self.parse_video_code(code)
        if not parsed:
            conn.close()
            return []
        
        # 生成所有可能的变体
        variants = self.generate_code_variants(parsed['studio'], parsed['number'])
        
        results = []
        for variant in variants:
            cursor.execute('''
                SELECT original_code, dmm_cid, title, url, studio
                FROM video_codes 
                WHERE original_code = ? OR normalized_code = ?
            ''', (variant, variant))
            
            rows = cursor.fetchall()
            for row in rows:
                results.append(VideoInfo(
                    original_code=row[0],
                    dmm_cid=row[1],
                    title=row[2] or "",
                    url=row[3] or "",
                    studio=row[4]
                ))
        
        conn.close()
        return results
    
    def search_dmm_website(self, code: str) -> Optional[VideoInfo]:
        """直接搜索DMM网站"""
        parsed = self.parse_video_code(code)
        if not parsed:
            return None
        
        # 尝试生成DMM CID
        dmm_cid = self.get_dmm_cid(parsed['studio'], parsed['number'])
        if dmm_cid:
            # 构造DMM详情页URL
            url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={dmm_cid}/"
            
            try:
                # 验证URL是否有效
                response = requests.head(url, timeout=10)
                if response.status_code == 200:
                    return VideoInfo(
                        original_code=code,
                        dmm_cid=dmm_cid,
                        title="",  # 需要进一步爬取
                        url=url,
                        studio=parsed['studio']
                    )
            except requests.RequestException:
                pass
        
        return None
    
    def add_to_database(self, video_info: VideoInfo):
        """添加视频信息到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 生成标准化代码
        parsed = self.parse_video_code(video_info.original_code)
        if parsed:
            normalized = f"{parsed['studio']}{parsed['number'].zfill(5)}"
        else:
            normalized = video_info.original_code
        
        cursor.execute('''
            INSERT OR REPLACE INTO video_codes 
            (original_code, normalized_code, dmm_cid, studio, title, url)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            video_info.original_code,
            normalized,
            video_info.dmm_cid,
            video_info.studio,
            video_info.title,
            video_info.url
        ))
        
        conn.commit()
        conn.close()
    
    def smart_search(self, code: str) -> List[VideoInfo]:
        """智能搜索：先查本地数据库，再查DMM网站"""
        # 1. 先查本地数据库
        local_results = self.search_local_database(code)
        if local_results:
            print(f"在本地数据库找到 {len(local_results)} 个结果")
            return local_results
        
        # 2. 查DMM网站
        print("本地数据库未找到，正在搜索DMM网站...")
        dmm_result = self.search_dmm_website(code)
        if dmm_result:
            # 保存到本地数据库
            self.add_to_database(dmm_result)
            print("找到结果并已保存到本地数据库")
            return [dmm_result]
        
        print("未找到任何结果")
        return []


def main():
    """主函数示例"""
    analyzer = DMMSearchAnalyzer()
    
    # 测试搜索
    test_codes = ["MILK-251", "MILK251", "SSIS-001", "STARS-123"]
    
    for code in test_codes:
        print(f"\n搜索番号: {code}")
        print("-" * 50)
        
        start_time = time.time()
        results = analyzer.smart_search(code)
        end_time = time.time()
        
        print(f"搜索耗时: {end_time - start_time:.2f}秒")
        
        for result in results:
            print(f"原始番号: {result.original_code}")
            print(f"DMM CID: {result.dmm_cid}")
            print(f"厂商: {result.studio}")
            print(f"URL: {result.url}")
            print()


if __name__ == "__main__":
    main()
