#!/usr/bin/env python3
"""
番号前缀与DMM CID前缀映射关系提取器
从CSV文件中自动提取和分析番号到CID的映射规则
"""

import re
import json
import time
from collections import defaultdict
from pathlib import Path


class StudioMappingExtractor:
    """番号厂商映射提取器"""
    
    def __init__(self):
        self.mappings = defaultdict(set)  # 使用set避免重复
        self.statistics = {
            "total_lines": 0,
            "matched_lines": 0,
            "unique_studios": 0,
            "mapping_patterns": defaultdict(int)
        }
    
    def extract_from_csv(self, csv_file_path: str):
        """从CSV文件中提取映射关系"""
        print(f"🔍 开始分析文件: {csv_file_path}")
        
        try:
            with open(csv_file_path, 'r', encoding='utf-8', errors='ignore') as file:
                # 尝试不同的CSV读取方式
                content = file.read()
                
                # 按行处理，因为可能不是标准CSV格式
                lines = content.split('\n')
                
                for line_num, line in enumerate(lines, 1):
                    self.statistics["total_lines"] += 1
                    
                    if line_num % 1000 == 0:
                        print(f"   处理进度: {line_num} 行...")
                    
                    # 提取映射关系
                    if self._extract_mapping_from_line(line):
                        self.statistics["matched_lines"] += 1
                
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return False
        
        print(f"✅ 文件分析完成")
        print(f"   总行数: {self.statistics['total_lines']}")
        print(f"   匹配行数: {self.statistics['matched_lines']}")
        
        return True
    
    def _extract_mapping_from_line(self, line: str) -> bool:
        """从单行文本中提取映射关系"""
        if not line.strip():
            return False

        # 定义番号匹配模式（更全面的格式）
        code_patterns = [
            r'([A-Z]+)-(\d+)',              # NACR-965
            r'([A-Z]+)(\d+)',               # NACR965 (无连字符)
            r'([A-Z]+)[-_](\d+)',           # NACR_965 (下划线)
        ]

        # 定义CID匹配模式（针对实际数据格式优化）
        cid_patterns = [
            # 主要格式：最后一个括号内的CID（避免匹配HD1080P等无关括号）
            r'\(([a-z]+)(\d{5,})\)(?![^(]*\([^)]*[a-z])',     # (gajk00024) - 纯字母前缀
            r'\((h_\d+[a-z]+)(\d{5,})\)(?![^(]*\([^)]*[a-z])', # (h_237nacr00965) - h_前缀
            r'\((\d+[a-z]+)(\d{5,})\)(?![^(]*\([^)]*[a-z])',   # (1sdmm00456) - 数字前缀

            # 备用格式：更严格的匹配
            r'\b(h_\d+[a-z]+)(\d{5,})\b',            # h_237nacr00965
            r'\b([a-z]{3,8})(\d{5,})\b',             # gajk00024 (3-8个字母)
            r'\b(\d{1,2}[a-z]{3,8})(\d{5,})\b',      # 1sdmm00456 (1-2个数字+字母)
        ]

        # 查找所有番号
        all_code_matches = []
        for pattern in code_patterns:
            matches = re.findall(pattern, line, re.IGNORECASE)
            all_code_matches.extend([(m[0].upper(), m[1]) for m in matches])

        if not all_code_matches:
            return False

        # 查找对应的CID
        found_mapping = False
        line_lower = line.lower()
        processed_mappings = set()  # 避免重复处理同一个映射

        for code_prefix, code_number in all_code_matches:
            # 尝试不同的CID模式
            for cid_pattern in cid_patterns:
                cid_matches = re.findall(cid_pattern, line_lower)

                for cid_match in cid_matches:
                    if len(cid_match) == 2:
                        cid_prefix, cid_number = cid_match

                        # 创建映射标识符，避免重复处理
                        mapping_key = f"{code_prefix}-{code_number}:{cid_prefix}{cid_number}"
                        if mapping_key in processed_mappings:
                            continue

                        # 验证是否为有效映射
                        if self._is_valid_mapping(code_prefix, code_number, cid_prefix, cid_number):
                            self.mappings[code_prefix].add(cid_prefix)
                            self.statistics["mapping_patterns"][f"{code_prefix} -> {cid_prefix}"] += 1
                            processed_mappings.add(mapping_key)
                            found_mapping = True

                            # 调试输出（仅前几个）
                            if self.statistics["matched_lines"] < 10:
                                print(f"   发现映射: {code_prefix}-{code_number} -> {cid_prefix}{cid_number}")

                            # 找到有效映射后，跳出内层循环，避免重复匹配
                            break

                # 如果已经找到映射，跳出CID模式循环
                if found_mapping:
                    break

        return found_mapping
    
    def _is_valid_mapping(self, code_prefix: str, code_number: str, cid_prefix: str, cid_number: str) -> bool:
        """验证映射关系是否有效"""
        try:
            # 基本验证
            if not code_prefix or not cid_prefix:
                return False

            # 过滤明显无效的CID前缀
            if len(cid_prefix) < 2 or len(cid_prefix) > 20:
                return False

            # 数字部分验证（允许补零）
            code_num = int(code_number)
            cid_num = int(cid_number)

            # 数字应该匹配（考虑补零）
            if code_num != cid_num:
                return False

            # CID数字部分长度验证（通常是3-5位）
            if len(cid_number) < 3 or len(cid_number) > 6:
                return False

            # CID前缀应该包含番号前缀的小写形式
            code_lower = code_prefix.lower()

            # 检查各种CID前缀格式的有效性
            if self._check_cid_prefix_validity(code_lower, cid_prefix):
                return True

            return False

        except (ValueError, AttributeError):
            return False

    def _check_cid_prefix_validity(self, code_lower: str, cid_prefix: str) -> bool:
        """检查CID前缀的有效性"""
        # 1. 直接匹配（最常见）
        if cid_prefix == code_lower:
            return True

        # 2. h_数字+番号前缀格式（如 h_237nacr）
        h_pattern = rf"^h_\d+{re.escape(code_lower)}$"
        if re.match(h_pattern, cid_prefix):
            return True

        # 3. 数字+番号前缀格式（如 1sdmm）
        num_pattern = rf"^\d+{re.escape(code_lower)}$"
        if re.match(num_pattern, cid_prefix):
            return True

        # 4. 番号前缀+下划线+数字+其他字母（如 abc_123def）
        complex_pattern = rf"^{re.escape(code_lower)}_\d+[a-z]*$"
        if re.match(complex_pattern, cid_prefix):
            return True

        # 5. 以番号前缀结尾（如 xxxnacr）
        if cid_prefix.endswith(code_lower) and len(cid_prefix) > len(code_lower):
            # 确保前面的部分是合理的（数字或h_数字）
            prefix_part = cid_prefix[:-len(code_lower)]
            if re.match(r'^(h_)?\d+$', prefix_part):
                return True

        # 6. 包含番号前缀但有其他合理的前后缀
        if code_lower in cid_prefix:
            # 检查是否是合理的包含关系
            parts = cid_prefix.split(code_lower)
            if len(parts) == 2:
                prefix_part, suffix_part = parts
                # 前缀应该是空、数字或h_数字
                # 后缀应该是空或少量字母
                if (not prefix_part or re.match(r'^(h_)?\d+$', prefix_part)) and \
                   (not suffix_part or re.match(r'^[a-z]{0,3}$', suffix_part)):
                    return True

        return False
    
    def generate_mappings_dict(self) -> dict:
        """生成最终的映射字典"""
        final_mappings = {}
        
        for studio, cid_prefixes in self.mappings.items():
            if len(cid_prefixes) == 1:
                # 只有一个映射，直接使用
                final_mappings[studio] = list(cid_prefixes)[0]
            else:
                # 多个映射，选择最常见的或最合理的
                # 优先选择最短的（通常是最基本的格式）
                best_prefix = min(cid_prefixes, key=len)
                final_mappings[studio] = best_prefix
                
                print(f"⚠️ {studio} 有多个映射: {list(cid_prefixes)}, 选择: {best_prefix}")
        
        self.statistics["unique_studios"] = len(final_mappings)
        return final_mappings
    
    def save_results(self, output_file: str = "studio_mappings_extracted.txt", 
                    json_file: str = "studio_mappings_extracted.json"):
        """保存提取结果"""
        final_mappings = self.generate_mappings_dict()
        
        # 按字母顺序排序
        sorted_mappings = dict(sorted(final_mappings.items()))
        
        # 保存为文本文件
        print(f"💾 保存映射关系到: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# 番号前缀与DMM CID前缀映射关系\n")
            f.write(f"# 提取时间: {self._get_timestamp()}\n")
            f.write(f"# 总计: {len(sorted_mappings)} 个厂商映射\n\n")
            
            for studio, cid_prefix in sorted_mappings.items():
                f.write(f"{studio}: {cid_prefix}\n")
        
        # 保存为JSON文件（用于FastDMM）
        print(f"💾 保存JSON配置到: {json_file}")

        # 创建完整的FastDMM配置格式
        json_config = {
            "studio_mappings": sorted_mappings,
            "search_patterns": {
                "number_padding": [3, 4, 5],
                "prefix_variations": ["", "0", "00"],
                "separator_variations": ["-", ""]
            },
            "extraction_info": {
                "source_file": "CSV数据提取",
                "timestamp": self._get_timestamp(),
                "total_lines_processed": self.statistics["total_lines"],
                "matched_lines": self.statistics["matched_lines"],
                "match_rate": f"{self.statistics['matched_lines']/self.statistics['total_lines']*100:.2f}%",
                "unique_studios_found": self.statistics["unique_studios"],
                "extraction_patterns": len(self.statistics["mapping_patterns"])
            },
            "quality_metrics": {
                "confidence_level": "high" if self.statistics["matched_lines"] > 1000 else "medium",
                "data_completeness": f"{len(sorted_mappings)} studios mapped",
                "validation_status": "auto-extracted"
            }
        }

        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_config, f, ensure_ascii=False, indent=2, sort_keys=False)
        
        return sorted_mappings
    
    def print_statistics(self):
        """打印统计信息"""
        print(f"\n📊 提取统计:")
        print(f"   总行数: {self.statistics['total_lines']}")
        print(f"   匹配行数: {self.statistics['matched_lines']}")
        print(f"   匹配率: {self.statistics['matched_lines']/self.statistics['total_lines']*100:.2f}%")
        print(f"   发现厂商: {len(self.mappings)}")
        print(f"   有效映射: {self.statistics['unique_studios']}")
        
        # 显示最常见的映射模式
        print(f"\n🔥 热门映射模式 (前10):")
        sorted_patterns = sorted(self.statistics["mapping_patterns"].items(), 
                               key=lambda x: x[1], reverse=True)
        for pattern, count in sorted_patterns[:10]:
            print(f"   {pattern}: {count}次")
    
    def print_preview(self, limit: int = 20):
        """预览提取的映射关系"""
        final_mappings = self.generate_mappings_dict()
        sorted_mappings = dict(sorted(final_mappings.items()))
        
        print(f"\n📋 映射关系预览 (前{limit}个):")
        print("-" * 40)
        
        for i, (studio, cid_prefix) in enumerate(sorted_mappings.items()):
            if i >= limit:
                break
            print(f"   {studio}: {cid_prefix}")
        
        if len(sorted_mappings) > limit:
            print(f"   ... 还有 {len(sorted_mappings) - limit} 个映射")
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def merge_with_existing_config(self, existing_config_path: str = "mmp/dmm_studio_mappings.json") -> dict:
        """将提取的映射关系与现有配置合并"""
        print(f"🔄 合并到现有配置: {existing_config_path}")

        # 读取现有配置
        existing_mappings = {}
        if Path(existing_config_path).exists():
            try:
                with open(existing_config_path, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
                    existing_mappings = existing_config.get('studio_mappings', {})
                print(f"   现有映射: {len(existing_mappings)} 个")
            except Exception as e:
                print(f"   ⚠️ 读取现有配置失败: {e}")
        else:
            print(f"   ⚠️ 现有配置文件不存在，将创建新文件")

        # 获取新提取的映射
        new_mappings = self.generate_mappings_dict()
        print(f"   新提取映射: {len(new_mappings)} 个")

        # 合并映射
        merged_mappings = existing_mappings.copy()
        conflicts = []
        new_additions = []

        for studio, new_cid_prefix in new_mappings.items():
            if studio in existing_mappings:
                if existing_mappings[studio] != new_cid_prefix:
                    conflicts.append({
                        "studio": studio,
                        "existing": existing_mappings[studio],
                        "new": new_cid_prefix
                    })
                    # 保持现有映射，但记录冲突
                    print(f"   ⚠️ 冲突: {studio} 现有={existing_mappings[studio]} 新={new_cid_prefix}")
            else:
                merged_mappings[studio] = new_cid_prefix
                new_additions.append(studio)

        print(f"   新增映射: {len(new_additions)} 个")
        print(f"   冲突映射: {len(conflicts)} 个")

        # 保存合并后的配置
        merged_config = {
            "studio_mappings": dict(sorted(merged_mappings.items())),
            "merge_info": {
                "timestamp": self._get_timestamp(),
                "original_count": len(existing_mappings),
                "extracted_count": len(new_mappings),
                "merged_count": len(merged_mappings),
                "new_additions": len(new_additions),
                "conflicts": len(conflicts)
            }
        }

        # 备份原文件
        if Path(existing_config_path).exists():
            backup_path = f"{existing_config_path}.backup.{int(time.time())}"
            import shutil
            shutil.copy2(existing_config_path, backup_path)
            print(f"   📦 原文件已备份到: {backup_path}")

        # 保存合并后的文件
        with open(existing_config_path, 'w', encoding='utf-8') as f:
            json.dump(merged_config, f, ensure_ascii=False, indent=2)

        # 保存冲突报告
        if conflicts:
            conflict_file = "mapping_conflicts.json"
            with open(conflict_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "timestamp": self._get_timestamp(),
                    "conflicts": conflicts
                }, f, ensure_ascii=False, indent=2)
            print(f"   ⚠️ 冲突详情已保存到: {conflict_file}")

        return merged_config


def main():
    """主函数"""
    print("🚀 番号前缀与DMM CID前缀映射关系提取器")
    print("=" * 60)
    
    # 输入文件路径
    input_file = r"c:\Users\<USER>\Downloads\Telegram Desktop\x1080x.12万.title.csv"
    
    # 检查文件是否存在
    if not Path(input_file).exists():
        print(f"❌ 文件不存在: {input_file}")
        print("请确认文件路径是否正确")
        return
    
    # 创建提取器
    extractor = StudioMappingExtractor()
    
    # 提取映射关系
    if not extractor.extract_from_csv(input_file):
        print("❌ 提取失败")
        return
    
    # 显示统计信息
    extractor.print_statistics()
    
    # 预览结果
    extractor.print_preview()
    
    # 保存结果
    mappings = extractor.save_results()
    
    print(f"\n🎉 提取完成！")
    print(f"   发现 {len(mappings)} 个厂商映射关系")
    print(f"   结果已保存到:")
    print(f"   - studio_mappings_extracted.txt (文本格式)")
    print(f"   - studio_mappings_extracted.json (JSON格式)")
    
    print(f"\n💡 使用建议:")
    print(f"   1. 检查提取的映射关系是否正确")
    print(f"   2. 将JSON文件合并到 mmp/dmm_studio_mappings.json")
    print(f"   3. 运行 python fast_dmm_manager.py build 重建索引")


if __name__ == "__main__":
    main()
