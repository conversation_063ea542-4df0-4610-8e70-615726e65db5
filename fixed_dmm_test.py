#!/usr/bin/env python3
"""
修复后的DMM爬虫测试 - 处理年龄验证和正确搜索格式
"""
import sys
import time
sys.path.append('.')

def test_age_verification():
    """测试年龄验证处理"""
    print("🔐 测试年龄验证处理...")
    
    try:
        from modules.dmm_search_crawler import DMMSearchCrawler
        
        print("   初始化爬虫（包含年龄验证）...")
        crawler = DMMSearchCrawler()
        
        print(f"   年龄验证状态: {'✅ 已验证' if crawler.age_verified else '❌ 未验证'}")
        
        return crawler.age_verified
        
    except Exception as e:
        print(f"❌ 年龄验证测试失败: {e}")
        return False

def test_search_format():
    """测试搜索格式修正"""
    print("\n📝 测试搜索格式修正...")
    
    test_cases = [
        ("ID", 21, "id00021"),
        ("SSIS", 1, "ssis00001"),
        ("NEO", 834, "neo00834"),
    ]
    
    print("   修正后的搜索格式（小写+严格5位数字）:")
    
    for studio, number, expected_format in test_cases:
        studio_lower = studio.lower()
        actual_format = f"{studio_lower}{number:05d}"
        
        print(f"   📋 {studio}-{number:03d}:")
        print(f"      预期格式: {expected_format}")
        print(f"      实际格式: {actual_format}")
        print(f"      匹配: {'✅' if actual_format == expected_format else '❌'}")
    
    return True

def test_fixed_crawl():
    """测试修复后的爬取功能"""
    print("\n🕷️ 测试修复后的爬取功能...")
    
    print("⚠️ 注意: 此测试将访问真实的DMM网站")
    user_input = input("是否继续？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("测试已取消")
        return False
    
    try:
        from modules.dmm_search_crawler import DMMSearchCrawler
        
        print("\n🔍 初始化修复后的爬虫...")
        crawler = DMMSearchCrawler()
        
        if not crawler.age_verified:
            print("❌ 年龄验证失败，无法继续测试")
            return False
        
        # 测试ID-021（使用修正后的格式）
        print("\n📝 测试 ID-021 (使用修正格式: id00021)")
        
        start_time = time.time()
        mappings = crawler._crawl_single_number("ID", 21)
        end_time = time.time()
        
        print(f"\n📊 爬取结果:")
        print(f"   耗时: {end_time - start_time:.2f} 秒")
        print(f"   发现映射: {len(mappings)} 个")
        
        if mappings:
            print(f"\n📋 发现的CID:")
            for i, mapping in enumerate(mappings, 1):
                print(f"   {i:2d}. {mapping['cid']} -> {mapping['prefix']} (置信度: {mapping['confidence']:.2f})")
            
            # 验证是否包含预期的CID
            expected_cids = [
                "5533id00021", "5532id00021", "5531id00021", "5530id00021",
                "5529id00021", "h_113id00021", "5526id00021", "5524id00021",
                "5525id00021", "5522id00021", "5521id00021", "5519id00021"
            ]
            
            found_cids = [m['cid'] for m in mappings]
            matched_count = sum(1 for cid in expected_cids if cid in found_cids)
            
            print(f"\n🎯 验证结果:")
            print(f"   预期CID: {len(expected_cids)} 个")
            print(f"   匹配CID: {matched_count} 个")
            print(f"   匹配率: {matched_count/len(expected_cids)*100:.1f}%")
            
            if matched_count > 0:
                print("   ✅ 成功发现映射")
                
                # 显示匹配的CID
                print("   匹配的CID:")
                for cid in expected_cids:
                    if cid in found_cids:
                        print(f"      ✅ {cid}")
                
                # 显示未匹配的CID
                unmatched = [cid for cid in expected_cids if cid not in found_cids]
                if unmatched:
                    print("   未匹配的CID:")
                    for cid in unmatched:
                        print(f"      ❌ {cid}")
                
                return True
            else:
                print("   ❌ 未找到预期的CID")
                return False
        else:
            print("   ❌ 未发现任何映射")
            print("   可能原因:")
            print("   - 年龄验证仍有问题")
            print("   - 页面结构变化")
            print("   - 搜索格式仍需调整")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_url_construction():
    """测试URL构建"""
    print("\n🌐 测试URL构建...")
    
    from urllib.parse import quote
    
    test_cases = [
        ("ID", 21, "id00021"),
        ("SSIS", 1, "ssis00001"),
        ("NEO", 834, "neo00834"),
    ]
    
    for studio, number, search_key in test_cases:
        encoded_key = quote(search_key)
        search_url = f"https://video.dmm.co.jp/av/list/?key={encoded_key}"
        
        print(f"   📝 {studio}-{number:03d}:")
        print(f"      搜索关键词: {search_key}")
        print(f"      编码后: {encoded_key}")
        print(f"      完整URL: {search_url}")
        print(f"      URL有效: {'✅' if 'key=' in search_url and encoded_key else '❌'}")
        print()
    
    return True

def main():
    """主函数"""
    print("🛠️ 修复后的DMM爬虫测试")
    print("=" * 50)
    
    print("📋 修复内容:")
    print("   ✅ 添加年龄验证处理")
    print("   ✅ 修正搜索格式（小写+严格5位数字）")
    print("   ✅ 处理年龄验证重定向")
    print("   ✅ 增强错误处理")
    
    # 执行测试
    tests = [
        ("URL构建测试", test_url_construction),
        ("搜索格式测试", test_search_format),
        ("年龄验证测试", test_age_verification),
        ("修复后爬取测试", test_fixed_crawl),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                print(f"✅ {test_name}: 通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试总结: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= 3:
        print("\n🎉 修复效果良好！")
        
        print("\n📋 修复成果:")
        print("   ✅ 年龄验证处理正常")
        print("   ✅ 搜索格式修正完成")
        print("   ✅ URL构建正确")
        if passed_tests == len(tests):
            print("   ✅ 爬取功能恢复正常")
        
        print("\n💡 下一步:")
        print("   1. 验证其他厂商的爬取")
        print("   2. 测试批量爬取功能")
        print("   3. 检查映射数据库更新")
    else:
        print("\n⚠️ 仍需进一步调试")
        
        if passed_tests < 2:
            print("   建议检查:")
            print("   - 网络连接和DMM网站访问")
            print("   - 年龄验证机制")
            print("   - 搜索URL格式")

if __name__ == "__main__":
    main()
