#!/usr/bin/env python3
"""
调试页面解析问题
"""
import sys
import requests
import re
from bs4 import BeautifulSoup
from urllib.parse import quote
sys.path.append('.')

def debug_dmm_page():
    """调试DMM页面解析"""
    print("🔍 调试DMM页面解析...")
    
    try:
        # 创建session
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
            'Referer': 'https://www.dmm.co.jp/'
        })
        
        # 年龄验证
        print("1. 处理年龄验证...")
        age_check_url = "https://www.dmm.co.jp/age_check/=/declared=yes/?rurl=https%3A%2F%2Fwww.dmm.co.jp%2F"
        response = session.get(age_check_url, timeout=10)
        
        if response.status_code == 200:
            session.cookies.set('age_check_done', '1', domain='.dmm.co.jp')
            session.cookies.set('ckcy', '1', domain='.dmm.co.jp')
            print("   ✅ 年龄验证完成")
        else:
            print("   ❌ 年龄验证失败")
            return False
        
        # 访问搜索页面
        print("2. 访问搜索页面...")
        search_key = "id00021"
        encoded_key = quote(search_key)
        search_url = f"https://video.dmm.co.jp/av/list/?key={encoded_key}"
        
        print(f"   URL: {search_url}")
        
        response = session.get(search_url, timeout=15)
        
        print(f"   状态码: {response.status_code}")
        print(f"   最终URL: {response.url}")
        print(f"   内容长度: {len(response.content)} bytes")
        
        if response.status_code != 200:
            print("   ❌ 页面访问失败")
            return False
        
        # 解析页面
        print("3. 解析页面内容...")
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 分析页面标题
        title = soup.find('title')
        if title:
            print(f"   页面标题: {title.get_text().strip()}")
        
        # 查找所有链接
        all_links = soup.find_all('a', href=True)
        print(f"   总链接数: {len(all_links)}")
        
        # 分析链接类型
        cid_links = []
        detail_links = []
        videoa_links = []
        
        for link in all_links:
            href = link.get('href', '')
            if 'cid=' in href:
                cid_links.append(href)
            elif '/detail/' in href:
                detail_links.append(href)
            elif '/videoa/' in href:
                videoa_links.append(href)
        
        print(f"   CID链接: {len(cid_links)}")
        print(f"   详情链接: {len(detail_links)}")
        print(f"   VideoA链接: {len(videoa_links)}")
        
        # 显示前几个链接示例
        if cid_links:
            print("   CID链接示例:")
            for i, link in enumerate(cid_links[:3], 1):
                print(f"      {i}. {link}")
        
        if detail_links:
            print("   详情链接示例:")
            for i, link in enumerate(detail_links[:3], 1):
                print(f"      {i}. {link}")
        
        if videoa_links:
            print("   VideoA链接示例:")
            for i, link in enumerate(videoa_links[:3], 1):
                print(f"      {i}. {link}")
        
        # 查找页面中的CID文本
        print("4. 查找页面中的CID文本...")
        page_text = response.text
        
        # CID模式
        cid_patterns = [
            r'\b(\d+id\d{5})\b',      # 5531id00021
            r'\b(h_\d+id\d{5})\b',    # h_113id00021
            r'\b([a-z]+id\d{5})\b',   # mbddid00021
        ]
        
        found_cids = set()
        for pattern in cid_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            found_cids.update(matches)
        
        print(f"   文本中找到的CID: {len(found_cids)}")
        if found_cids:
            sample_cids = list(found_cids)[:10]
            print("   CID示例:")
            for i, cid in enumerate(sample_cids, 1):
                print(f"      {i}. {cid}")
        
        # 查找可能的产品容器
        print("5. 查找产品容器...")
        container_selectors = [
            'div[class*="product"]',
            'div[class*="item"]',
            'li[class*="product"]',
            'li[class*="item"]',
            '.tmb',
            '.thumb',
            '.tile'
        ]
        
        for selector in container_selectors:
            try:
                elements = soup.select(selector)
                if elements:
                    print(f"   选择器 '{selector}': {len(elements)} 个元素")
                    
                    # 检查第一个元素
                    if elements:
                        first_element = elements[0]
                        links_in_element = first_element.find_all('a', href=True)
                        print(f"      第一个元素包含 {len(links_in_element)} 个链接")
            except Exception as e:
                print(f"   选择器 '{selector}' 错误: {e}")
        
        # 保存页面用于分析
        print("6. 保存页面内容...")
        with open('debug_dmm_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("   页面已保存到: debug_dmm_page.html")
        
        # 检查是否有JavaScript渲染的内容
        print("7. 检查JavaScript内容...")
        script_tags = soup.find_all('script')
        print(f"   找到 {len(script_tags)} 个script标签")
        
        js_cids = set()
        for script in script_tags:
            script_content = script.get_text()
            if 'cid' in script_content.lower():
                # 查找JavaScript中的CID
                for pattern in cid_patterns:
                    matches = re.findall(pattern, script_content, re.IGNORECASE)
                    js_cids.update(matches)
        
        if js_cids:
            print(f"   JavaScript中的CID: {len(js_cids)}")
            for i, cid in enumerate(list(js_cids)[:5], 1):
                print(f"      {i}. {cid}")
        
        # 总结
        total_found = len(found_cids) + len(js_cids)
        print(f"\n📊 总结:")
        print(f"   HTML链接中的CID: {len(cid_links)}")
        print(f"   页面文本中的CID: {len(found_cids)}")
        print(f"   JavaScript中的CID: {len(js_cids)}")
        print(f"   总计发现的CID: {total_found}")
        
        if total_found > 0:
            print("   ✅ 页面包含CID数据")
            return True
        else:
            print("   ❌ 页面不包含CID数据")
            return False
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ DMM页面解析调试工具")
    print("=" * 50)
    
    print("⚠️ 注意: 此工具将访问真实的DMM网站")
    user_input = input("是否继续？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("调试已取消")
        return
    
    success = debug_dmm_page()
    
    if success:
        print("\n🎉 调试完成，发现了CID数据！")
        print("\n💡 下一步:")
        print("1. 查看保存的页面文件: debug_dmm_page.html")
        print("2. 根据发现的CID位置更新爬虫逻辑")
        print("3. 重新测试爬虫功能")
    else:
        print("\n❌ 调试完成，但未发现CID数据")
        print("   可能的原因:")
        print("   - 页面结构完全改变")
        print("   - 内容通过JavaScript动态加载")
        print("   - 需要额外的认证或参数")

if __name__ == "__main__":
    main()
