#!/usr/bin/env python3
"""
简单的CID验证测试
"""
import sys
sys.path.append('.')

def main():
    print("🔍 简单CID验证测试")
    print("=" * 40)
    
    try:
        print("1. 导入模块...")
        from modules.dmm_search_crawler import DMMSearchCrawler
        print("   ✅ 模块导入成功")
        
        print("2. 初始化爬虫...")
        crawler = DMMSearchCrawler()
        print(f"   ✅ 爬虫初始化成功，年龄验证: {crawler.age_verified}")
        
        print("3. 测试单个CID验证...")
        test_cid = "5531id00021"
        detail_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={test_cid}/"
        
        print(f"   测试CID: {test_cid}")
        print(f"   详情页URL: {detail_url}")
        
        user_input = input("   是否进行网络验证？(y/N): ").strip().lower()
        
        if user_input == 'y':
            print("   🌐 进行网络验证...")
            exists = crawler._verify_cid_exists(detail_url, test_cid)
            print(f"   验证结果: {'✅ 存在' if exists else '❌ 不存在'}")
            
            if exists:
                print("\n4. 测试完整爬取...")
                mappings = crawler._crawl_single_number("ID", 21)
                print(f"   发现映射: {len(mappings)} 个")
                
                for mapping in mappings[:5]:  # 只显示前5个
                    print(f"      - {mapping['cid']} -> {mapping['prefix']}")
        else:
            print("   💡 跳过网络验证")
        
        print("\n🎉 基本功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
