#!/usr/bin/env python3
"""
基础功能测试
"""
import json
import sqlite3
import os

def test_basic_functionality():
    """测试基础功能"""
    results = []
    
    try:
        # 1. 测试JSON文件加载
        results.append("1. 测试JSON文件加载...")
        
        if not os.path.exists('studio_mappings_all.json'):
            results.append("❌ JSON文件不存在")
            return results
        
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        results.append(f"✅ JSON加载成功: {len(mappings)} 个厂商")
        
        # 2. 测试数据库创建
        results.append("\n2. 测试数据库创建...")
        
        # 确保目录存在
        os.makedirs('mmp', exist_ok=True)
        
        # 创建数据库
        db_path = 'mmp/fast_dmm.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建映射表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS studio_mappings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                studio TEXT UNIQUE NOT NULL,
                mapping_type TEXT NOT NULL,
                primary_mapping TEXT NOT NULL,
                mapping_count INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS studio_alternative_mappings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                studio TEXT NOT NULL,
                alternative_mapping TEXT NOT NULL,
                priority INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        results.append("✅ 数据库表创建成功")
        
        # 3. 测试数据插入
        results.append("\n3. 测试数据插入...")
        
        # 清空现有数据
        cursor.execute('DELETE FROM studio_alternative_mappings')
        cursor.execute('DELETE FROM studio_mappings')
        
        # 插入一些测试数据
        test_count = 0
        for studio, mapping_info in list(mappings.items())[:10]:  # 只插入前10个
            cursor.execute('''
                INSERT INTO studio_mappings 
                (studio, mapping_type, primary_mapping, mapping_count)
                VALUES (?, ?, ?, ?)
            ''', (
                studio,
                mapping_info['type'],
                mapping_info['primary'],
                mapping_info['count']
            ))
            
            # 插入备选映射
            for i, alt_mapping in enumerate(mapping_info.get('alternatives', [])):
                cursor.execute('''
                    INSERT INTO studio_alternative_mappings
                    (studio, alternative_mapping, priority)
                    VALUES (?, ?, ?)
                ''', (studio, alt_mapping, i + 1))
            
            test_count += 1
        
        conn.commit()
        conn.close()
        
        results.append(f"✅ 数据插入成功: {test_count} 个厂商")
        
        # 4. 验证数据库文件
        if os.path.exists(db_path):
            file_size = os.path.getsize(db_path)
            results.append(f"✅ 数据库文件已创建: {file_size} 字节")
        else:
            results.append("❌ 数据库文件未找到")
        
        results.append("\n✅ 所有基础测试通过")
        
    except Exception as e:
        results.append(f"\n❌ 测试失败: {e}")
        import traceback
        results.append(traceback.format_exc())
    
    return results

def main():
    """主函数"""
    print("🧪 基础功能测试")
    print("=" * 50)
    
    results = test_basic_functionality()
    
    # 输出结果
    for result in results:
        print(result)
    
    # 同时保存到文件
    with open('basic_test_result.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(results))
    
    print("\n结果已保存到: basic_test_result.txt")

if __name__ == "__main__":
    main()
