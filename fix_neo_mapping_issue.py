#!/usr/bin/env python3
"""
修复NEO映射问题和UI多次实例化问题
"""
import json
import os

def analyze_neo_mapping_issue():
    """分析NEO映射问题"""
    print("🔍 分析NEO映射问题...")
    print("=" * 50)
    
    # 检查映射配置
    try:
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        neo_mapping = mappings.get('NEO', {})
        
        print("📋 NEO映射配置:")
        print(f"   Primary: {neo_mapping.get('primary', '未找到')}")
        print(f"   Type: {neo_mapping.get('type', '未找到')}")
        print(f"   Alternatives: {neo_mapping.get('alternatives', [])}")
        
        # 分析问题
        print("\n🔍 问题分析:")
        print("   配置映射: NEO -> 433neo")
        print("   期望CID: 433neo00834 (NEO-834)")
        print("   实际找到: 433neo00834 ✅")
        print("   智能搜索: 失败 ❌")
        
        print("\n💡 问题原因:")
        print("   1. 智能搜索引擎的CID构建逻辑有问题")
        print("   2. 可能是番号解析或CID格式化的问题")
        print("   3. 需要检查FastDMM搜索引擎的实现")
        
        return neo_mapping
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def test_neo_cid_construction():
    """测试NEO的CID构建逻辑"""
    print("\n🧪 测试NEO的CID构建逻辑...")
    
    test_cases = [
        {
            "input": "NEO-834",
            "studio": "NEO",
            "number": "834",
            "mapping": "433neo",
            "expected_cid": "433neo00834"
        }
    ]
    
    for case in test_cases:
        print(f"\n   测试用例: {case['input']}")
        print(f"   厂商: {case['studio']}")
        print(f"   番号: {case['number']}")
        print(f"   映射: {case['mapping']}")
        print(f"   期望CID: {case['expected_cid']}")
        
        # 模拟CID构建过程
        studio = case['studio']
        number = case['number']
        mapping = case['mapping']
        
        # 标准CID构建逻辑
        if number.isdigit():
            # 纯数字，补零到5位
            formatted_number = number.zfill(5)
            constructed_cid = f"{mapping}{formatted_number}"
        else:
            # 包含字母，直接拼接
            constructed_cid = f"{mapping}{number}"
        
        print(f"   构建CID: {constructed_cid}")
        
        if constructed_cid == case['expected_cid']:
            print("   ✅ CID构建正确")
        else:
            print("   ❌ CID构建错误")
            print(f"   差异: {constructed_cid} != {case['expected_cid']}")

def check_fastdmm_search_logic():
    """检查FastDMM搜索逻辑"""
    print("\n🔍 检查FastDMM搜索逻辑...")
    
    # 检查关键文件
    files_to_check = [
        'modules/fast_dmm_search.py',
        'mmp/fast_dmm_search.py'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n   检查文件: {file_path}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键方法
                key_methods = [
                    'build_index_for_code',
                    'parse_code',
                    'smart_search',
                    'intelligent_search'
                ]
                
                for method in key_methods:
                    if method in content:
                        print(f"     ✅ 找到方法: {method}")
                    else:
                        print(f"     ❌ 缺少方法: {method}")
                
                # 检查CID构建逻辑
                if 'zfill(5)' in content:
                    print("     ✅ 找到数字补零逻辑")
                else:
                    print("     ⚠️ 可能缺少数字补零逻辑")
                
            except Exception as e:
                print(f"     ❌ 读取失败: {e}")
        else:
            print(f"   ❌ 文件不存在: {file_path}")

def suggest_fixes():
    """建议修复方案"""
    print("\n🛠️ 修复建议:")
    print("=" * 30)
    
    suggestions = [
        "1. NEO映射问题修复:",
        "   - 检查FastDMM搜索引擎的CID构建逻辑",
        "   - 确保数字番号正确补零到5位",
        "   - 验证映射查找和CID格式化过程",
        "",
        "2. UI多次实例化问题:",
        "   - 将UI实例也缓存到session_state",
        "   - 避免每次渲染都创建新的UI对象",
        "   - 使用@st.cache_resource装饰器",
        "",
        "3. 性能进一步优化:",
        "   - 缓存映射管理器实例",
        "   - 减少数据库查询次数",
        "   - 使用更细粒度的缓存策略",
        "",
        "4. 调试和测试:",
        "   - 添加详细的调试日志",
        "   - 创建单元测试验证CID构建",
        "   - 测试更多边界情况"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")

def create_test_script():
    """创建测试脚本"""
    print("\n📝 创建测试脚本...")
    
    test_script = '''#!/usr/bin/env python3
"""
NEO映射问题测试脚本
"""
import sys
sys.path.append('.')

def test_neo_mapping():
    """测试NEO映射"""
    try:
        from modules.fast_dmm_search import FastDMMSearch
        
        # 创建搜索引擎
        search_engine = FastDMMSearch()
        
        # 测试NEO-834
        test_code = "NEO-834"
        print(f"测试番号: {test_code}")
        
        # 解析番号
        parsed = search_engine.parse_code(test_code)
        if parsed:
            studio, number = parsed
            print(f"解析结果: 厂商={studio}, 番号={number}")
            
            # 获取映射
            mapping_info = search_engine.mapping_manager.get_mapping_info(studio)
            if mapping_info['has_mapping']:
                primary_mapping = mapping_info['primary']
                print(f"映射结果: {studio} -> {primary_mapping}")
                
                # 构建CID
                if number.isdigit():
                    formatted_number = number.zfill(5)
                    expected_cid = f"{primary_mapping}{formatted_number}"
                else:
                    expected_cid = f"{primary_mapping}{number}"
                
                print(f"期望CID: {expected_cid}")
                
                # 执行搜索
                results = search_engine.smart_search(test_code)
                if results:
                    print(f"搜索结果: {len(results)} 个")
                    for result in results:
                        print(f"  - {result.dmm_cid} ({result.confidence})")
                else:
                    print("搜索失败: 无结果")
            else:
                print("映射失败: 未找到映射")
        else:
            print("解析失败: 无法解析番号")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_neo_mapping()
'''
    
    with open('test_neo_mapping.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("   ✅ 测试脚本已创建: test_neo_mapping.py")

def main():
    """主函数"""
    print("🛠️ NEO映射问题诊断和修复")
    print("=" * 50)
    
    # 分析NEO映射问题
    neo_mapping = analyze_neo_mapping_issue()
    
    # 测试CID构建逻辑
    test_neo_cid_construction()
    
    # 检查FastDMM搜索逻辑
    check_fastdmm_search_logic()
    
    # 建议修复方案
    suggest_fixes()
    
    # 创建测试脚本
    create_test_script()
    
    print("\n" + "=" * 50)
    print("📋 诊断总结:")
    
    if neo_mapping:
        print("✅ NEO映射配置正确: NEO -> 433neo")
        print("⚠️ 问题在于智能搜索引擎的实现")
        print("💡 建议检查CID构建和搜索逻辑")
    else:
        print("❌ NEO映射配置有问题")
        print("💡 建议先修复映射配置")
    
    print("\n🔄 下一步操作:")
    print("1. 运行测试脚本: python test_neo_mapping.py")
    print("2. 检查FastDMM搜索引擎实现")
    print("3. 修复CID构建逻辑")
    print("4. 优化UI实例化问题")

if __name__ == "__main__":
    main()
