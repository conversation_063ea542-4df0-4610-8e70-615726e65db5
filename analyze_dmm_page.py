#!/usr/bin/env python3
"""
分析DMM页面结构工具
"""
import sys
import re
sys.path.append('.')

def analyze_dmm_page():
    """分析DMM页面结构"""
    print("🔍 分析DMM页面结构...")
    
    try:
        from modules.dmm_search_crawler import DMMSearchCrawler
        from bs4 import BeautifulSoup
        from urllib.parse import quote
        
        print("1. 初始化爬虫...")
        crawler = DMMSearchCrawler()
        
        if not crawler.age_verified:
            print("❌ 年龄验证失败")
            return False
        
        print("2. 获取页面内容...")
        search_key = "id00021"
        encoded_key = quote(search_key)
        search_url = f"https://video.dmm.co.jp/av/list/?key={encoded_key}"
        
        response = crawler.session.get(search_url, timeout=15)
        
        print(f"   状态码: {response.status_code}")
        print(f"   最终URL: {response.url}")
        print(f"   内容长度: {len(response.content)} bytes")
        
        if response.status_code != 200:
            print("❌ 页面访问失败")
            return False
        
        print("3. 解析页面结构...")
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 分析页面标题
        title = soup.find('title')
        if title:
            print(f"   页面标题: {title.get_text().strip()}")
        
        # 查找所有包含 'cid' 的文本
        print("\n4. 查找包含 'cid' 的内容...")
        page_text = response.text
        cid_matches = re.findall(r'[^a-zA-Z]cid[^a-zA-Z][^"\'>\s]*', page_text, re.IGNORECASE)
        
        if cid_matches:
            print(f"   找到 {len(cid_matches)} 个包含 'cid' 的文本:")
            for i, match in enumerate(cid_matches[:10], 1):  # 只显示前10个
                print(f"      {i}. {match.strip()}")
        else:
            print("   ❌ 未找到包含 'cid' 的文本")
        
        # 查找所有链接
        print("\n5. 分析页面链接...")
        all_links = soup.find_all('a', href=True)
        print(f"   总链接数: {len(all_links)}")
        
        # 分析链接类型
        cid_links = []
        detail_links = []
        other_links = []
        
        for link in all_links:
            href = link.get('href', '')
            if 'cid=' in href:
                cid_links.append(href)
            elif '/detail/' in href:
                detail_links.append(href)
            else:
                other_links.append(href)
        
        print(f"   包含 'cid=' 的链接: {len(cid_links)}")
        print(f"   包含 '/detail/' 的链接: {len(detail_links)}")
        print(f"   其他链接: {len(other_links)}")
        
        # 显示前几个相关链接
        if cid_links:
            print("\n   CID链接示例:")
            for i, link in enumerate(cid_links[:5], 1):
                print(f"      {i}. {link}")
        
        if detail_links:
            print("\n   详情链接示例:")
            for i, link in enumerate(detail_links[:5], 1):
                print(f"      {i}. {link}")
        
        # 查找可能的产品容器
        print("\n6. 分析产品容器...")
        possible_containers = [
            'div[class*="product"]',
            'div[class*="item"]',
            'div[class*="list"]',
            'div[class*="box"]',
            'li[class*="product"]',
            'li[class*="item"]',
            '.tmb',
            '.thumb',
            '.tile'
        ]
        
        for selector in possible_containers:
            try:
                elements = soup.select(selector)
                if elements:
                    print(f"   选择器 '{selector}': 找到 {len(elements)} 个元素")
                    
                    # 检查第一个元素的内容
                    if elements:
                        first_element = elements[0]
                        links_in_element = first_element.find_all('a', href=True)
                        print(f"      第一个元素包含 {len(links_in_element)} 个链接")
                        
                        for link in links_in_element[:3]:  # 只显示前3个
                            href = link.get('href', '')
                            text = link.get_text().strip()[:50]  # 只显示前50个字符
                            print(f"         链接: {href}")
                            if text:
                                print(f"         文本: {text}")
            except Exception as e:
                print(f"   选择器 '{selector}' 错误: {e}")
        
        # 保存页面内容用于进一步分析
        print("\n7. 保存页面内容...")
        with open('dmm_page_analysis.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("   页面内容已保存到: dmm_page_analysis.html")
        
        # 查找JavaScript中的数据
        print("\n8. 查找JavaScript数据...")
        script_tags = soup.find_all('script')
        print(f"   找到 {len(script_tags)} 个script标签")
        
        for i, script in enumerate(script_tags):
            script_content = script.get_text()
            if 'cid' in script_content.lower():
                print(f"   Script {i+1} 包含 'cid'")
                # 提取可能的CID
                cid_in_script = re.findall(r'["\']([a-z0-9]+id\d+)["\']', script_content, re.IGNORECASE)
                if cid_in_script:
                    print(f"      发现CID: {cid_in_script[:5]}")  # 只显示前5个
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ DMM页面结构分析工具")
    print("=" * 50)
    
    print("⚠️ 注意: 此工具将访问真实的DMM网站进行页面结构分析")
    user_input = input("是否继续？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("分析已取消")
        return
    
    success = analyze_dmm_page()
    
    if success:
        print("\n🎉 页面分析完成！")
        print("\n💡 下一步:")
        print("1. 查看保存的页面文件: dmm_page_analysis.html")
        print("2. 根据分析结果更新CSS选择器")
        print("3. 重新测试爬虫功能")
    else:
        print("\n❌ 页面分析失败")

if __name__ == "__main__":
    main()
