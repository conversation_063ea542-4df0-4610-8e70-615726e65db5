#!/usr/bin/env python3
"""
修正MILK映射问题
"""
import json
import os
from datetime import datetime

def fix_milk_mapping():
    """修正MILK映射"""
    print("🔧 修正MILK映射...")
    
    try:
        # 加载映射数据
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        
        if 'MILK' in mappings:
            current_info = mappings['MILK']
            current_primary = current_info['primary']
            alternatives = current_info.get('alternatives', [])
            
            print(f"   当前MILK映射: {current_primary}")
            print(f"   备选映射: {alternatives}")
            
            # MILK应该使用h_1240milk作为主映射
            correct_primary = 'h_1240milk'
            
            if current_primary != correct_primary:
                # 检查h_1240milk是否在备选映射中
                if correct_primary in alternatives:
                    # 将h_1240milk设为主映射，原主映射加入备选
                    new_alternatives = [current_primary] + [alt for alt in alternatives if alt != correct_primary]
                    
                    mappings['MILK'] = {
                        'primary': correct_primary,
                        'alternatives': new_alternatives,
                        'count': len(new_alternatives) + 1,
                        'type': 'multiple'
                    }
                    
                    print(f"   ✅ 修正: {current_primary} -> {correct_primary}")
                    print(f"   新的备选映射: {new_alternatives}")
                    
                    # 保存修正后的数据
                    with open('studio_mappings_all.json', 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    return True
                else:
                    print(f"   ⚠️ 在备选映射中未找到 {correct_primary}")
                    print("   可能需要检查原始数据")
                    return False
            else:
                print(f"   ✅ MILK映射已正确: {correct_primary}")
                return False
        else:
            print("   ❌ 未找到MILK映射")
            return False
            
    except Exception as e:
        print(f"❌ 修正失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def update_database_after_fix():
    """修正后更新数据库"""
    print("\n🔄 更新数据库...")
    
    try:
        import sys
        sys.path.append('.')
        from modules.mapping_manager import MappingManager
        
        # 重新创建映射管理器
        manager = MappingManager(
            config_file="studio_mappings_all.json",
            db_file="mmp/fast_dmm.db",
            enable_db_sync=True
        )
        
        # 验证MILK映射
        milk_info = manager.get_mapping_info('MILK')
        if milk_info['has_mapping']:
            print(f"   ✅ MILK映射已更新: {milk_info['primary']} ({milk_info['type']})")
            if milk_info['alternatives']:
                print(f"   备选映射: {', '.join(milk_info['alternatives'][:3])}")
        else:
            print("   ❌ MILK映射未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库更新失败: {e}")
        return False

def test_milk_search():
    """测试MILK番号搜索"""
    print("\n🧪 测试MILK番号搜索...")
    
    try:
        import sys
        sys.path.append('.')
        from modules.fast_dmm_search import FastDMMSearch
        
        # 创建搜索引擎
        search_engine = FastDMMSearch(db_file="mmp/fast_dmm.db")
        
        # 测试MILK番号
        test_code = "MILK-251"
        print(f"   测试番号: {test_code}")
        
        result = search_engine.intelligent_search(test_code)
        
        if result['success']:
            if result['is_multiple']:
                print(f"   找到 {result['count']} 个映射:")
                for i, res in enumerate(result['results'][:3]):
                    print(f"     {i+1}. {res.dmm_cid} ({res.mapping_type})")
            else:
                if result['results']:
                    res = result['results'][0]
                    print(f"   CID: {res.dmm_cid}")
                    expected_cid = "h_1240milk00251"
                    if res.dmm_cid == expected_cid:
                        print("   ✅ 映射正确！")
                    else:
                        print(f"   ⚠️ 映射可能不正确，期望: {expected_cid}")
        else:
            print(f"   ❌ 搜索失败: {result['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_other_h_prefix_studios():
    """检查其他需要h_前缀的厂商"""
    print("\n🔍 检查其他h_前缀厂商...")
    
    # 这些厂商通常需要h_前缀
    h_prefix_studios = {
        'MILK': 'h_1240milk',
        'HUNTB': 'huntb',  # 这个不需要h_前缀
        'SCPX': 'h_565scpx',
        'SCOP': 'h_565scop',
        'NATR': 'h_067natr',
        'MUCH': 'h_796much'
    }
    
    try:
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        
        print("   检查结果:")
        for studio, expected in h_prefix_studios.items():
            if studio in mappings:
                current = mappings[studio]['primary']
                status = "✅" if current == expected else "⚠️"
                print(f"     {studio}: {current} {status}")
                if current != expected:
                    print(f"       期望: {expected}")
            else:
                print(f"     {studio}: 未找到")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🛠️ MILK映射修正工具")
    print("=" * 40)
    
    # 修正MILK映射
    fixed = fix_milk_mapping()
    
    if fixed:
        # 更新数据库
        update_database_after_fix()
        
        # 测试搜索
        test_milk_search()
    
    # 检查其他h_前缀厂商
    check_other_h_prefix_studios()
    
    print("\n" + "=" * 40)
    if fixed:
        print("🎉 MILK映射修正完成！")
        print("\n📋 建议:")
        print("1. 运行 python3 test_fast_dmm_updated.py 验证")
        print("2. 检查其他可能需要h_前缀的厂商")
    else:
        print("✅ MILK映射无需修正")

if __name__ == "__main__":
    main()
