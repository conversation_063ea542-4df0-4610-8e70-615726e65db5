#!/usr/bin/env python3
"""
FastDMM数据库管理工具
提供索引构建、维护、统计等功能
"""

import argparse
import sys
import time
from modules.search_detail import SearchDetailModule


def print_banner():
    """打印工具横幅"""
    print("=" * 60)
    print("🚀 FastDMM数据库管理工具")
    print("   高性能DMM番号搜索系统")
    print("=" * 60)


def build_index(args):
    """构建索引"""
    print("📦 开始构建FastDMM索引...")
    
    search_module = SearchDetailModule(enable_fast_search=True)
    
    if not search_module.enable_fast_search:
        print("❌ FastDMM搜索引擎不可用")
        return
    
    start_time = time.time()
    
    if args.studio:
        # 构建特定厂商索引
        studios = [s.upper() for s in args.studio]
        number_range = (args.start, args.end)
        
        print(f"🎯 构建厂商索引: {studios}")
        print(f"📊 番号范围: {args.start}-{args.end}")
        
        built_count = search_module.build_fast_search_index(studios, number_range)
    else:
        # 构建热门厂商索引
        print(f"🔥 构建热门厂商索引 (前{args.top}个)")
        print(f"📊 番号范围: {args.start}-{args.end}")
        
        built_count = search_module.build_fast_search_index(number_range=(args.start, args.end))
    
    elapsed_time = time.time() - start_time
    
    print(f"\n✅ 索引构建完成！")
    print(f"   📈 构建记录数: {built_count}")
    print(f"   ⏱️ 耗时: {elapsed_time:.2f}秒")
    
    # 显示统计信息
    stats = search_module.get_fast_search_stats()
    if "database_stats" in stats:
        db_stats = stats["database_stats"]
        print(f"   📊 数据库总记录: {db_stats['total_videos']}")
        print(f"   ✅ 已验证记录: {db_stats['verified_videos']}")
        print(f"   📈 验证率: {db_stats['verification_rate']:.2%}")


def show_stats(args):
    """显示统计信息"""
    print("📊 FastDMM数据库统计信息")
    print("-" * 40)
    
    search_module = SearchDetailModule(enable_fast_search=True)
    
    if not search_module.enable_fast_search:
        print("❌ FastDMM搜索引擎不可用")
        return
    
    stats = search_module.get_fast_search_stats()
    
    # 搜索性能统计
    print("🚀 搜索性能:")
    print(f"   总搜索次数: {stats['total_searches']}")
    print(f"   缓存命中: {stats['cache_hits']}")
    print(f"   缓存未命中: {stats['cache_misses']}")
    print(f"   缓存命中率: {stats['cache_hit_rate']:.2%}")
    print(f"   平均响应时间: {stats['average_response_time_ms']:.2f}ms")
    
    # 数据库统计
    if "database_stats" in stats:
        db_stats = stats["database_stats"]
        print(f"\n📊 数据库统计:")
        print(f"   总视频记录: {db_stats['total_videos']}")
        print(f"   已验证记录: {db_stats['verified_videos']}")
        print(f"   变体索引数: {db_stats['total_variants']}")
        print(f"   支持厂商数: {db_stats['total_studios']}")
        print(f"   验证率: {db_stats['verification_rate']:.2%}")
        
        # 热门厂商
        if db_stats['top_studios']:
            print(f"\n🏆 热门厂商 (前10):")
            for studio in db_stats['top_studios']:
                print(f"   {studio['studio']}: {studio['total_codes']}条 "
                      f"(验证率: {studio['success_rate']:.2%})")


def verify_urls(args):
    """验证URL有效性"""
    print(f"🔍 开始验证 {args.count} 个URL...")
    
    search_module = SearchDetailModule(enable_fast_search=True)
    
    if not search_module.enable_fast_search:
        print("❌ FastDMM搜索引擎不可用")
        return
    
    start_time = time.time()
    result = search_module.verify_fast_search_urls(args.count)
    elapsed_time = time.time() - start_time
    
    if "error" in result:
        print(f"❌ 验证失败: {result['error']}")
        return
    
    print(f"\n✅ URL验证完成！")
    print(f"   📊 检查总数: {result['total_checked']}")
    print(f"   ✅ 验证成功: {result['verified_count']}")
    print(f"   ❌ 验证失败: {result['failed_count']}")
    print(f"   📈 成功率: {result['success_rate']:.2%}")
    print(f"   ⏱️ 耗时: {elapsed_time:.2f}秒")


def cleanup_database(args):
    """清理数据库"""
    print(f"🧹 开始清理 {args.days} 天前的过期记录...")
    
    search_module = SearchDetailModule(enable_fast_search=True)
    
    if not search_module.enable_fast_search:
        print("❌ FastDMM搜索引擎不可用")
        return
    
    deleted_count = search_module.cleanup_fast_search_database(args.days)
    
    print(f"✅ 清理完成！删除了 {deleted_count} 条过期记录")


def list_studios(args):
    """列出支持的厂商"""
    print("📋 FastDMM支持的厂商列表:")
    print("-" * 40)
    
    search_module = SearchDetailModule(enable_fast_search=True)
    
    if not search_module.enable_fast_search:
        print("❌ FastDMM搜索引擎不可用")
        return
    
    studios = search_module.get_supported_studios()
    
    if not studios:
        print("❌ 没有找到支持的厂商")
        return
    
    # 按字母顺序排序并分列显示
    studios.sort()
    
    for i, studio in enumerate(studios, 1):
        print(f"{studio:8}", end="")
        if i % 8 == 0:  # 每行显示8个
            print()
    
    if len(studios) % 8 != 0:
        print()
    
    print(f"\n总计: {len(studios)} 个厂商")


def test_search(args):
    """测试搜索功能"""
    print(f"🔍 测试搜索: {args.code}")
    print("-" * 40)
    
    search_module = SearchDetailModule(enable_fast_search=True)
    
    start_time = time.time()
    result = search_module.search_dmm_enhanced(args.code)
    elapsed_time = time.time() - start_time
    
    if result["success"]:
        print("✅ 搜索成功！")
        print(f"   番号: {result['code']}")
        print(f"   CID: {result['cid']}")
        print(f"   厂牌: {result['label']}")
        print(f"   URL: {result['url']}")
        if 'confidence' in result:
            print(f"   置信度: {result['confidence']:.2f}")
        if 'source' in result:
            print(f"   数据源: {result['source']}")
        print(f"   响应时间: {elapsed_time*1000:.2f}ms")
    else:
        print("❌ 搜索失败")
        print(f"   错误信息: {result['message']}")
        print(f"   响应时间: {elapsed_time*1000:.2f}ms")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="FastDMM数据库管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 构建热门厂商索引
  python fast_dmm_manager.py build --top 10 --start 1 --end 300
  
  # 构建特定厂商索引
  python fast_dmm_manager.py build --studio MILK SSIS --start 1 --end 500
  
  # 显示统计信息
  python fast_dmm_manager.py stats
  
  # 验证URL
  python fast_dmm_manager.py verify --count 100
  
  # 清理数据库
  python fast_dmm_manager.py cleanup --days 30
  
  # 列出支持的厂商
  python fast_dmm_manager.py studios
  
  # 测试搜索
  python fast_dmm_manager.py test MILK-251
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 构建索引命令
    build_parser = subparsers.add_parser('build', help='构建索引')
    build_parser.add_argument('--studio', nargs='+', help='指定厂商 (如: MILK SSIS)')
    build_parser.add_argument('--top', type=int, default=10, help='热门厂商数量 (默认: 10)')
    build_parser.add_argument('--start', type=int, default=1, help='起始番号 (默认: 1)')
    build_parser.add_argument('--end', type=int, default=300, help='结束番号 (默认: 300)')
    build_parser.set_defaults(func=build_index)
    
    # 统计信息命令
    stats_parser = subparsers.add_parser('stats', help='显示统计信息')
    stats_parser.set_defaults(func=show_stats)
    
    # 验证URL命令
    verify_parser = subparsers.add_parser('verify', help='验证URL有效性')
    verify_parser.add_argument('--count', type=int, default=50, help='验证数量 (默认: 50)')
    verify_parser.set_defaults(func=verify_urls)
    
    # 清理数据库命令
    cleanup_parser = subparsers.add_parser('cleanup', help='清理数据库')
    cleanup_parser.add_argument('--days', type=int, default=30, help='清理天数 (默认: 30)')
    cleanup_parser.set_defaults(func=cleanup_database)
    
    # 列出厂商命令
    studios_parser = subparsers.add_parser('studios', help='列出支持的厂商')
    studios_parser.set_defaults(func=list_studios)
    
    # 测试搜索命令
    test_parser = subparsers.add_parser('test', help='测试搜索功能')
    test_parser.add_argument('code', help='要搜索的番号')
    test_parser.set_defaults(func=test_search)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print_banner()
    args.func(args)


if __name__ == "__main__":
    main()
