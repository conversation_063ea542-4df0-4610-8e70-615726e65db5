#!/usr/bin/env python3
"""
检查映射数据问题
"""
import json
import sys
sys.path.append('.')

def check_mapping_issues():
    """检查映射数据中的问题"""
    print("🔍 检查映射数据问题...")
    
    try:
        # 加载映射数据
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        
        # 检查一些常见厂商的映射
        test_studios = ['SSIS', 'GDRD', 'MOND', 'JUQ', 'MIDV', 'CAWD', 'IPX']
        
        print("📊 常见厂商映射检查:")
        problematic_mappings = []
        
        for studio in test_studios:
            if studio in mappings:
                info = mappings[studio]
                primary = info['primary']
                alternatives = info.get('alternatives', [])
                
                print(f"\n   {studio}:")
                print(f"     类型: {info['type']}")
                print(f"     主映射: {primary}")
                if alternatives:
                    print(f"     备选映射: {', '.join(alternatives[:3])}{'...' if len(alternatives) > 3 else ''}")
                
                # 检查是否有问题的映射
                all_mappings = [primary] + alternatives
                for mapping in all_mappings:
                    # 检查是否有不合理的前缀
                    if mapping.startswith('h_') and len(mapping) > 10:
                        problematic_mappings.append((studio, mapping))
                    elif mapping.startswith(('1', '2', '3', '4', '5')) and len(mapping) > 8:
                        problematic_mappings.append((studio, mapping))
            else:
                print(f"   {studio}: 未找到映射")
        
        # 显示问题映射
        if problematic_mappings:
            print(f"\n⚠️ 发现 {len(problematic_mappings)} 个可能有问题的映射:")
            for studio, mapping in problematic_mappings[:10]:  # 只显示前10个
                print(f"   {studio} -> {mapping}")
            if len(problematic_mappings) > 10:
                print(f"   ... 还有 {len(problematic_mappings) - 10} 个")
        
        # 分析映射模式
        print(f"\n📈 映射模式分析:")
        pattern_stats = {
            'direct': 0,      # 直接映射 (ssis -> ssis)
            'h_prefix': 0,    # h_前缀 (ssis -> h_1116ssis)
            'num_prefix': 0,  # 数字前缀 (ssis -> 1ssis)
            'complex': 0      # 复杂映射
        }
        
        for studio, info in mappings.items():
            primary = info['primary']
            studio_lower = studio.lower()
            
            if primary == studio_lower:
                pattern_stats['direct'] += 1
            elif primary.startswith('h_') and primary.endswith(studio_lower):
                pattern_stats['h_prefix'] += 1
            elif primary.replace(studio_lower, '').isdigit():
                pattern_stats['num_prefix'] += 1
            else:
                pattern_stats['complex'] += 1
        
        for pattern, count in pattern_stats.items():
            percentage = count / len(mappings) * 100
            print(f"   {pattern}: {count} ({percentage:.1f}%)")
        
        return problematic_mappings
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def check_original_csv_data():
    """检查原始CSV数据中的映射"""
    print(f"\n🔍 检查原始CSV数据...")
    
    try:
        # 检查是否有原始CSV文件
        import os
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv') and 'title' in f]
        
        if not csv_files:
            print("   ⚠️ 未找到原始CSV文件")
            return
        
        csv_file = csv_files[0]
        print(f"   检查文件: {csv_file}")
        
        # 读取前几行检查格式
        with open(csv_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()[:20]  # 只读前20行
        
        print("   前几行数据示例:")
        ssis_examples = []
        gdrd_examples = []
        
        for line in lines:
            if 'SSIS' in line.upper():
                ssis_examples.append(line.strip())
            elif 'GDRD' in line.upper():
                gdrd_examples.append(line.strip())
        
        if ssis_examples:
            print("   SSIS示例:")
            for example in ssis_examples[:3]:
                print(f"     {example}")
        
        if gdrd_examples:
            print("   GDRD示例:")
            for example in gdrd_examples[:3]:
                print(f"     {example}")
        
    except Exception as e:
        print(f"   ❌ 检查原始数据失败: {e}")

def suggest_corrections():
    """建议修正方案"""
    print(f"\n💡 修正建议:")
    print("1. 对于SSIS类番号:")
    print("   错误映射: h_1116ssis -> 应该是: ssis")
    print("")
    print("2. 对于其他厂商:")
    print("   检查是否存在类似的h_前缀或数字前缀问题")
    print("")
    print("3. 修正方法:")
    print("   - 重新运行csv_mapping_extractor.py")
    print("   - 或者手动修正studio_mappings_all.json")
    print("   - 然后重新运行manual_setup.py导入数据")

def main():
    """主函数"""
    print("🔧 映射数据问题检查")
    print("=" * 50)
    
    # 检查映射问题
    problematic = check_mapping_issues()
    
    # 检查原始数据
    check_original_csv_data()
    
    # 建议修正方案
    suggest_corrections()
    
    print("\n" + "=" * 50)
    if problematic:
        print(f"⚠️ 发现 {len(problematic)} 个问题映射，建议修正")
    else:
        print("✅ 映射数据看起来正常")

if __name__ == "__main__":
    main()
