#!/usr/bin/env python3
"""
简单的FastDMM整合测试
"""

print("🧪 开始FastDMM整合测试")

try:
    print("📦 导入模块...")
    from modules.search_detail import SearchDetailModule
    print("✅ 模块导入成功")
    
    print("🚀 初始化搜索模块...")
    search_module = SearchDetailModule(enable_fast_search=True)
    print("✅ 搜索模块初始化成功")
    
    print(f"⚙️ FastDMM状态: {'启用' if search_module.enable_fast_search else '禁用'}")
    
    if search_module.enable_fast_search:
        print("📊 获取支持的厂商...")
        studios = search_module.get_supported_studios()
        print(f"✅ 支持 {len(studios)} 个厂商")
        print(f"   前5个: {studios[:5]}")
        
        print("🔍 测试搜索功能...")
        result = search_module.search_dmm_enhanced("MILK-251")
        
        if result["success"]:
            print("✅ 搜索成功！")
            print(f"   番号: {result['code']}")
            print(f"   CID: {result['cid']}")
            print(f"   厂牌: {result['label']}")
            if 'confidence' in result:
                print(f"   置信度: {result['confidence']}")
            if 'source' in result:
                print(f"   数据源: {result['source']}")
        else:
            print(f"❌ 搜索失败: {result['message']}")
    
    print("🎉 测试完成！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
