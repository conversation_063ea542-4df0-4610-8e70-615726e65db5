#!/usr/bin/env python3
"""
验证报告分析工具
对比历史报告，分析优化效果，提出改进建议
"""
import json
import glob
from datetime import datetime
from typing import Dict, List, Tuple

class VerificationAnalyzer:
    """验证报告分析器"""
    
    def __init__(self):
        self.reports = []
        self.analysis_results = {}
    
    def load_all_reports(self):
        """加载所有验证报告"""
        print("📊 加载所有验证报告...")
        
        # 查找所有报告文件
        report_files = []
        report_files.extend(glob.glob("*verification_report_*.json"))
        report_files.extend(glob.glob("scientific_verification_report_*.json"))
        report_files.extend(glob.glob("intelligent_verification_report_*.json"))
        report_files.extend(glob.glob("configurable_verification_report_*.json"))
        
        print(f"发现 {len(report_files)} 个报告文件")
        
        for file_path in sorted(report_files):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 提取报告信息
                report_info = {
                    'file': file_path,
                    'type': self.detect_report_type(file_path),
                    'timestamp': data.get('summary', {}).get('timestamp', ''),
                    'data': data
                }
                
                self.reports.append(report_info)
                print(f"✅ 加载: {file_path}")
                
            except Exception as e:
                print(f"❌ 加载失败: {file_path} - {e}")
        
        # 按时间排序
        self.reports.sort(key=lambda x: x['timestamp'])
        print(f"📈 按时间排序完成，共 {len(self.reports)} 个报告")
    
    def detect_report_type(self, filename: str) -> str:
        """检测报告类型"""
        if 'fixed_verification' in filename:
            return 'fixed'
        elif 'scientific_verification' in filename:
            return 'scientific'
        elif 'intelligent_verification' in filename:
            return 'intelligent'
        elif 'configurable_verification' in filename:
            return 'configurable'
        else:
            return 'unknown'
    
    def analyze_evolution(self):
        """分析验证方法的演进"""
        print("\n📈 分析验证方法演进...")
        
        evolution = []
        
        for report in self.reports:
            summary = report['data'].get('summary', {})
            
            evolution_point = {
                'type': report['type'],
                'timestamp': report['timestamp'],
                'studios': summary.get('total_studios', 0),
                'prefixes': summary.get('total_prefixes', summary.get('total_mappings', 0)),
                'success_rate': summary.get('overall_success_rate', 0),
                'requests': summary.get('total_requests', 0),
                'time': summary.get('verification_time', 0),
                'frequency': summary.get('avg_requests_per_minute', 0)
            }
            
            evolution.append(evolution_point)
        
        self.analysis_results['evolution'] = evolution
        
        # 打印演进分析
        print("\n🔍 验证方法演进分析:")
        print("=" * 80)
        print(f"{'类型':<12} {'厂商数':<8} {'成功率':<10} {'请求数':<8} {'频率':<12} {'时间':<8}")
        print("-" * 80)
        
        for point in evolution:
            print(f"{point['type']:<12} {point['studios']:<8} {point['success_rate']:.1%}    {point['requests']:<8} {point['frequency']:.1f}req/min  {point['time']:.1f}s")
    
    def analyze_success_rate_trend(self):
        """分析成功率趋势"""
        print("\n📊 成功率趋势分析:")
        
        if len(self.reports) < 2:
            print("报告数量不足，无法分析趋势")
            return
        
        first_report = self.reports[0]['data']['summary']
        latest_report = self.reports[-1]['data']['summary']
        
        first_rate = first_report.get('overall_success_rate', 0)
        latest_rate = latest_report.get('overall_success_rate', 0)
        
        improvement = latest_rate - first_rate
        
        print(f"初始成功率: {first_rate:.1%}")
        print(f"最新成功率: {latest_rate:.1%}")
        print(f"改进幅度: {improvement:+.1%}")
        
        if improvement > 0.1:
            print("✅ 显著改进")
        elif improvement > 0:
            print("🟡 轻微改进")
        else:
            print("🔴 需要优化")
    
    def analyze_efficiency_trend(self):
        """分析效率趋势"""
        print("\n⚡ 效率趋势分析:")
        
        if len(self.reports) < 2:
            return
        
        first_report = self.reports[0]['data']['summary']
        latest_report = self.reports[-1]['data']['summary']
        
        first_freq = first_report.get('avg_requests_per_minute', 0)
        latest_freq = latest_report.get('avg_requests_per_minute', 0)
        
        freq_improvement = latest_freq - first_freq
        
        print(f"初始频率: {first_freq:.1f} 请求/分钟")
        print(f"最新频率: {latest_freq:.1f} 请求/分钟")
        print(f"效率提升: {freq_improvement:+.1f} 请求/分钟")
        
        if freq_improvement > 10:
            print("🚀 效率大幅提升")
        elif freq_improvement > 0:
            print("📈 效率有所提升")
        else:
            print("📉 效率需要优化")
    
    def analyze_range_discoveries(self):
        """分析番号范围发现"""
        print("\n🎯 番号范围发现分析:")
        
        latest_report = self.reports[-1]['data']
        range_discoveries = latest_report.get('range_discoveries', {})
        
        if not range_discoveries:
            print("最新报告中没有范围发现数据")
            return
        
        print("发现的番号范围分布:")
        for range_key, prefixes in range_discoveries.items():
            print(f"  {range_key}: {len(prefixes)} 个前缀")
            
            # 显示特殊案例
            if range_key != "0-99":
                print(f"    特殊案例: {', '.join(prefixes[:5])}")
    
    def identify_problem_areas(self):
        """识别问题区域"""
        print("\n🔍 问题区域识别:")
        
        latest_report = self.reports[-1]['data']
        detailed_results = latest_report.get('detailed_results', {})
        
        failed_studios = []
        poor_studios = []
        
        for studio, result in detailed_results.items():
            status = result.get('status', 'unknown')
            success_rate = result.get('success_rate', 0)
            
            if status == 'failed' or success_rate == 0:
                failed_studios.append(studio)
            elif status == 'poor' or success_rate < 0.5:
                poor_studios.append(studio)
        
        if failed_studios:
            print(f"❌ 完全失败的厂商 ({len(failed_studios)}个):")
            for studio in failed_studios[:10]:  # 只显示前10个
                print(f"   - {studio}")
        
        if poor_studios:
            print(f"⚠️ 表现较差的厂商 ({len(poor_studios)}个):")
            for studio in poor_studios[:10]:
                print(f"   - {studio}")
        
        if not failed_studios and not poor_studios:
            print("✅ 没有发现明显的问题区域")
    
    def generate_optimization_suggestions(self):
        """生成优化建议"""
        print("\n💡 优化建议:")
        
        if not self.reports:
            print("没有报告数据，无法生成建议")
            return
        
        latest_report = self.reports[-1]['data']
        summary = latest_report.get('summary', {})
        
        success_rate = summary.get('overall_success_rate', 0)
        frequency = summary.get('avg_requests_per_minute', 0)
        
        suggestions = []
        
        # 基于成功率的建议
        if success_rate < 0.9:
            suggestions.append("🎯 成功率优化: 当前成功率{:.1%}，建议扩展测试番号范围".format(success_rate))
        
        # 基于效率的建议
        if frequency < 30:
            suggestions.append("⚡ 效率优化: 当前频率{:.1f}req/min，可以适当提高请求频率".format(frequency))
        
        # 基于范围发现的建议
        range_discoveries = latest_report.get('range_discoveries', {})
        if range_discoveries:
            special_ranges = {k: v for k, v in range_discoveries.items() if k != "0-99"}
            if special_ranges:
                suggestions.append("🔍 范围优化: 发现特殊番号范围，建议优化测试点分布")
        
        # 基于问题区域的建议
        detailed_results = latest_report.get('detailed_results', {})
        failed_count = sum(1 for result in detailed_results.values() 
                          if result.get('status') == 'failed')
        
        if failed_count > 0:
            suggestions.append(f"🔧 失败处理: {failed_count}个厂商验证失败，需要特殊处理策略")
        
        if suggestions:
            for i, suggestion in enumerate(suggestions, 1):
                print(f"{i}. {suggestion}")
        else:
            print("✅ 当前验证系统表现良好，无需特殊优化")
    
    def generate_report(self):
        """生成完整分析报告"""
        print("\n" + "="*80)
        print("📊 验证系统分析报告")
        print("="*80)
        
        self.analyze_evolution()
        self.analyze_success_rate_trend()
        self.analyze_efficiency_trend()
        self.analyze_range_discoveries()
        self.identify_problem_areas()
        self.generate_optimization_suggestions()
        
        print("\n" + "="*80)
        print("📋 分析完成")
        print("="*80)

def main():
    """主函数"""
    print("📊 验证报告分析工具")
    print("="*60)
    
    analyzer = VerificationAnalyzer()
    
    # 加载所有报告
    analyzer.load_all_reports()
    
    if not analyzer.reports:
        print("❌ 没有找到验证报告文件")
        return
    
    # 生成分析报告
    analyzer.generate_report()

if __name__ == "__main__":
    main()
