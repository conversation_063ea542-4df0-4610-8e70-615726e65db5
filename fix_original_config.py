#!/usr/bin/env python3
"""
修正原有配置文件中的错误映射
"""
import json
import os
from datetime import datetime

def fix_original_config():
    """修正原有配置文件"""
    print("🔧 修正原有配置文件...")
    
    config_file = "mmp/dmm_studio_mappings.json"
    
    try:
        # 加载原有配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        studio_mappings = config.get('studio_mappings', {})
        
        print(f"   原有配置包含 {len(studio_mappings)} 个厂商映射")
        
        # 需要修正的映射
        corrections = {
            'SSIS': 'ssis',      # 从 h_1116ssis 改为 ssis
            'GDRD': 'gdrd',      # 确保是 gdrd
            'JUQ': 'juq',        # 确保是 juq
            'MIDV': 'midv',      # 确保是 midv
            'CAWD': 'cawd',      # 确保是 cawd
            'IPX': 'ipx',        # 确保是 ipx
            'HUNTB': 'huntb',    # 确保是 huntb
            'JUL': 'jul',        # 确保是 jul
            'ALDN': 'aldn',      # 确保是 aldn
            'VRKM': 'vrkm',      # 确保是 vrkm
            'SONE': 'sone'       # 确保是 sone
        }
        
        print("\n   检查需要修正的映射:")
        corrections_made = 0
        
        for studio, correct_mapping in corrections.items():
            if studio in studio_mappings:
                current_mapping = studio_mappings[studio]
                if current_mapping != correct_mapping:
                    print(f"     {studio}: {current_mapping} -> {correct_mapping}")
                    studio_mappings[studio] = correct_mapping
                    corrections_made += 1
                else:
                    print(f"     {studio}: ✅ 已正确 ({correct_mapping})")
            else:
                print(f"     {studio}: ⚠️ 未找到，添加映射")
                studio_mappings[studio] = correct_mapping
                corrections_made += 1
        
        if corrections_made > 0:
            # 备份原文件
            backup_file = f"{config_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            with open(config_file, 'r', encoding='utf-8') as f:
                backup_data = f.read()
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(backup_data)
            print(f"\n   📦 原文件已备份为: {backup_file}")
            
            # 保存修正后的配置
            config['studio_mappings'] = studio_mappings
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 已修正 {corrections_made} 个映射")
            return True
        else:
            print(f"   ✅ 没有需要修正的映射")
            return False
            
    except Exception as e:
        print(f"❌ 修正失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_config_consistency():
    """验证配置一致性"""
    print("\n🔍 验证配置一致性...")
    
    try:
        # 加载两个配置文件
        with open('mmp/dmm_studio_mappings.json', 'r', encoding='utf-8') as f:
            original_config = json.load(f)
        
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            new_config = json.load(f)
        
        original_mappings = original_config.get('studio_mappings', {})
        new_mappings = new_config.get('mappings', {})
        
        # 检查关键厂商的一致性
        key_studios = ['SSIS', 'GDRD', 'MOND', 'JUQ', 'MIDV']
        
        print("   关键厂商映射对比:")
        inconsistencies = 0
        
        for studio in key_studios:
            original_mapping = original_mappings.get(studio, '未找到')
            new_primary = new_mappings.get(studio, {}).get('primary', '未找到')
            
            if original_mapping == new_primary:
                print(f"     {studio}: ✅ 一致 ({original_mapping})")
            else:
                print(f"     {studio}: ⚠️ 不一致")
                print(f"       原配置: {original_mapping}")
                print(f"       新配置: {new_primary}")
                inconsistencies += 1
        
        if inconsistencies == 0:
            print("   ✅ 配置一致性验证通过")
            return True
        else:
            print(f"   ⚠️ 发现 {inconsistencies} 个不一致项")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_ssis_search_after_fix():
    """修正后测试SSIS搜索"""
    print("\n🧪 测试SSIS搜索...")
    
    try:
        import sys
        sys.path.append('.')
        from modules.fast_dmm_search import FastDMMSearch
        
        # 重新创建搜索引擎（会重新加载配置）
        search_engine = FastDMMSearch(
            db_file="mmp/fast_dmm.db",
            config_file="mmp/dmm_studio_mappings.json"
        )
        
        # 测试SSIS番号
        test_code = "SSIS-001"
        print(f"   测试番号: {test_code}")
        
        result = search_engine.intelligent_search(test_code)
        
        if result['success']:
            if result['is_multiple']:
                print(f"   找到 {result['count']} 个映射:")
                for i, res in enumerate(result['results'][:2]):
                    print(f"     {i+1}. {res.dmm_cid}")
            else:
                if result['results']:
                    res = result['results'][0]
                    print(f"   CID: {res.dmm_cid}")
                    print(f"   URL: {res.url}")
                    
                    expected_cid = "ssis00001"
                    if res.dmm_cid == expected_cid:
                        print("   ✅ 映射修正成功！")
                    else:
                        print(f"   ⚠️ 仍然不正确，期望: {expected_cid}")
        else:
            print(f"   ❌ 搜索失败: {result['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ 原有配置文件修正工具")
    print("=" * 50)
    
    # 修正原有配置文件
    fixed = fix_original_config()
    
    # 验证配置一致性
    verify_config_consistency()
    
    if fixed:
        # 测试SSIS搜索
        test_ssis_search_after_fix()
    
    print("\n" + "=" * 50)
    if fixed:
        print("🎉 原有配置文件修正完成！")
        print("\n📋 下一步:")
        print("1. 运行 python3 test_fast_dmm_updated.py 验证")
        print("2. 确认SSIS等番号映射正确")
        print("3. 继续系统集成")
    else:
        print("✅ 原有配置文件无需修正")

if __name__ == "__main__":
    main()
