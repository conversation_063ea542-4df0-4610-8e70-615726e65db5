#!/usr/bin/env python3
"""
分析CID重复情况，了解为什么一页有240个元素
"""
import sqlite3
from collections import Counter

def analyze_cid_duplicates(db_file: str = "dmm_selenium_database.db"):
    """分析CID重复情况"""
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        print("🔍 CID重复分析")
        print("=" * 60)
        
        # 获取第1页的所有CID
        cursor.execute('''
            SELECT cid, detail_url FROM dmm_works 
            WHERE page_number = 1 
            ORDER BY id
        ''')
        
        results = cursor.fetchall()
        
        if not results:
            print("❌ 没有找到第1页的数据")
            return
        
        print(f"📊 第1页总记录数: {len(results)}")
        
        # 统计CID出现次数
        cid_counter = Counter([row[0] for row in results])
        
        print(f"📊 唯一CID数量: {len(cid_counter)}")
        print(f"📊 总记录数: {sum(cid_counter.values())}")
        
        # 分析重复情况
        duplicates = {cid: count for cid, count in cid_counter.items() if count > 1}
        
        if duplicates:
            print(f"\n🔍 发现重复CID: {len(duplicates)}个")
            print("-" * 60)
            
            for cid, count in sorted(duplicates.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"CID: {cid} - 出现 {count} 次")
                
                # 显示该CID的所有URL
                cursor.execute('SELECT detail_url FROM dmm_works WHERE cid = ? AND page_number = 1', (cid,))
                urls = [row[0] for row in cursor.fetchall()]
                
                for i, url in enumerate(urls, 1):
                    print(f"  {i}. {url}")
                print()
        else:
            print("\n✅ 没有发现重复CID")
        
        # 分析URL模式
        print(f"\n🔗 URL模式分析:")
        print("-" * 60)
        
        url_patterns = {}
        for cid, url in results[:20]:  # 分析前20个
            # 提取URL中的参数
            if '?' in url:
                base_url, params = url.split('?', 1)
                pattern = f"基础URL + 参数({params})"
            else:
                pattern = "纯CID URL"
            
            url_patterns[pattern] = url_patterns.get(pattern, 0) + 1
        
        for pattern, count in url_patterns.items():
            print(f"{pattern}: {count}个")
        
        # 显示前20个记录的详细信息
        print(f"\n📋 前20个记录详情:")
        print("-" * 60)
        
        for i, (cid, url) in enumerate(results[:20], 1):
            print(f"{i:2d}. CID: {cid}")
            print(f"    URL: {url}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def analyze_element_structure():
    """分析页面元素结构（如果有调试HTML文件）"""
    
    import os
    from bs4 import BeautifulSoup
    
    debug_files = [f for f in os.listdir('.') if f.startswith('debug_selenium_page_')]
    
    if not debug_files:
        print("📄 没有找到调试HTML文件")
        return
    
    print(f"\n🔍 页面结构分析:")
    print("-" * 60)
    
    for debug_file in debug_files[:1]:  # 只分析第一个文件
        print(f"📄 分析文件: {debug_file}")
        
        try:
            with open(debug_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # 查找包含cid的链接
            cid_links = soup.find_all('a', href=lambda x: x and 'cid=' in x)
            
            print(f"📊 包含CID的链接总数: {len(cid_links)}")
            
            # 分析前10个链接的结构
            print(f"\n📋 前10个CID链接分析:")
            
            cid_counter = Counter()
            
            for i, link in enumerate(cid_links[:10]):
                href = link.get('href', '')
                
                # 提取CID
                import re
                cid_match = re.search(r'cid=([^&/]+)', href)
                cid = cid_match.group(1) if cid_match else 'Unknown'
                
                cid_counter[cid] += 1
                
                # 分析链接的父元素和上下文
                parent = link.parent
                parent_class = parent.get('class', []) if parent else []
                
                print(f"{i+1:2d}. CID: {cid}")
                print(f"    链接: {href}")
                print(f"    父元素: {parent.name if parent else 'None'} class={parent_class}")
                print(f"    链接文本: {link.get_text(strip=True)[:50]}...")
                print()
            
            # 统计CID重复情况
            duplicates = {cid: count for cid, count in cid_counter.items() if count > 1}
            if duplicates:
                print(f"🔍 在前10个链接中发现重复:")
                for cid, count in duplicates.items():
                    print(f"  {cid}: {count}次")
            
        except Exception as e:
            print(f"❌ 分析文件失败: {e}")

if __name__ == "__main__":
    print("🔍 DMM页面元素分析")
    print("=" * 60)
    
    # 分析数据库中的重复情况
    analyze_cid_duplicates()
    
    # 分析页面结构
    analyze_element_structure()
    
    print(f"\n✅ 分析完成")
