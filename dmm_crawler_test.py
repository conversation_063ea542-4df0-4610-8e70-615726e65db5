#!/usr/bin/env python3
"""
DMM爬虫测试脚本 - 安全测试DMM官网数据收集
"""
import sys
import os
import time
import json
from datetime import datetime
sys.path.append('.')

def show_disclaimer():
    """显示免责声明和使用须知"""
    print("🕷️ DMM爬虫测试脚本")
    print("=" * 60)
    print("⚠️ 重要提醒:")
    print("   - 此脚本仅用于测试和学习目的")
    print("   - 请遵守DMM网站的使用条款")
    print("   - 请勿进行大规模或高频率爬取")
    print("   - 建议设置合理的请求间隔（3-5秒）")
    print("   - 如遇到访问限制，请立即停止")
    print("   - 请尊重网站服务器资源")
    print()
    
    user_input = input("是否同意以上条款并继续测试？(y/N): ").strip().lower()
    return user_input == 'y'

def test_single_search():
    """测试单个番号搜索"""
    print("\n🔍 测试单个番号搜索")
    print("-" * 40)
    
    try:
        from modules.dmm_search_crawler import DMMSearchCrawler
        
        crawler = DMMSearchCrawler()
        
        # 测试用例
        test_cases = [
            ("ID", 21, "ID-021"),
            ("SSIS", 1, "SSIS-001"),
            ("NEO", 834, "NEO-834"),
        ]
        
        print("📋 可用测试用例:")
        for i, (studio, number, description) in enumerate(test_cases, 1):
            print(f"   {i}. {description}")
        
        choice = input("\n请选择测试用例 (1-3) 或输入自定义 (格式: STUDIO,NUMBER): ").strip()
        
        if choice in ['1', '2', '3']:
            studio, number, description = test_cases[int(choice) - 1]
        else:
            try:
                parts = choice.split(',')
                studio = parts[0].strip().upper()
                number = int(parts[1].strip())
                description = f"{studio}-{number}"
            except:
                print("❌ 输入格式错误，使用默认测试用例 ID-021")
                studio, number, description = "ID", 21, "ID-021"
        
        print(f"\n🎯 开始测试: {description}")
        print(f"   厂商: {studio}, 番号: {number}")
        
        # 执行爬取
        start_time = time.time()
        mappings = crawler._crawl_single_number(studio, number)
        end_time = time.time()
        
        print(f"\n📊 爬取结果:")
        print(f"   耗时: {end_time - start_time:.2f} 秒")
        print(f"   发现映射: {len(mappings)} 个")
        
        if mappings:
            print(f"\n📋 映射详情:")
            for i, mapping in enumerate(mappings, 1):
                print(f"   {i:2d}. CID: {mapping['cid']}")
                print(f"       前缀: {mapping['prefix']}")
                print(f"       置信度: {mapping['confidence']:.2f}")
                print(f"       URL: {mapping['url']}")
                if 'search_key' in mapping:
                    print(f"       搜索关键词: {mapping['search_key']}")
                print()
        else:
            print("   ⚠️ 未发现映射（可能是网络问题或页面结构变化）")
        
        return mappings
        
    except Exception as e:
        print(f"❌ 单个搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_batch_crawl():
    """测试批量爬取"""
    print("\n📦 测试批量爬取")
    print("-" * 40)
    
    try:
        from modules.dmm_search_crawler import DMMSearchCrawler
        
        crawler = DMMSearchCrawler()
        
        # 获取用户输入
        studio = input("请输入厂商代码 (如 ID): ").strip().upper()
        if not studio:
            studio = "ID"
            print(f"使用默认厂商: {studio}")
        
        start_num = input("请输入起始番号 (如 21): ").strip()
        if not start_num:
            start_num = "21"
            print(f"使用默认起始番号: {start_num}")
        
        try:
            start_num = int(start_num)
        except:
            start_num = 21
            print(f"番号格式错误，使用默认: {start_num}")
        
        count = input("请输入爬取数量 (建议不超过5): ").strip()
        if not count:
            count = "3"
            print(f"使用默认数量: {count}")
        
        try:
            count = int(count)
            if count > 5:
                print("⚠️ 数量过大，限制为5个")
                count = 5
        except:
            count = 3
            print(f"数量格式错误，使用默认: {count}")
        
        end_num = start_num + count - 1
        
        print(f"\n🎯 开始批量爬取:")
        print(f"   厂商: {studio}")
        print(f"   范围: {start_num} - {end_num}")
        print(f"   数量: {count}")
        
        # 执行批量爬取
        start_time = time.time()
        results = crawler.crawl_studio_mappings(studio, (start_num, end_num))
        end_time = time.time()
        
        print(f"\n📊 批量爬取结果:")
        print(f"   总耗时: {end_time - start_time:.2f} 秒")
        print(f"   发现映射: {results['total_found']} 个")
        print(f"   错误数量: {len(results['errors'])}")
        
        if results['discovered_mappings']:
            print(f"\n📋 详细结果:")
            for code, mappings in results['discovered_mappings'].items():
                print(f"   📝 {code}: {len(mappings)} 个映射")
                for mapping in mappings:
                    print(f"      - {mapping['cid']} (置信度: {mapping['confidence']:.2f})")
        
        if results['errors']:
            print(f"\n❌ 错误信息:")
            for error in results['errors']:
                print(f"   - {error}")
        
        return results
        
    except Exception as e:
        print(f"❌ 批量爬取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def test_mapping_save():
    """测试映射保存功能"""
    print("\n💾 测试映射保存功能")
    print("-" * 40)
    
    try:
        from modules.dmm_search_crawler import DMMSearchCrawler
        
        crawler = DMMSearchCrawler()
        
        # 模拟爬取结果
        mock_results = {
            "studio": "TEST",
            "crawl_time": datetime.now().isoformat(),
            "discovered_mappings": {
                "TEST-001": [
                    {
                        "cid": "testprefix00001",
                        "prefix": "testprefix",
                        "confidence": 0.8,
                        "url": "https://www.dmm.co.jp/digital/videoa/-/detail/=/cid=testprefix00001/",
                        "search_key": "TEST00001"
                    }
                ]
            },
            "total_found": 1,
            "errors": []
        }
        
        print("📝 模拟爬取结果:")
        print(f"   厂商: {mock_results['studio']}")
        print(f"   发现映射: {mock_results['total_found']}")
        
        # 测试保存功能
        save_result = crawler.save_discovered_mappings(mock_results)
        
        print(f"\n💾 保存结果:")
        print(f"   成功: {save_result['success']}")
        print(f"   消息: {save_result['message']}")
        
        if save_result['success'] and 'discovered_prefixes' in save_result:
            print(f"   发现前缀: {save_result['discovered_prefixes']}")
        
        return save_result['success']
        
    except Exception as e:
        print(f"❌ 映射保存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_url_validation():
    """测试URL构建和验证"""
    print("\n🌐 测试URL构建和验证")
    print("-" * 40)
    
    test_cases = [
        ("ID", 21, "ID00021"),
        ("SSIS", 1, "SSIS00001"),
        ("NEO", 834, "NEO00834"),
        ("MILK", 251, "MILK00251"),
        ("HODV", 21987, "HODV21987"),
    ]
    
    print("📋 URL构建测试:")
    
    for studio, number, expected_key in test_cases:
        # 模拟搜索格式生成
        search_formats = [
            f"{studio}{number:05d}",  # 最常见格式
            f"{studio}{number:04d}",
            f"{studio}{number:03d}",
            f"{studio}-{number:03d}",
            f"{studio} {number:03d}",
        ]
        
        primary_format = search_formats[0]
        
        from urllib.parse import quote
        encoded_key = quote(primary_format)
        search_url = f"https://video.dmm.co.jp/av/list/?key={encoded_key}"
        
        print(f"\n   📝 {studio}-{number}:")
        print(f"      搜索关键词: {primary_format}")
        print(f"      编码后: {encoded_key}")
        print(f"      完整URL: {search_url}")
        
        # 验证URL格式
        if f"key={encoded_key}" in search_url and encoded_key:
            print(f"      ✅ URL构建正确")
        else:
            print(f"      ❌ URL构建错误")
    
    return True

def save_test_results(results):
    """保存测试结果"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"dmm_crawler_test_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 测试结果已保存到: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ 保存测试结果失败: {e}")
        return False

def main():
    """主函数"""
    # 显示免责声明
    if not show_disclaimer():
        print("测试已取消")
        return
    
    print("\n🛠️ DMM爬虫功能测试")
    print("=" * 60)
    
    # 测试菜单
    tests = [
        ("URL构建验证", test_url_validation),
        ("单个番号搜索", test_single_search),
        ("批量爬取测试", test_batch_crawl),
        ("映射保存功能", test_mapping_save),
    ]
    
    print("📋 可用测试:")
    for i, (test_name, _) in enumerate(tests, 1):
        print(f"   {i}. {test_name}")
    print("   0. 运行所有测试")
    
    choice = input("\n请选择测试 (0-4): ").strip()
    
    test_results = {
        "timestamp": datetime.now().isoformat(),
        "tests": {}
    }
    
    if choice == '0':
        # 运行所有测试
        print("\n🚀 运行所有测试...")
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            
            try:
                start_time = time.time()
                result = test_func()
                end_time = time.time()
                
                test_results["tests"][test_name] = {
                    "success": bool(result),
                    "duration": end_time - start_time,
                    "result": result
                }
                
                if result:
                    print(f"✅ {test_name}: 通过")
                else:
                    print(f"❌ {test_name}: 失败")
                    
            except Exception as e:
                test_results["tests"][test_name] = {
                    "success": False,
                    "error": str(e)
                }
                print(f"❌ {test_name}: 异常 - {e}")
            
            # 测试间隔
            if test_name != tests[-1][0]:  # 不是最后一个测试
                print("⏳ 等待3秒...")
                time.sleep(3)
    
    elif choice in ['1', '2', '3', '4']:
        # 运行单个测试
        test_name, test_func = tests[int(choice) - 1]
        
        print(f"\n🚀 运行测试: {test_name}")
        print("=" * 60)
        
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            test_results["tests"][test_name] = {
                "success": bool(result),
                "duration": end_time - start_time,
                "result": result
            }
            
            if result:
                print(f"\n✅ {test_name}: 测试通过")
            else:
                print(f"\n❌ {test_name}: 测试失败")
                
        except Exception as e:
            test_results["tests"][test_name] = {
                "success": False,
                "error": str(e)
            }
            print(f"\n❌ {test_name}: 测试异常 - {e}")
    
    else:
        print("❌ 无效选择")
        return
    
    # 保存测试结果
    save_test_results(test_results)
    
    # 显示总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    total_tests = len(test_results["tests"])
    passed_tests = sum(1 for test in test_results["tests"].values() if test.get("success", False))
    
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   成功率: {passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "   成功率: 0%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！DMM爬虫功能正常")
        
        print("\n💡 使用建议:")
        print("   - 在生产环境中设置更长的请求间隔")
        print("   - 监控爬取质量和错误率")
        print("   - 定期验证映射数据的准确性")
        print("   - 遵守网站访问频率限制")
    else:
        print(f"\n⚠️ 有 {total_tests - passed_tests} 个测试失败")
        print("   建议检查网络连接和代码逻辑")

if __name__ == "__main__":
    main()
