#!/usr/bin/env python3
"""
修正映射数据问题
"""
import json
import os
from datetime import datetime

def fix_common_mapping_issues():
    """修正常见的映射问题"""
    print("🔧 修正常见映射问题...")
    
    try:
        # 加载现有映射数据
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        
        # 定义正确的映射（基于常见番号格式）
        correct_mappings = {
            'SSIS': 'ssis',
            'GDRD': 'gdrd', 
            'JUQ': 'juq',
            'MIDV': 'midv',
            'CAWD': 'cawd',
            'IPX': 'ipx',
            'MILK': 'milk',
            'HUNTB': 'huntb',
            'JUL': 'jul',
            'ALDN': 'aldn',
            'VRKM': 'vrkm',
            'SONE': 'sone'
        }
        
        print("📋 修正映射:")
        corrections_made = 0
        
        for studio, correct_mapping in correct_mappings.items():
            if studio in mappings:
                current_info = mappings[studio]
                current_primary = current_info['primary']
                
                if current_primary != correct_mapping:
                    print(f"   {studio}: {current_primary} -> {correct_mapping}")
                    
                    # 保存原来的映射作为备选
                    alternatives = current_info.get('alternatives', [])
                    if current_primary not in alternatives:
                        alternatives.insert(0, current_primary)
                    
                    # 更新映射
                    mappings[studio] = {
                        'primary': correct_mapping,
                        'alternatives': alternatives,
                        'count': len(alternatives) + 1,
                        'type': 'multiple' if alternatives else 'single'
                    }
                    corrections_made += 1
                else:
                    print(f"   {studio}: ✅ 已正确 ({correct_mapping})")
            else:
                print(f"   {studio}: ⚠️ 未找到")
        
        if corrections_made > 0:
            # 备份原文件
            backup_file = f"studio_mappings_all.json.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.rename('studio_mappings_all.json', backup_file)
            print(f"   📦 原文件已备份为: {backup_file}")
            
            # 更新统计信息
            single_count = sum(1 for info in mappings.values() if info['type'] == 'single')
            multiple_count = sum(1 for info in mappings.values() if info['type'] == 'multiple')
            
            data['statistics'] = {
                'total_studios': len(mappings),
                'single_mapping_studios': single_count,
                'multiple_mapping_studios': multiple_count
            }
            
            # 保存修正后的数据
            with open('studio_mappings_all.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 已修正 {corrections_made} 个映射")
            print(f"   📊 更新后统计: {single_count} 单一映射, {multiple_count} 多重映射")
            
            return True
        else:
            print("   ✅ 没有需要修正的映射")
            return False
            
    except Exception as e:
        print(f"❌ 修正失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_h_prefix_issues():
    """修正h_前缀问题"""
    print("\n🔧 修正h_前缀问题...")
    
    try:
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        
        h_prefix_fixes = 0
        
        for studio, info in mappings.items():
            primary = info['primary']
            alternatives = info.get('alternatives', [])
            
            # 检查主映射是否有不必要的h_前缀
            if primary.startswith('h_') and len(primary) > 8:
                # 尝试找到更简单的映射
                studio_lower = studio.lower()
                simple_mapping = None
                
                # 在备选映射中查找简单映射
                for alt in alternatives:
                    if alt == studio_lower or alt.endswith(studio_lower):
                        if len(alt) < len(primary):
                            simple_mapping = alt
                            break
                
                if simple_mapping:
                    print(f"   {studio}: {primary} -> {simple_mapping}")
                    
                    # 更新映射
                    new_alternatives = [primary] + [alt for alt in alternatives if alt != simple_mapping]
                    mappings[studio] = {
                        'primary': simple_mapping,
                        'alternatives': new_alternatives,
                        'count': len(new_alternatives) + 1,
                        'type': 'multiple'
                    }
                    h_prefix_fixes += 1
        
        if h_prefix_fixes > 0:
            # 保存修正后的数据
            with open('studio_mappings_all.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 修正了 {h_prefix_fixes} 个h_前缀问题")
            return True
        else:
            print("   ✅ 没有发现h_前缀问题")
            return False
            
    except Exception as e:
        print(f"❌ h_前缀修正失败: {e}")
        return False

def update_database():
    """更新数据库"""
    print("\n🔄 更新数据库...")
    
    try:
        import sys
        sys.path.append('.')
        from modules.mapping_manager import MappingManager
        
        # 重新创建映射管理器，这会自动同步数据到数据库
        manager = MappingManager(
            config_file="studio_mappings_all.json",
            db_file="mmp/fast_dmm.db",
            enable_db_sync=True
        )
        
        stats = manager.get_statistics()
        print(f"   ✅ 数据库已更新: {stats['total_studios']} 个厂商")
        
        # 验证几个关键映射
        test_studios = ['SSIS', 'GDRD', 'MOND']
        print("   🔍 验证关键映射:")
        for studio in test_studios:
            info = manager.get_mapping_info(studio)
            if info['has_mapping']:
                print(f"     {studio}: {info['primary']} ({info['type']})")
            else:
                print(f"     {studio}: 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库更新失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ 映射数据修正工具")
    print("=" * 50)
    
    # 步骤1: 修正常见映射问题
    step1 = fix_common_mapping_issues()
    
    # 步骤2: 修正h_前缀问题
    step2 = fix_h_prefix_issues()
    
    # 步骤3: 更新数据库
    if step1 or step2:
        step3 = update_database()
    else:
        print("\n✅ 没有修改，跳过数据库更新")
        step3 = True
    
    print("\n" + "=" * 50)
    if step1 or step2:
        if step3:
            print("🎉 映射数据修正完成！")
            print("\n📋 下一步:")
            print("1. 运行 python3 test_fast_dmm_updated.py 验证修正")
            print("2. 继续系统集成")
        else:
            print("⚠️ 修正完成但数据库更新失败")
    else:
        print("✅ 映射数据无需修正")

if __name__ == "__main__":
    main()
