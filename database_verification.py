#!/usr/bin/env python3
"""
数据库映射验证工具
使用新DMM爬虫验证数据库中所有厂商映射的真实性
"""
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Tuple
sys.path.append('.')

class DatabaseVerifier:
    """数据库映射验证器"""
    
    def __init__(self):
        self.crawler = None
        self.verification_results = {}
        self.start_time = None
        
    def initialize_crawler(self):
        """初始化DMM爬虫"""
        print("🔧 初始化DMM爬虫...")
        
        try:
            from modules.dmm_search_crawler import DMMSearchCrawler
            
            self.crawler = DMMSearchCrawler()
            
            if not self.crawler.age_verified:
                print("❌ 年龄验证失败")
                return False
            
            print("✅ DMM爬虫初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 爬虫初始化失败: {e}")
            return False
    
    def load_database_mappings(self):
        """加载数据库中的映射数据"""
        print("📖 加载数据库映射...")
        
        try:
            with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'mappings' not in data:
                print("❌ 映射数据格式错误")
                return None
            
            mappings = data['mappings']
            print(f"✅ 加载了 {len(mappings)} 个厂商映射")
            
            return mappings
            
        except Exception as e:
            print(f"❌ 加载映射数据失败: {e}")
            return None
    
    def verify_studio_mapping(self, studio: str, mapping_data: Dict) -> Dict:
        """验证单个厂商的映射"""
        print(f"\n🔍 验证厂商: {studio}")
        
        result = {
            'studio': studio,
            'total_mappings': 0,
            'verified_mappings': [],
            'failed_mappings': [],
            'success_rate': 0.0,
            'verification_time': 0,
            'status': 'unknown'
        }
        
        try:
            start_time = time.time()
            
            # 获取映射信息
            primary = mapping_data.get('primary', '')
            alternatives = mapping_data.get('alternatives', [])
            
            all_prefixes = [primary] + alternatives if primary else alternatives
            result['total_mappings'] = len(all_prefixes)
            
            print(f"   总映射数: {len(all_prefixes)}")
            print(f"   主映射: {primary}")
            print(f"   备选映射: {len(alternatives)} 个")
            
            if not all_prefixes:
                result['status'] = 'no_mappings'
                return result
            
            # 验证每个映射（使用测试番号）
            test_numbers = [1, 21, 100, 123]  # 常见的测试番号
            
            for prefix in all_prefixes:
                prefix_verified = False
                
                for test_num in test_numbers:
                    try:
                        # 构建测试CID
                        test_cid = f"{prefix}{test_num:05d}"
                        detail_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={test_cid}/"
                        
                        # 验证CID是否存在
                        if self.crawler._verify_cid_exists(detail_url, test_cid):
                            result['verified_mappings'].append({
                                'prefix': prefix,
                                'test_cid': test_cid,
                                'test_number': test_num
                            })
                            prefix_verified = True
                            print(f"      ✅ {prefix} (测试: {test_cid})")
                            break
                        
                        # 限制请求频率
                        time.sleep(0.5)
                        
                    except Exception as e:
                        print(f"      ⚠️ {prefix} 验证异常: {e}")
                        continue
                
                if not prefix_verified:
                    result['failed_mappings'].append(prefix)
                    print(f"      ❌ {prefix} (所有测试都失败)")
            
            # 计算成功率
            verified_count = len(result['verified_mappings'])
            total_count = len(all_prefixes)
            result['success_rate'] = verified_count / total_count if total_count > 0 else 0
            
            # 确定状态
            if result['success_rate'] >= 0.8:
                result['status'] = 'excellent'
            elif result['success_rate'] >= 0.5:
                result['status'] = 'good'
            elif result['success_rate'] >= 0.2:
                result['status'] = 'poor'
            else:
                result['status'] = 'failed'
            
            result['verification_time'] = time.time() - start_time
            
            print(f"   📊 验证完成: {verified_count}/{total_count} ({result['success_rate']:.1%})")
            
            return result
            
        except Exception as e:
            print(f"   ❌ 验证异常: {e}")
            result['status'] = 'error'
            result['error'] = str(e)
            return result
    
    def verify_all_mappings(self, mappings: Dict, limit: int = None) -> Dict:
        """验证所有映射"""
        print(f"\n🚀 开始批量验证...")
        
        self.start_time = time.time()
        studios = list(mappings.keys())
        
        if limit:
            studios = studios[:limit]
            print(f"⚠️ 限制验证数量: {limit} 个厂商")
        
        print(f"📋 计划验证 {len(studios)} 个厂商")
        
        for i, studio in enumerate(studios, 1):
            print(f"\n{'='*50}")
            print(f"进度: {i}/{len(studios)} ({i/len(studios):.1%})")
            
            mapping_data = mappings[studio]
            result = self.verify_studio_mapping(studio, mapping_data)
            self.verification_results[studio] = result
            
            # 显示进度
            elapsed = time.time() - self.start_time
            avg_time = elapsed / i
            remaining = (len(studios) - i) * avg_time
            
            print(f"⏱️ 已用时: {elapsed:.1f}s, 预计剩余: {remaining:.1f}s")
            
            # 适当休息，避免请求过于频繁
            if i % 5 == 0:
                print("😴 休息2秒...")
                time.sleep(2)
        
        return self.verification_results
    
    def generate_report(self) -> Dict:
        """生成验证报告"""
        print(f"\n📊 生成验证报告...")
        
        if not self.verification_results:
            return {}
        
        total_studios = len(self.verification_results)
        total_time = time.time() - self.start_time if self.start_time else 0
        
        # 统计各种状态
        status_counts = {
            'excellent': 0,  # >=80%
            'good': 0,       # >=50%
            'poor': 0,       # >=20%
            'failed': 0,     # <20%
            'error': 0,      # 验证异常
            'no_mappings': 0 # 无映射数据
        }
        
        total_mappings = 0
        total_verified = 0
        
        for studio, result in self.verification_results.items():
            status = result.get('status', 'unknown')
            if status in status_counts:
                status_counts[status] += 1
            
            total_mappings += result.get('total_mappings', 0)
            total_verified += len(result.get('verified_mappings', []))
        
        overall_success_rate = total_verified / total_mappings if total_mappings > 0 else 0
        
        report = {
            'summary': {
                'total_studios': total_studios,
                'total_mappings': total_mappings,
                'total_verified': total_verified,
                'overall_success_rate': overall_success_rate,
                'verification_time': total_time,
                'timestamp': datetime.now().isoformat()
            },
            'status_distribution': status_counts,
            'detailed_results': self.verification_results
        }
        
        return report
    
    def print_report(self, report: Dict):
        """打印验证报告"""
        if not report:
            print("❌ 无报告数据")
            return
        
        summary = report['summary']
        status_dist = report['status_distribution']
        
        print(f"\n" + "="*60)
        print(f"📊 数据库映射验证报告")
        print(f"="*60)
        
        print(f"\n📈 总体统计:")
        print(f"   验证厂商: {summary['total_studios']} 个")
        print(f"   总映射数: {summary['total_mappings']} 个")
        print(f"   验证成功: {summary['total_verified']} 个")
        print(f"   总成功率: {summary['overall_success_rate']:.1%}")
        print(f"   验证耗时: {summary['verification_time']:.1f} 秒")
        
        print(f"\n📋 厂商状态分布:")
        print(f"   🟢 优秀 (≥80%): {status_dist['excellent']} 个")
        print(f"   🟡 良好 (≥50%): {status_dist['good']} 个") 
        print(f"   🟠 较差 (≥20%): {status_dist['poor']} 个")
        print(f"   🔴 失败 (<20%): {status_dist['failed']} 个")
        print(f"   ❌ 异常: {status_dist['error']} 个")
        print(f"   ⚪ 无映射: {status_dist['no_mappings']} 个")
        
        # 显示问题厂商
        problem_studios = []
        for studio, result in report['detailed_results'].items():
            status = result.get('status', 'unknown')
            if status in ['failed', 'error', 'poor']:
                problem_studios.append((studio, status, result.get('success_rate', 0)))
        
        if problem_studios:
            print(f"\n⚠️ 需要关注的厂商:")
            for studio, status, rate in sorted(problem_studios, key=lambda x: x[2]):
                print(f"   {studio}: {status} ({rate:.1%})")
        
        # 显示优秀厂商
        excellent_studios = []
        for studio, result in report['detailed_results'].items():
            if result.get('status') == 'excellent':
                excellent_studios.append((studio, result.get('success_rate', 0)))
        
        if excellent_studios:
            print(f"\n✅ 优秀厂商 (前10个):")
            for studio, rate in sorted(excellent_studios, key=lambda x: x[1], reverse=True)[:10]:
                print(f"   {studio}: {rate:.1%}")

def main():
    """主函数"""
    print("🛠️ 数据库映射验证工具")
    print("=" * 60)
    
    print("💡 验证目标:")
    print("   - 验证数据库中所有厂商映射的真实性")
    print("   - 使用新DMM爬虫进行实际验证")
    print("   - 生成详细的质量报告")
    print("   - 识别需要清理的无效映射")
    
    # 用户确认
    user_input = input("\n是否开始验证？这可能需要30-60分钟 (y/N): ").strip().lower()
    
    if user_input != 'y':
        print("验证已取消")
        return
    
    # 询问验证数量限制
    limit_input = input("是否限制验证数量？(输入数字限制厂商数量，回车验证全部): ").strip()
    limit = None
    
    if limit_input.isdigit():
        limit = int(limit_input)
        print(f"⚠️ 将只验证前 {limit} 个厂商")
    
    # 执行验证
    verifier = DatabaseVerifier()
    
    # 1. 初始化爬虫
    if not verifier.initialize_crawler():
        print("❌ 爬虫初始化失败，验证终止")
        return
    
    # 2. 加载映射数据
    mappings = verifier.load_database_mappings()
    if not mappings:
        print("❌ 映射数据加载失败，验证终止")
        return
    
    # 3. 执行验证
    print(f"\n🚀 开始验证...")
    results = verifier.verify_all_mappings(mappings, limit)
    
    # 4. 生成报告
    report = verifier.generate_report()
    
    # 5. 显示报告
    verifier.print_report(report)
    
    # 6. 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"verification_report_{timestamp}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细报告已保存: {report_file}")
    except Exception as e:
        print(f"\n❌ 报告保存失败: {e}")
    
    # 7. 建议后续操作
    summary = report['summary']
    if summary['overall_success_rate'] < 0.7:
        print(f"\n💡 建议:")
        print(f"   - 总成功率较低 ({summary['overall_success_rate']:.1%})")
        print(f"   - 考虑清理失效映射")
        print(f"   - 重点关注失败和较差的厂商")
    else:
        print(f"\n🎉 数据库质量良好！")
        print(f"   - 总成功率: {summary['overall_success_rate']:.1%}")
        print(f"   - 可以考虑扩展映射数据")

if __name__ == "__main__":
    main()
