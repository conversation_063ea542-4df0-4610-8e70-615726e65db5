#!/usr/bin/env python3
"""
性能优化脚本 - 解决重复初始化问题
"""
import os
import time
import streamlit as st

def analyze_initialization_issue():
    """分析初始化问题"""
    print("🔍 分析Streamlit应用初始化问题")
    print("=" * 50)
    
    print("📋 问题分析:")
    print("1. 每次操作都重新初始化整个系统")
    print("2. FastDMM搜索引擎重复创建 (2244个厂商映射)")
    print("3. 映射管理器重复加载数据库")
    print("4. 数据库连接重复建立")
    print("")
    
    print("🎯 优化策略:")
    print("1. 使用 st.session_state 缓存初始化对象")
    print("2. 避免重复加载映射数据")
    print("3. 复用数据库连接")
    print("4. 延迟初始化非关键组件")
    print("")

def check_optimization_status():
    """检查优化状态"""
    print("📊 优化状态检查:")
    print("=" * 30)
    
    # 检查关键文件的优化状态
    optimizations = {
        "modules/search_detail_ui.py": {
            "description": "搜索详情UI模块",
            "optimized": False,
            "check_pattern": "session_state.search_detail_module"
        },
        "modules/search_detail.py": {
            "description": "搜索详情模块",
            "optimized": False,
            "check_pattern": "session_state.fast_dmm_search_engine"
        },
        "modules/fast_dmm_search.py": {
            "description": "FastDMM搜索引擎",
            "optimized": False,
            "check_pattern": "@st.cache_resource"
        }
    }
    
    for file_path, info in optimizations.items():
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if info["check_pattern"] in content:
                        info["optimized"] = True
                        print(f"   ✅ {info['description']}: 已优化")
                    else:
                        print(f"   ⚠️ {info['description']}: 需要优化")
            except Exception as e:
                print(f"   ❌ {info['description']}: 检查失败 - {e}")
        else:
            print(f"   ❌ {info['description']}: 文件不存在")
    
    return optimizations

def create_cache_decorator_example():
    """创建缓存装饰器示例"""
    print("\n💡 Streamlit缓存装饰器使用示例:")
    print("=" * 40)
    
    example_code = '''
# 方法1: 使用 @st.cache_resource 装饰器
@st.cache_resource
def get_fast_dmm_search_engine():
    """获取FastDMM搜索引擎实例 - 全局缓存"""
    print("🔧 初始化FastDMM搜索引擎...")
    return FastDMMSearch()

# 方法2: 使用 session_state 手动缓存
def get_search_module():
    """获取搜索模块 - session缓存"""
    if "search_detail_module" not in st.session_state:
        print("🔧 首次初始化搜索详情模块...")
        st.session_state.search_detail_module = SearchDetailModule()
    return st.session_state.search_detail_module

# 方法3: 混合缓存策略
@st.cache_resource
def get_mapping_manager():
    """获取映射管理器 - 全局缓存"""
    return MappingManager(
        config_file="studio_mappings_all.json",
        db_file="mmp/fast_dmm.db",
        enable_db_sync=True
    )
'''
    
    print(example_code)

def suggest_further_optimizations():
    """建议进一步优化"""
    print("\n🚀 进一步优化建议:")
    print("=" * 30)
    
    suggestions = [
        "1. 数据库连接池:",
        "   - 使用连接池管理数据库连接",
        "   - 避免频繁打开/关闭数据库",
        "",
        "2. 映射数据预加载:",
        "   - 应用启动时一次性加载所有映射",
        "   - 使用内存缓存减少磁盘IO",
        "",
        "3. 异步初始化:",
        "   - 非关键组件延迟初始化",
        "   - 后台异步加载大数据",
        "",
        "4. 状态管理优化:",
        "   - 减少不必要的状态更新",
        "   - 使用更细粒度的缓存键",
        "",
        "5. 内存管理:",
        "   - 定期清理过期缓存",
        "   - 监控内存使用情况"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")

def test_performance_improvement():
    """测试性能改进效果"""
    print("\n⏱️ 性能测试建议:")
    print("=" * 25)
    
    print("测试步骤:")
    print("1. 重启Streamlit应用")
    print("2. 观察首次初始化日志")
    print("3. 进行多次搜索操作")
    print("4. 检查是否还有重复初始化")
    print("")
    
    print("预期改进:")
    print("✅ 首次访问: 正常初始化 (~2-3秒)")
    print("✅ 后续操作: 无重复初始化 (~0.1秒)")
    print("✅ 内存使用: 稳定不增长")
    print("✅ 响应速度: 显著提升")

def create_monitoring_script():
    """创建监控脚本"""
    print("\n📊 性能监控脚本:")
    print("=" * 25)
    
    monitoring_code = '''
# 添加到应用开头
import time
import psutil
import os

def log_performance_metrics():
    """记录性能指标"""
    process = psutil.Process(os.getpid())
    memory_mb = process.memory_info().rss / 1024 / 1024
    
    print(f"📊 性能指标:")
    print(f"   内存使用: {memory_mb:.1f} MB")
    print(f"   CPU使用: {process.cpu_percent():.1f}%")
    
    # 检查session_state大小
    if hasattr(st, 'session_state'):
        cached_objects = [k for k in st.session_state.keys() 
                         if 'module' in k or 'engine' in k]
        print(f"   缓存对象: {len(cached_objects)} 个")
        for obj in cached_objects:
            print(f"     - {obj}")

# 在关键位置调用
log_performance_metrics()
'''
    
    print(monitoring_code)

def main():
    """主函数"""
    print("🛠️ Streamlit应用性能优化")
    print("=" * 50)
    
    # 分析问题
    analyze_initialization_issue()
    
    # 检查优化状态
    optimizations = check_optimization_status()
    
    # 创建示例代码
    create_cache_decorator_example()
    
    # 建议进一步优化
    suggest_further_optimizations()
    
    # 测试性能改进
    test_performance_improvement()
    
    # 创建监控脚本
    create_monitoring_script()
    
    print("\n" + "=" * 50)
    print("📋 优化总结:")
    
    optimized_count = sum(1 for opt in optimizations.values() if opt["optimized"])
    total_count = len(optimizations)
    
    print(f"✅ 已优化模块: {optimized_count}/{total_count}")
    
    if optimized_count == total_count:
        print("🎉 所有模块已优化完成！")
        print("🚀 请重启应用测试性能改进效果")
    else:
        print("⚠️ 还有模块需要优化")
        print("💡 建议继续优化剩余模块")
    
    print("\n🔄 下一步操作:")
    print("1. 重启Streamlit应用: Ctrl+C 然后 streamlit run gradio_app.py")
    print("2. 观察初始化日志变化")
    print("3. 测试多次搜索操作")
    print("4. 验证性能改进效果")

if __name__ == "__main__":
    main()
