#!/usr/bin/env python3
"""
后处理脚本 - 为已爬取的数据添加番号和厂商信息
"""
import sqlite3
import re

def generate_number_from_cid(cid: str) -> str:
    """从CID生成番号"""
    try:
        # 移除数字前缀（如1nhdtc00082 -> nhdtc00082）
        clean_cid = re.sub(r'^\d+', '', cid.lower())
        
        # 提取字母前缀和数字
        match = re.match(r'^([a-zA-Z]+)(\d+)$', clean_cid)
        if match:
            prefix = match.group(1).upper()
            number = int(match.group(2))
            
            # 生成标准番号格式
            return f"{prefix}-{number:03d}"
        
        return None
        
    except Exception:
        return None

def post_process_database(db_file: str = "dmm_requests_database.db"):
    """后处理数据库，添加番号和厂商信息"""
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()

        print("🔄 开始后处理数据库...")

        # 检查并添加缺少的列
        print("🔍 检查数据库结构...")

        # 获取表结构
        cursor.execute("PRAGMA table_info(dmm_works)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"   现有列: {columns}")

        # 添加缺少的列
        if 'number' not in columns:
            print("   ➕ 添加 number 列...")
            cursor.execute('ALTER TABLE dmm_works ADD COLUMN number TEXT')

        if 'studio' not in columns:
            print("   ➕ 添加 studio 列...")
            cursor.execute('ALTER TABLE dmm_works ADD COLUMN studio TEXT')

        conn.commit()

        # 获取所有需要处理的记录
        cursor.execute('SELECT id, cid FROM dmm_works WHERE number IS NULL OR number = ""')
        records = cursor.fetchall()
        
        print(f"📊 找到 {len(records)} 条需要处理的记录")
        
        updated_count = 0
        
        for record_id, cid in records:
            # 生成番号
            number = generate_number_from_cid(cid)
            
            if number:
                # 从番号提取厂商前缀
                studio_match = re.match(r'^([A-Z]+)', number)
                studio = studio_match.group(1) if studio_match else None
                
                # 更新数据库
                cursor.execute('''
                    UPDATE dmm_works 
                    SET number = ?, studio = ? 
                    WHERE id = ?
                ''', (number, studio, record_id))
                
                updated_count += 1
                
                if updated_count % 100 == 0:
                    print(f"   ⏳ 已处理 {updated_count} 条记录...")
        
        conn.commit()
        conn.close()
        
        print(f"✅ 后处理完成，更新了 {updated_count} 条记录")
        
        # 显示处理结果
        show_sample_results(db_file)
        
    except Exception as e:
        print(f"❌ 后处理失败: {e}")

def show_sample_results(db_file: str):
    """显示处理结果样本"""
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        print(f"\n📋 处理结果样本:")
        print("-" * 60)
        
        cursor.execute('''
            SELECT cid, number, studio 
            FROM dmm_works 
            WHERE number IS NOT NULL 
            ORDER BY id 
            LIMIT 10
        ''')
        
        results = cursor.fetchall()
        
        for i, (cid, number, studio) in enumerate(results, 1):
            print(f"{i:2d}. CID: {cid:15s} → 番号: {number:12s} 厂商: {studio}")
        
        # 统计信息
        cursor.execute('SELECT COUNT(*) FROM dmm_works WHERE number IS NOT NULL')
        has_number = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM dmm_works')
        total = cursor.fetchone()[0]
        
        print(f"\n📊 处理统计:")
        print(f"   有番号的作品: {has_number}/{total} ({has_number/total*100:.1f}%)")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 显示结果失败: {e}")

if __name__ == "__main__":
    print("🔄 DMM数据后处理")
    print("=" * 50)
    
    post_process_database()
    
    print("\n✅ 后处理完成")
