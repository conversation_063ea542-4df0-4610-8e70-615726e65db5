#!/usr/bin/env python3
"""
智能验证工具 - 自动探测厂商的实际番号范围
解决MOND(275起)、EKDV(775起)等问题
"""
import sys
import json
import time
import random
from datetime import datetime
from typing import Dict, List, Tuple, Optional
sys.path.append('.')

class IntelligentVerifier:
    """智能验证器 - 自动探测番号范围"""
    
    def __init__(self):
        self.crawler = None
        self.verification_results = {}
        self.start_time = None
        self.request_count = 0
        self.request_times = []
        
        # 简化智能配置 - 针对MOND(275)、EKDV(775)等问题优化
        self.config = {
            # 速度控制 (可自由调整)
            'min_delay': 0.1,           # 最小延迟 - 可改为0.1加速
            'max_delay': 0.5,           # 最大延迟 - 可改为0.5加速
            'max_requests_per_minute': 100,  # 每分钟最大请求 - 可改为80加速
            'batch_size': 25,           # 批次大小
            'batch_delay': 15,          # 批次休息时间

            # 修复后的探测策略 - 确保覆盖MAAN(1091)
            'probe_points': [1, 50, 100, 200, 300, 500, 800, 1000, 1100, 1500],  # 确保包含1100
            'max_probe_tests': 10,      # 增加到10个点，确保能测试到1100
            'early_stop_on_success': True,  # 成功即停止
            'adaptive_probing': True,   # 自适应探测
            'early_stop': True,         # 成功即停止
        }
    
    def safe_delay(self):
        """安全延迟"""
        delay = random.uniform(self.config['min_delay'], self.config['max_delay'])
        time.sleep(delay)
        
        current_time = time.time()
        self.request_times.append(current_time)
        self.request_count += 1
        
        # 清理1分钟前的记录
        one_minute_ago = current_time - 60
        self.request_times = [t for t in self.request_times if t > one_minute_ago]
    
    def check_rate_limit(self):
        """频率检查"""
        current_time = time.time()
        one_minute_ago = current_time - 60
        self.request_times = [t for t in self.request_times if t > one_minute_ago]
        
        recent_requests = len(self.request_times)
        
        if recent_requests >= self.config['max_requests_per_minute']:
            wait_time = 60
            print(f"         ⚠️ 频率限制: {recent_requests}/{self.config['max_requests_per_minute']}, 等待{wait_time}秒")
            time.sleep(wait_time)
        
        return True
    
    def initialize_crawler(self):
        """初始化DMM爬虫"""
        print("🔧 初始化DMM爬虫...")
        
        try:
            from modules.dmm_search_crawler import DMMSearchCrawler
            
            self.crawler = DMMSearchCrawler()
            
            if not self.crawler.age_verified:
                print("❌ 年龄验证失败")
                return False
            
            print("✅ DMM爬虫初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 爬虫初始化失败: {e}")
            return False
    
    def load_database_mappings(self):
        """加载数据库映射"""
        print("📖 加载数据库映射...")
        
        try:
            with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            mappings = data['mappings']
            print(f"✅ 加载了 {len(mappings)} 个厂商映射")
            
            return mappings
            
        except Exception as e:
            print(f"❌ 加载映射数据失败: {e}")
            return None
    
    def test_single_number(self, prefix: str, number: int) -> bool:
        """测试单个番号"""
        try:
            self.check_rate_limit()
            
            test_cid = f"{prefix}{number:05d}"
            detail_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={test_cid}/"
            
            self.safe_delay()
            
            result = self.crawler._verify_cid_exists(detail_url, test_cid)
            
            status = "✅" if result else "❌"
            print(f"            {status} {test_cid}")
            
            return result
            
        except Exception as e:
            print(f"            ⚠️ {prefix}{number:05d} 异常: {e}")
            return False
    
    def universal_test_prefix(self, prefix: str) -> Dict:
        """通用智能测试前缀 - 适应任何起始番号"""
        result = {
            'prefix': prefix,
            'successful_numbers': [],
            'failed_numbers': [],
            'total_tests': 0,
            'success_count': 0,
            'is_valid': False,
            'confidence': 0.0,
            'strategy': 'universal_probe'
        }

        print(f"      🔍 通用探测前缀: {prefix}")
        print(f"         策略: 分段探测 + 快速验证")

        # 优化的自适应探测策略
        probe_points = self.config['probe_points'][:self.config['max_probe_tests']]

        # 优先测试最可能成功的点
        priority_points = [1, 50, 100]  # 大部分厂商在这个范围
        extended_points = [p for p in probe_points if p not in priority_points]

        print(f"         优先点: {priority_points}")
        print(f"         扩展点: {extended_points}")

        # 先测试优先点
        for test_num in priority_points:
            if result['total_tests'] >= self.config['max_probe_tests']:
                break

            success = self.test_single_number(prefix, test_num)
            result['total_tests'] += 1

            if success:
                result['successful_numbers'].append(test_num)
                result['success_count'] += 1
                print(f"            ✅ 发现有效番号: {test_num} (优先范围)")

                if self.config['early_stop_on_success']:
                    print(f"            🎯 早停：前缀有效")
                    break
            else:
                result['failed_numbers'].append(test_num)

        # 如果优先点都失败，测试扩展点
        if result['success_count'] == 0:
            print(f"            🔍 优先范围未发现，扩展搜索...")
            for test_num in extended_points:
                if result['total_tests'] >= self.config['max_probe_tests']:
                    break

                success = self.test_single_number(prefix, test_num)
                result['total_tests'] += 1

                if success:
                    result['successful_numbers'].append(test_num)
                    result['success_count'] += 1
                    print(f"            ✅ 发现有效番号: {test_num} (扩展范围)")

                    if self.config['early_stop_on_success']:
                        print(f"            🎯 扩展搜索成功")
                        break
                else:
                    result['failed_numbers'].append(test_num)
        
        # 判断前缀是否有效
        result['is_valid'] = result['success_count'] >= 1

        # 计算置信度
        if result['total_tests'] > 0:
            result['confidence'] = result['success_count'] / result['total_tests']
        elif result['success_count'] > 0:  # 早停情况
            result['confidence'] = 1.0

        print(f"      📊 测试结果: {prefix}")
        print(f"         测试数: {result['total_tests']}")
        print(f"         成功数: {result['success_count']}")
        print(f"         成功番号: {result['successful_numbers']}")
        print(f"         有效性: {'✅ 有效' if result['is_valid'] else '❌ 无效'}")
        print(f"         置信度: {result['confidence']:.2f}")

        return result
    
    def verify_studio_mapping(self, studio: str, mapping_data: Dict) -> Dict:
        """验证单个厂商的映射"""
        print(f"\n🧠 智能验证厂商: {studio}")
        
        result = {
            'studio': studio,
            'total_prefixes': 0,
            'valid_prefixes': [],
            'invalid_prefixes': [],
            'prefix_details': {},
            'success_rate': 0.0,
            'verification_time': 0,
            'status': 'unknown',
            'total_requests': 0,
            'verification_method': 'intelligent_range_probing'
        }
        
        try:
            start_time = time.time()
            
            # 获取映射信息
            primary = mapping_data.get('primary', '')
            alternatives = mapping_data.get('alternatives', [])
            
            all_prefixes = [primary] + alternatives if primary else alternatives
            result['total_prefixes'] = len(all_prefixes)
            
            print(f"   总前缀数: {len(all_prefixes)}")
            print(f"   通用验证策略:")
            print(f"      探测点: {self.config['probe_points']}")
            print(f"      最多探测: {self.config['max_probe_tests']} 个")
            print(f"      探测范围: 1-2000 (适应任何起始番号)")
            
            if not all_prefixes:
                result['status'] = 'no_mappings'
                return result
            
            request_count_start = self.request_count
            
            # 通用验证每个前缀
            for prefix in all_prefixes:
                prefix_result = self.universal_test_prefix(prefix)
                result['prefix_details'][prefix] = prefix_result
                
                if prefix_result['is_valid']:
                    result['valid_prefixes'].append(prefix)
                else:
                    result['invalid_prefixes'].append(prefix)
            
            result['total_requests'] = self.request_count - request_count_start
            
            # 计算成功率
            valid_count = len(result['valid_prefixes'])
            total_count = len(all_prefixes)
            result['success_rate'] = valid_count / total_count if total_count > 0 else 0
            
            # 确定状态
            if result['success_rate'] >= 0.8:
                result['status'] = 'excellent'
            elif result['success_rate'] >= 0.5:
                result['status'] = 'good'
            elif result['success_rate'] >= 0.2:
                result['status'] = 'poor'
            else:
                result['status'] = 'failed'
            
            result['verification_time'] = time.time() - start_time
            
            print(f"   📊 智能验证完成:")
            print(f"      有效前缀: {valid_count}/{total_count} ({result['success_rate']:.1%})")
            print(f"      总请求数: {result['total_requests']}")
            print(f"      验证状态: {result['status']}")
            
            return result
            
        except Exception as e:
            print(f"   ❌ 验证异常: {e}")
            result['status'] = 'error'
            result['error'] = str(e)
            return result
    
    def batch_verification(self, mappings: Dict, start_index: int = 0, limit: int = None) -> Dict:
        """批量智能验证"""
        print(f"\n🧠 开始智能批量验证...")
        
        self.start_time = time.time()
        studios = list(mappings.keys())[start_index:]
        
        if limit:
            studios = studios[:limit]
            print(f"⚠️ 限制验证数量: {limit} 个厂商")
        
        print(f"📋 计划验证 {len(studios)} 个厂商")
        print("🔍 修复优化智能验证特点:")
        print("   - 优先探测: [1, 50, 100] + 扩展探测: [200, 300, 500, 800, 1000, 1100, 1500]")
        print("   - 修复MAAN问题: 确保测试到1100覆盖1091起始番号")
        print("   - 自适应策略: 优先测试高成功率范围，失败时扩展到大范围")
        
        for i, studio in enumerate(studios, 1):
            print(f"\n{'='*70}")
            print(f"进度: {i}/{len(studios)} ({i/len(studios):.1%})")
            
            # 批次休息
            if i > 1 and (i - 1) % self.config['batch_size'] == 0:
                print(f"😴 批次休息 {self.config['batch_delay']} 秒...")
                time.sleep(self.config['batch_delay'])
            
            mapping_data = mappings[studio]
            result = self.verify_studio_mapping(studio, mapping_data)
            self.verification_results[studio] = result
            
            # 显示进度和统计
            elapsed = time.time() - self.start_time
            avg_time = elapsed / i
            remaining = (len(studios) - i) * avg_time
            
            print(f"⏱️ 已用时: {elapsed:.1f}s, 预计剩余: {remaining:.1f}s")
            print(f"📡 总请求数: {self.request_count}")
            print(f"📊 当前频率: {len(self.request_times)} 请求/分钟")
        
        return self.verification_results
    
    def generate_intelligent_report(self) -> Dict:
        """生成智能验证报告"""
        print(f"\n📊 生成智能验证报告...")
        
        if not self.verification_results:
            return {}
        
        total_studios = len(self.verification_results)
        total_time = time.time() - self.start_time if self.start_time else 0
        
        # 统计各种状态
        status_counts = {
            'excellent': 0, 'good': 0, 'poor': 0, 'failed': 0, 'error': 0, 'no_mappings': 0
        }
        
        total_prefixes = 0
        total_valid_prefixes = 0
        total_requests = 0
        
        # 范围发现统计
        range_discoveries = {}
        
        for studio, result in self.verification_results.items():
            status = result.get('status', 'unknown')
            if status in status_counts:
                status_counts[status] += 1
            
            total_prefixes += result.get('total_prefixes', 0)
            total_valid_prefixes += len(result.get('valid_prefixes', []))
            total_requests += result.get('total_requests', 0)
            
            # 统计发现的番号范围
            for prefix, details in result.get('prefix_details', {}).items():
                if details.get('successful_numbers'):
                    min_num = min(details['successful_numbers'])

                    range_key = f"{min_num//100*100}-{min_num//100*100+99}"
                    if range_key not in range_discoveries:
                        range_discoveries[range_key] = []
                    range_discoveries[range_key].append(f"{studio}:{prefix}")
        
        overall_success_rate = total_valid_prefixes / total_prefixes if total_prefixes > 0 else 0
        
        report = {
            'summary': {
                'total_studios': total_studios,
                'total_prefixes': total_prefixes,
                'total_valid_prefixes': total_valid_prefixes,
                'overall_success_rate': overall_success_rate,
                'verification_time': total_time,
                'total_requests': total_requests,
                'avg_requests_per_minute': (total_requests / (total_time / 60)) if total_time > 0 else 0,
                'timestamp': datetime.now().isoformat()
            },
            'status_distribution': status_counts,
            'range_discoveries': range_discoveries,
            'intelligent_config': self.config,
            'detailed_results': self.verification_results
        }
        
        return report
    
    def print_intelligent_report(self, report: Dict):
        """打印智能验证报告"""
        if not report:
            print("❌ 无报告数据")
            return
        
        summary = report['summary']
        status_dist = report['status_distribution']
        range_disc = report['range_discoveries']
        
        print(f"\n" + "="*80)
        print(f"📊 智能数据库映射验证报告")
        print(f"="*80)
        
        print(f"\n📈 总体统计:")
        print(f"   验证厂商: {summary['total_studios']} 个")
        print(f"   总前缀数: {summary['total_prefixes']} 个")
        print(f"   有效前缀: {summary['total_valid_prefixes']} 个")
        print(f"   总成功率: {summary['overall_success_rate']:.1%}")
        print(f"   验证耗时: {summary['verification_time']:.1f} 秒")
        print(f"   总请求数: {summary['total_requests']}")
        print(f"   平均频率: {summary['avg_requests_per_minute']:.1f} 请求/分钟")
        
        print(f"\n📋 厂商状态分布:")
        print(f"   🟢 优秀 (≥80%): {status_dist['excellent']} 个")
        print(f"   🟡 良好 (≥50%): {status_dist['good']} 个") 
        print(f"   🟠 较差 (≥20%): {status_dist['poor']} 个")
        print(f"   🔴 失败 (<20%): {status_dist['failed']} 个")
        print(f"   ❌ 异常: {status_dist['error']} 个")
        print(f"   ⚪ 无映射: {status_dist['no_mappings']} 个")
        
        if range_disc:
            print(f"\n🎯 发现的番号范围分布:")
            for range_key, studios in sorted(range_disc.items()):
                print(f"   {range_key}: {len(studios)} 个前缀")
                if len(studios) <= 3:
                    print(f"      {', '.join(studios)}")
        
        print(f"\n💡 智能验证优势:")
        print(f"   ✅ 自动探测：发现厂商的实际番号范围")
        print(f"   ✅ 分段采样：避免盲目测试小番号")
        print(f"   ✅ 智能早停：找到证据即停止")
        print(f"   ✅ 范围发现：统计不同厂商的番号分布规律")

def main():
    """主函数"""
    print("🧠 智能数据库映射验证工具")
    print("=" * 80)
    
    print("🔍 基于数据优化的智能验证特性:")
    print("   - 🎯 优先策略: 先测试[1,50,100]高成功率范围")
    print("   - 🔍 扩展搜索: 失败时自动扩展到[150,200,300,500,800,1000,1100,1500]")
    print("   - 📊 数据驱动: 基于历史验证报告优化探测点分布")
    print("   - ⚡ 高效验证: 为大规模数据库验证和爬取做准备")
    
    # 用户确认
    user_input = input("\n是否开始智能验证？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("验证已取消")
        return
    
    # 询问验证数量限制
    limit_input = input("验证数量限制 (建议10-20，回车=10): ").strip()
    limit = 10
    
    if limit_input.isdigit():
        limit = int(limit_input)
    
    print(f"🎯 将智能验证 {limit} 个厂商")
    
    # 执行验证
    verifier = IntelligentVerifier()
    
    # 1. 初始化爬虫
    if not verifier.initialize_crawler():
        print("❌ 爬虫初始化失败，验证终止")
        return
    
    # 2. 加载映射数据
    mappings = verifier.load_database_mappings()
    if not mappings:
        print("❌ 映射数据加载失败，验证终止")
        return
    
    # 3. 执行智能验证
    print("\n🧠 开始智能验证...")
    verifier.batch_verification(mappings, limit=limit)
    
    # 4. 生成报告
    report = verifier.generate_intelligent_report()
    
    # 5. 显示报告
    verifier.print_intelligent_report(report)
    
    # 6. 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"intelligent_verification_report_{timestamp}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细报告已保存: {report_file}")
    except Exception as e:
        print(f"\n❌ 报告保存失败: {e}")

if __name__ == "__main__":
    main()
