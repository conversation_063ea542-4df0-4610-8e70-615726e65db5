#!/usr/bin/env python3
"""
修正h_前缀映射问题
"""
import json
import os
from datetime import datetime

def check_and_fix_h_prefix_mappings():
    """检查并修正h_前缀映射"""
    print("🔧 检查并修正h_前缀映射...")
    
    # 需要h_前缀的厂商及其正确格式
    h_prefix_studios = {
        'MILK': 'h_1240milk',
        'SCPX': 'h_565scpx', 
        'SCOP': 'h_565scop',
        'NATR': 'h_067natr',
        'MUCH': 'h_796much'
    }
    
    try:
        # 加载映射数据
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        
        corrections_made = 0
        
        for studio, correct_primary in h_prefix_studios.items():
            if studio not in mappings:
                print(f"   ❌ {studio}: 未找到映射")
                continue
            
            current_info = mappings[studio]
            current_primary = current_info['primary']
            alternatives = current_info.get('alternatives', [])
            
            print(f"\n   🔍 检查 {studio}:")
            print(f"     当前主映射: {current_primary}")
            print(f"     备选映射: {alternatives}")
            print(f"     期望主映射: {correct_primary}")
            
            if current_primary == correct_primary:
                print(f"     ✅ 映射已正确")
                continue
            
            # 检查正确的映射是否在备选中
            if correct_primary in alternatives:
                print(f"     🔄 在备选中找到正确映射，进行调整...")
                
                # 将正确的映射设为主映射
                new_alternatives = [current_primary] + [alt for alt in alternatives if alt != correct_primary]
                
                mappings[studio] = {
                    'primary': correct_primary,
                    'alternatives': new_alternatives,
                    'count': len(new_alternatives) + 1,
                    'type': 'multiple'
                }
                
                print(f"     ✅ 已调整: {current_primary} -> {correct_primary}")
                print(f"     新备选映射: {new_alternatives[:3]}{'...' if len(new_alternatives) > 3 else ''}")
                corrections_made += 1
                
            else:
                print(f"     ⚠️ 在备选映射中未找到 {correct_primary}")
                print(f"     可能的原因:")
                print(f"       1. 原始数据中没有这个映射")
                print(f"       2. 映射格式不完全匹配")
                
                # 尝试模糊匹配
                fuzzy_matches = []
                for alt in alternatives:
                    if correct_primary.replace('h_', '').replace('0', '') in alt or alt in correct_primary:
                        fuzzy_matches.append(alt)
                
                if fuzzy_matches:
                    print(f"     🔍 找到可能的匹配: {fuzzy_matches}")
        
        if corrections_made > 0:
            # 备份原文件
            backup_file = f"studio_mappings_all.json.backup.h_prefix.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
                backup_data = f.read()
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(backup_data)
            print(f"\n   📦 原文件已备份为: {backup_file}")
            
            # 保存修正后的数据
            with open('studio_mappings_all.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 已修正 {corrections_made} 个h_前缀映射")
            return True
        else:
            print(f"\n   ✅ 没有需要修正的h_前缀映射")
            return False
            
    except Exception as e:
        print(f"❌ 修正失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_detailed_mapping_info():
    """显示详细的映射信息"""
    print("\n📋 详细映射信息:")
    
    studios_to_check = ['MILK', 'SCPX', 'SCOP', 'NATR', 'MUCH']
    
    try:
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        mappings = data.get('mappings', {})
        
        for studio in studios_to_check:
            if studio in mappings:
                info = mappings[studio]
                print(f"\n   {studio}:")
                print(f"     类型: {info['type']}")
                print(f"     主映射: {info['primary']}")
                print(f"     映射数量: {info['count']}")
                if info.get('alternatives'):
                    print(f"     所有备选映射:")
                    for i, alt in enumerate(info['alternatives'], 1):
                        print(f"       {i}. {alt}")
            else:
                print(f"\n   {studio}: 未找到")
                
    except Exception as e:
        print(f"❌ 显示信息失败: {e}")

def update_database_and_test():
    """更新数据库并测试"""
    print("\n🔄 更新数据库并测试...")
    
    try:
        import sys
        sys.path.append('.')
        from modules.mapping_manager import MappingManager
        
        # 重新创建映射管理器
        manager = MappingManager(
            config_file="studio_mappings_all.json",
            db_file="mmp/fast_dmm.db",
            enable_db_sync=True
        )
        
        # 测试修正后的映射
        test_studios = ['MILK', 'SCPX', 'SCOP', 'NATR', 'MUCH']
        
        print("   🔍 验证修正后的映射:")
        for studio in test_studios:
            info = manager.get_mapping_info(studio)
            if info['has_mapping']:
                print(f"     {studio}: {info['primary']} ({info['type']})")
                if info['alternatives']:
                    print(f"       备选: {', '.join(info['alternatives'][:2])}{'...' if len(info['alternatives']) > 2 else ''}")
            else:
                print(f"     {studio}: 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库更新失败: {e}")
        return False

def test_search_examples():
    """测试搜索示例"""
    print("\n🧪 测试搜索示例...")
    
    test_cases = [
        ('MILK-251', 'h_1240milk00251'),
        ('SCPX-123', 'h_565scpx00123'),
        ('MUCH-456', 'h_796much00456')
    ]
    
    try:
        import sys
        sys.path.append('.')
        from modules.fast_dmm_search import FastDMMSearch
        
        search_engine = FastDMMSearch(db_file="mmp/fast_dmm.db")
        
        for code, expected_cid in test_cases:
            print(f"\n   测试: {code}")
            result = search_engine.intelligent_search(code)
            
            if result['success']:
                if result['is_multiple']:
                    print(f"     找到 {result['count']} 个映射:")
                    primary_result = None
                    for res in result['results']:
                        if hasattr(res, 'mapping_type') and res.mapping_type == 'primary':
                            primary_result = res
                            break
                    
                    if primary_result:
                        print(f"     主要结果: {primary_result.dmm_cid}")
                        if primary_result.dmm_cid == expected_cid:
                            print("     ✅ 映射正确！")
                        else:
                            print(f"     ⚠️ 期望: {expected_cid}")
                else:
                    if result['results']:
                        res = result['results'][0]
                        print(f"     结果: {res.dmm_cid}")
                        if res.dmm_cid == expected_cid:
                            print("     ✅ 映射正确！")
                        else:
                            print(f"     ⚠️ 期望: {expected_cid}")
            else:
                print(f"     ❌ 搜索失败: {result['message']}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🛠️ h_前缀映射修正工具")
    print("=" * 50)
    
    # 显示当前映射信息
    show_detailed_mapping_info()
    
    # 修正h_前缀映射
    fixed = check_and_fix_h_prefix_mappings()
    
    if fixed:
        # 更新数据库
        update_database_and_test()
        
        # 测试搜索
        test_search_examples()
    
    print("\n" + "=" * 50)
    if fixed:
        print("🎉 h_前缀映射修正完成！")
        print("\n📋 下一步:")
        print("1. 运行 python3 test_fast_dmm_updated.py 验证")
        print("2. 继续系统集成")
    else:
        print("✅ h_前缀映射无需修正")

if __name__ == "__main__":
    main()
