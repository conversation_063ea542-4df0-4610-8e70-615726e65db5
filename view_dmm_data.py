#!/usr/bin/env python3
"""
查看DMM爬虫数据
"""
import sqlite3
import json
from datetime import datetime

def view_dmm_data(db_file: str = "dmm_firefox_database.db"):
    """查看DMM数据库内容"""
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 获取总体统计
        print("🗄️ DMM数据库统计")
        print("=" * 60)
        
        cursor.execute('SELECT COUNT(*) FROM dmm_works')
        total_works = cursor.fetchone()[0]
        print(f"📊 总作品数: {total_works}")
        
        cursor.execute('SELECT COUNT(DISTINCT page_number) FROM dmm_works')
        total_pages = cursor.fetchone()[0]
        print(f"📄 已爬取页数: {total_pages}")
        
        cursor.execute('SELECT MIN(crawl_time), MAX(crawl_time) FROM dmm_works')
        time_range = cursor.fetchone()
        print(f"⏰ 爬取时间范围: {time_range[0]} 到 {time_range[1]}")
        
        # 查看前10个作品的详细信息
        print(f"\n📋 前10个作品详情:")
        print("-" * 60)
        
        cursor.execute('''
            SELECT cid, title, number, studio, detail_url, page_number,
                   actress, duration, maker, release_date, rating, detail_crawled
            FROM dmm_works
            ORDER BY id
            LIMIT 10
        ''')

        works = cursor.fetchall()

        for i, work in enumerate(works, 1):
            cid, title, number, studio, detail_url, page_number, actress, duration, maker, release_date, rating, detail_crawled = work
            print(f"\n{i:2d}. CID: {cid}")
            print(f"    标题: {title or '未提取'}")
            print(f"    番号: {number or '未提取'}")
            print(f"    厂商: {studio or '未提取'}")

            # 显示详细信息（如果已爬取）
            if detail_crawled == 1:
                print(f"    🎬 演员: {actress or '未知'}")
                print(f"    ⏰ 时长: {duration or '未知'}")
                print(f"    🏢 制作商: {maker or '未知'}")
                print(f"    📅 发布: {release_date or '未知'}")
                print(f"    ⭐ 评分: {rating or '未知'}")
            else:
                print(f"    📊 详细信息: 未爬取")

            print(f"    🔗 详情页: {detail_url}")
            print(f"    📄 来源页: 第{page_number}页")
        
        # 按页面统计
        print(f"\n📊 各页面作品数统计:")
        print("-" * 60)
        
        cursor.execute('''
            SELECT page_number, COUNT(*) as count
            FROM dmm_works 
            GROUP BY page_number 
            ORDER BY page_number
            LIMIT 10
        ''')
        
        page_stats = cursor.fetchall()
        for page_num, count in page_stats:
            print(f"第{page_num:3d}页: {count:3d}个作品")
        
        # CID格式分析
        print(f"\n🔍 CID格式分析:")
        print("-" * 60)
        
        cursor.execute('''
            SELECT cid FROM dmm_works 
            ORDER BY id 
            LIMIT 20
        ''')
        
        cids = [row[0] for row in cursor.fetchall()]
        
        # 分析CID模式
        patterns = {}
        for cid in cids:
            # 提取字母前缀
            import re
            match = re.match(r'^([a-zA-Z]+)', cid)
            if match:
                prefix = match.group(1).upper()
                patterns[prefix] = patterns.get(prefix, 0) + 1
        
        print("CID前缀分布:")
        for prefix, count in sorted(patterns.items()):
            print(f"  {prefix}: {count}个")
        
        print(f"\n📝 示例CID:")
        for cid in cids[:10]:
            print(f"  {cid}")
        
        # 检查数据质量
        print(f"\n🔍 数据质量检查:")
        print("-" * 60)
        
        cursor.execute('SELECT COUNT(*) FROM dmm_works WHERE title IS NOT NULL AND title != ""')
        has_title = cursor.fetchone()[0]
        print(f"有标题的作品: {has_title}/{total_works} ({has_title/total_works*100:.1f}%)")
        
        cursor.execute('SELECT COUNT(*) FROM dmm_works WHERE number IS NOT NULL AND number != ""')
        has_number = cursor.fetchone()[0]
        print(f"有番号的作品: {has_number}/{total_works} ({has_number/total_works*100:.1f}%)")
        
        cursor.execute('SELECT COUNT(*) FROM dmm_works WHERE studio IS NOT NULL AND studio != ""')
        has_studio = cursor.fetchone()[0]
        print(f"有厂商的作品: {has_studio}/{total_works} ({has_studio/total_works*100:.1f}%)")

        # 详细信息统计
        cursor.execute('SELECT COUNT(*) FROM dmm_works WHERE detail_crawled = 1')
        has_details = cursor.fetchone()[0]

        if has_details > 0:
            print(f"\n📊 详细信息统计:")

            cursor.execute('SELECT COUNT(*) FROM dmm_works WHERE actress IS NOT NULL AND actress != ""')
            has_actress = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM dmm_works WHERE duration IS NOT NULL AND duration != ""')
            has_duration = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM dmm_works WHERE maker IS NOT NULL AND maker != ""')
            has_maker = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM dmm_works WHERE rating IS NOT NULL AND rating != ""')
            has_rating = cursor.fetchone()[0]

            print(f"已爬取详细信息: {has_details}/{total_works} ({has_details/total_works*100:.1f}%)")
            print(f"有演员信息: {has_actress}/{total_works} ({has_actress/total_works*100:.1f}%)")
            print(f"有时长信息: {has_duration}/{total_works} ({has_duration/total_works*100:.1f}%)")
            print(f"有制作商信息: {has_maker}/{total_works} ({has_maker/total_works*100:.1f}%)")
            print(f"有评分信息: {has_rating}/{total_works} ({has_rating/total_works*100:.1f}%)")

        conn.close()
        
    except Exception as e:
        print(f"❌ 查看数据失败: {e}")

def export_sample_data(db_file: str = "dmm_firefox_database.db", output_file: str = "dmm_sample.json"):
    """导出样本数据到JSON文件"""
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT cid, title, number, studio, detail_url, page_number, crawl_time,
                   actress, duration, maker, release_date, rating, detail_crawled
            FROM dmm_works
            ORDER BY id
            LIMIT 50
        ''')
        
        works = cursor.fetchall()
        
        sample_data = []
        for work in works:
            sample_data.append({
                'cid': work[0],
                'title': work[1],
                'number': work[2],
                'studio': work[3],
                'detail_url': work[4],
                'page_number': work[5],
                'crawl_time': work[6],
                'actress': work[7],
                'duration': work[8],
                'maker': work[9],
                'release_date': work[10],
                'rating': work[11],
                'detail_crawled': work[12]
            })
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 样本数据已导出到: {output_file}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 导出数据失败: {e}")

def analyze_url_patterns(db_file: str = "dmm_firefox_database.db"):
    """分析URL模式"""
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        cursor.execute('SELECT detail_url FROM dmm_works LIMIT 20')
        urls = [row[0] for row in cursor.fetchall()]
        
        print(f"\n🔗 URL模式分析:")
        print("-" * 60)
        
        for i, url in enumerate(urls, 1):
            print(f"{i:2d}. {url}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析URL失败: {e}")

if __name__ == "__main__":
    print("🔍 DMM数据查看器")
    print("=" * 60)
    
    # 查看数据
    view_dmm_data()
    
    # 分析URL模式
    analyze_url_patterns()
    
    # 导出样本数据
    export_sample_data()
    
    print(f"\n✅ 数据查看完成")
