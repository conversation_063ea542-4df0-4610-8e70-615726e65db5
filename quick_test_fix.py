#!/usr/bin/env python3
"""
快速测试修复
"""
import sys
import os

# 添加项目路径
sys.path.append('.')

def test_searchresult_creation():
    """测试SearchResult创建"""
    print("🧪 测试SearchResult创建...")
    
    try:
        from modules.fast_dmm_database import SearchResult
        
        # 测试创建SearchResult
        result = SearchResult(
            code="MOND-123",
            dmm_cid="mond00123",
            url="https://www.dmm.co.jp/digital/videoa/-/detail/=/cid=mond00123/",
            confidence=0.95,
            source="multi_mapping_primary",
            studio="MOND"
        )
        
        # 添加额外属性
        result.mapping_type = 'primary'
        result.mapping_priority = 0
        result.number = 123
        
        print(f"   ✅ SearchResult创建成功:")
        print(f"      Code: {result.code}")
        print(f"      CID: {result.dmm_cid}")
        print(f"      映射类型: {result.mapping_type}")
        print(f"      Number: {result.number}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ SearchResult创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_intelligent_search_simple():
    """简单测试智能搜索"""
    print("\n🔍 简单测试智能搜索...")
    
    try:
        from modules.fast_dmm_search import FastDMMSearch
        
        # 创建搜索引擎
        search_engine = FastDMMSearch(db_file="mmp/fast_dmm.db")
        
        # 测试一个多重映射番号
        code = "MOND-123"
        print(f"   测试番号: {code}")
        
        result = search_engine.intelligent_search(code)
        
        print(f"   搜索结果: {result['search_type']}")
        print(f"   成功: {result['success']}")
        print(f"   消息: {result['message']}")
        
        if result['success'] and result['is_multiple']:
            print(f"   找到 {result['count']} 个映射:")
            for i, res in enumerate(result['results'][:2]):  # 只显示前2个
                print(f"     {i+1}. {res.dmm_cid} ({res.mapping_type})")
        
        return result['success']
        
    except Exception as e:
        print(f"   ❌ 智能搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 快速修复测试")
    print("=" * 40)
    
    # 测试SearchResult创建
    result1 = test_searchresult_creation()
    
    # 测试智能搜索
    result2 = test_intelligent_search_simple()
    
    print("\n" + "=" * 40)
    if result1 and result2:
        print("✅ 修复成功！可以继续下一步")
    else:
        print("❌ 仍有问题需要解决")

if __name__ == "__main__":
    main()
