#!/usr/bin/env python3
"""
多重映射验证和去重系统
"""
import requests
import time
import re
from typing import Dict, List, Set, Tuple
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import sqlite3
import json
from datetime import datetime

class MultiMappingValidator:
    """多重映射验证器"""
    
    def __init__(self, db_file: str = "mmp/fast_dmm.db"):
        self.db_file = db_file
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.rate_limit_delay = 2.0  # 2秒间隔
        
    def validate_and_deduplicate_mappings(self, studio: str, number: int, mappings: List[str]) -> Dict:
        """验证并去重多重映射"""
        print(f"🔍 验证多重映射: {studio}-{number}")
        print(f"   候选映射: {mappings}")
        
        results = {
            "studio": studio,
            "number": number,
            "original_mappings": mappings.copy(),
            "valid_mappings": [],
            "invalid_mappings": [],
            "duplicate_mappings": [],
            "discovered_mappings": [],
            "verification_results": {}
        }
        
        # 1. 验证每个映射的有效性
        valid_cids = set()
        
        for mapping in mappings:
            padded_number = str(number).zfill(5)
            cid = f"{mapping}{padded_number}"
            
            print(f"   🔍 验证CID: {cid}")
            
            # 检查CID是否真实存在
            is_valid = self._verify_cid_exists(cid)
            
            if is_valid:
                if cid not in valid_cids:
                    results["valid_mappings"].append(mapping)
                    valid_cids.add(cid)
                    results["verification_results"][mapping] = {
                        "status": "valid",
                        "cid": cid,
                        "url": f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/"
                    }
                    print(f"      ✅ 有效: {cid}")
                else:
                    results["duplicate_mappings"].append(mapping)
                    results["verification_results"][mapping] = {
                        "status": "duplicate",
                        "cid": cid
                    }
                    print(f"      🔄 重复: {cid}")
            else:
                results["invalid_mappings"].append(mapping)
                results["verification_results"][mapping] = {
                    "status": "invalid",
                    "cid": cid
                }
                print(f"      ❌ 无效: {cid}")
            
            # 限制请求频率
            time.sleep(self.rate_limit_delay)
        
        # 2. 通过DMM搜索页面发现额外的映射
        discovered = self._discover_mappings_from_dmm_search(studio, number)
        
        for discovered_mapping in discovered:
            if discovered_mapping not in mappings:
                results["discovered_mappings"].append(discovered_mapping)
                print(f"   🆕 发现新映射: {discovered_mapping}")
        
        print(f"✅ 验证完成:")
        print(f"   有效映射: {len(results['valid_mappings'])}")
        print(f"   无效映射: {len(results['invalid_mappings'])}")
        print(f"   重复映射: {len(results['duplicate_mappings'])}")
        print(f"   发现新映射: {len(results['discovered_mappings'])}")
        
        return results
    
    def _verify_cid_exists(self, cid: str) -> bool:
        """验证CID是否真实存在"""
        try:
            url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/"
            response = self.session.head(url, timeout=10)
            
            # 200表示页面存在，404表示不存在
            return response.status_code == 200
            
        except Exception as e:
            print(f"      ⚠️ 验证异常: {e}")
            return False
    
    def _discover_mappings_from_dmm_search(self, studio: str, number: int) -> List[str]:
        """从DMM搜索页面发现映射"""
        try:
            # 构建搜索关键词
            search_key = f"{studio}{number:05d}"  # 如 ID00021
            search_url = f"https://video.dmm.co.jp/av/list/?key={search_key}"
            
            print(f"   🔍 搜索DMM页面: {search_url}")
            
            response = self.session.get(search_url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找所有作品链接
            discovered_mappings = set()
            
            # 查找包含CID的链接
            cid_links = soup.find_all('a', href=re.compile(r'/cid=([^/]+)/'))
            
            for link in cid_links:
                href = link.get('href', '')
                cid_match = re.search(r'/cid=([^/]+)/', href)
                
                if cid_match:
                    found_cid = cid_match.group(1)
                    
                    # 尝试从CID中提取前缀
                    prefix = self._extract_prefix_from_cid(found_cid, studio, number)
                    
                    if prefix:
                        discovered_mappings.add(prefix)
                        print(f"      🆕 发现CID: {found_cid} -> 前缀: {prefix}")
            
            return list(discovered_mappings)
            
        except Exception as e:
            print(f"   ❌ DMM搜索页面解析失败: {e}")
            return []
    
    def _extract_prefix_from_cid(self, cid: str, studio: str, number: int) -> str:
        """从CID中提取前缀"""
        try:
            # 移除数字部分，提取前缀
            number_str = f"{number:05d}"
            
            # 尝试不同的模式
            patterns = [
                rf'^(.+?){number_str}$',  # 简单前缀
                rf'^(.+?){number:03d}$',  # 3位数字
                rf'^(.+?){number:04d}$',  # 4位数字
                rf'^(.+?){number:02d}$',  # 2位数字
                rf'^(.+?){number}$',      # 原始数字
            ]
            
            for pattern in patterns:
                match = re.match(pattern, cid, re.IGNORECASE)
                if match:
                    prefix = match.group(1)
                    
                    # 验证前缀是否合理
                    if self._is_valid_prefix(prefix, studio):
                        return prefix
            
            return None
            
        except Exception as e:
            print(f"      ⚠️ 前缀提取失败: {e}")
            return None
    
    def _is_valid_prefix(self, prefix: str, studio: str) -> bool:
        """验证前缀是否合理"""
        # 基本长度检查
        if len(prefix) < 2 or len(prefix) > 20:
            return False
        
        # 检查是否包含厂商名（不区分大小写）
        studio_lower = studio.lower()
        prefix_lower = prefix.lower()
        
        # 如果前缀包含厂商名，很可能是有效的
        if studio_lower in prefix_lower:
            return True
        
        # 检查常见的前缀模式
        common_patterns = [
            r'^\d+[a-z]+$',      # 数字+字母 (如 433neo)
            r'^h_\d+[a-z]+$',    # h_前缀 (如 h_1240milk)
            r'^[a-z]+$',         # 纯字母 (如 ssis)
            r'^[a-z]+[a-z]+$',   # 复合字母 (如 mbddneo)
        ]
        
        for pattern in common_patterns:
            if re.match(pattern, prefix_lower):
                return True
        
        return False
    
    def update_mapping_database(self, validation_result: Dict) -> Dict:
        """更新映射数据库"""
        try:
            studio = validation_result["studio"]
            valid_mappings = validation_result["valid_mappings"]
            discovered_mappings = validation_result["discovered_mappings"]
            
            # 合并有效映射和发现的映射
            all_valid_mappings = list(set(valid_mappings + discovered_mappings))
            
            if not all_valid_mappings:
                return {"success": False, "message": "没有有效的映射可更新"}
            
            # 更新JSON配置文件
            config_result = self._update_json_config(studio, all_valid_mappings)
            
            # 更新数据库
            db_result = self._update_database(studio, all_valid_mappings, validation_result)
            
            return {
                "success": True,
                "message": f"映射数据库更新成功",
                "updated_mappings": all_valid_mappings,
                "config_update": config_result,
                "db_update": db_result
            }
            
        except Exception as e:
            return {"success": False, "message": f"更新映射数据库失败: {e}"}
    
    def _update_json_config(self, studio: str, mappings: List[str]) -> Dict:
        """更新JSON配置文件"""
        try:
            config_file = "studio_mappings_all.json"
            
            # 加载现有配置
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 更新映射
            if mappings:
                primary_mapping = mappings[0]
                alternative_mappings = mappings[1:] if len(mappings) > 1 else []
                
                mapping_info = {
                    "primary": primary_mapping,
                    "alternatives": alternative_mappings,
                    "count": len(mappings),
                    "type": "multiple" if len(mappings) > 1 else "single"
                }
                
                data["mappings"][studio] = mapping_info
                
                # 更新分类映射
                if mapping_info["type"] == "single":
                    data["single_mappings"][studio] = mapping_info
                    if studio in data.get("multi_mappings", {}):
                        del data["multi_mappings"][studio]
                else:
                    data["multi_mappings"][studio] = mapping_info
                    if studio in data.get("single_mappings", {}):
                        del data["single_mappings"][studio]
            
            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            return {"success": True, "message": "JSON配置更新成功"}
            
        except Exception as e:
            return {"success": False, "message": f"JSON配置更新失败: {e}"}
    
    def _update_database(self, studio: str, mappings: List[str], validation_result: Dict) -> Dict:
        """更新数据库"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 为每个有效映射添加索引记录
            added_records = 0
            
            for mapping in mappings:
                verification = validation_result["verification_results"].get(mapping, {})
                
                if verification.get("status") == "valid":
                    cid = verification["cid"]
                    url = verification["url"]
                    
                    # 检查是否已存在
                    cursor.execute('SELECT COUNT(*) FROM video_index WHERE dmm_cid = ?', (cid,))
                    
                    if cursor.fetchone()[0] == 0:
                        # 添加新记录
                        cursor.execute('''
                            INSERT INTO video_index 
                            (original_code, dmm_cid, url, confidence, source, studio, verified)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            f"{studio}-{validation_result['number']}",
                            cid,
                            url,
                            0.9,  # 高置信度，因为已验证
                            "multi_mapping_validated",
                            studio,
                            True
                        ))
                        
                        added_records += 1
            
            conn.commit()
            conn.close()
            
            return {
                "success": True,
                "message": f"数据库更新成功，添加 {added_records} 条记录"
            }
            
        except Exception as e:
            return {"success": False, "message": f"数据库更新失败: {e}"}

def main():
    """测试函数"""
    validator = MultiMappingValidator()
    
    # 测试ID厂商的映射验证
    test_mappings = ["5531id", "h_1234id", "mbddid", "433id", "1000id"]
    
    result = validator.validate_and_deduplicate_mappings("ID", 21, test_mappings)
    
    print("\n" + "="*60)
    print("验证结果:")
    print(f"有效映射: {result['valid_mappings']}")
    print(f"发现新映射: {result['discovered_mappings']}")
    
    # 更新数据库
    update_result = validator.update_mapping_database(result)
    print(f"数据库更新: {update_result}")

if __name__ == "__main__":
    main()
