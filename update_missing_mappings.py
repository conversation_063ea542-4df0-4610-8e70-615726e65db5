#!/usr/bin/env python3
"""
更新缺失的映射数据
基于新爬虫发现的结果，补充数据库中缺失的映射
"""
import sys
import json
import os
from datetime import datetime
sys.path.append('.')

def backup_current_mappings():
    """备份当前的映射文件"""
    print("📦 备份当前映射文件...")
    
    mapping_file = "studio_mappings_all.json"
    if os.path.exists(mapping_file):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"{mapping_file}.backup.{timestamp}"
        
        try:
            import shutil
            shutil.copy2(mapping_file, backup_file)
            print(f"   ✅ 备份完成: {backup_file}")
            return backup_file
        except Exception as e:
            print(f"   ❌ 备份失败: {e}")
            return None
    else:
        print(f"   ⚠️ 映射文件不存在: {mapping_file}")
        return None

def load_current_mappings():
    """加载当前的映射数据"""
    print("📖 加载当前映射数据...")
    
    mapping_file = "studio_mappings_all.json"
    try:
        with open(mapping_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"   ✅ 映射文件加载成功")
        return data
    except Exception as e:
        print(f"   ❌ 映射文件加载失败: {e}")
        return None

def update_id_mappings(data):
    """更新ID厂商的映射数据"""
    print("🔧 更新ID厂商映射...")
    
    if not data or 'mappings' not in data:
        print("   ❌ 映射数据格式错误")
        return False
    
    # 新爬虫发现的完整映射
    new_crawler_prefixes = [
        "5533id", "5532id", "5531id", "5530id", "5529id", "h_113id", 
        "5526id", "5524id", "5525id", "5522id", "5521id", "5519id"
    ]
    
    # 当前数据库中的映射
    current_id_mapping = data['mappings'].get('ID', {})
    current_primary = current_id_mapping.get('primary', '')
    current_alternatives = current_id_mapping.get('alternatives', [])
    
    print(f"   当前主映射: {current_primary}")
    print(f"   当前备选映射: {len(current_alternatives)} 个")
    
    # 找出缺失的映射
    all_current = [current_primary] + current_alternatives
    missing_prefixes = [prefix for prefix in new_crawler_prefixes if prefix not in all_current]
    
    print(f"   发现缺失映射: {len(missing_prefixes)} 个")
    for prefix in missing_prefixes:
        print(f"      - {prefix}")
    
    if missing_prefixes:
        # 更新映射数据
        updated_alternatives = current_alternatives + missing_prefixes
        
        # 按数字排序（保持一致性）
        def extract_number(prefix):
            if prefix.startswith('h_'):
                return 999  # h_前缀排在最后
            elif prefix.startswith('55'):
                return int(prefix[2:4])  # 提取中间数字
            else:
                return 0
        
        updated_alternatives.sort(key=extract_number, reverse=True)
        
        # 更新数据
        data['mappings']['ID'] = {
            'primary': current_primary,
            'alternatives': updated_alternatives,
            'count': len(updated_alternatives) + 1,
            'last_updated': datetime.now().isoformat(),
            'update_source': 'new_dmm_crawler_discovery'
        }
        
        print(f"   ✅ 映射已更新:")
        print(f"      主映射: {current_primary}")
        print(f"      备选映射: {len(updated_alternatives)} 个")
        print(f"      总计: {len(updated_alternatives) + 1} 个")
        
        return True
    else:
        print(f"   ✅ 映射数据已是最新，无需更新")
        return False

def save_updated_mappings(data):
    """保存更新后的映射数据"""
    print("💾 保存更新后的映射数据...")
    
    mapping_file = "studio_mappings_all.json"
    try:
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 映射文件已更新: {mapping_file}")
        return True
    except Exception as e:
        print(f"   ❌ 映射文件保存失败: {e}")
        return False

def verify_update():
    """验证更新结果"""
    print("🔍 验证更新结果...")
    
    try:
        # 重新加载数据验证
        with open("studio_mappings_all.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        id_mapping = data['mappings'].get('ID', {})
        primary = id_mapping.get('primary', '')
        alternatives = id_mapping.get('alternatives', [])
        total_count = len(alternatives) + (1 if primary else 0)
        
        print(f"   验证结果:")
        print(f"      主映射: {primary}")
        print(f"      备选映射: {len(alternatives)} 个")
        print(f"      总计: {total_count} 个")
        
        # 检查是否包含新发现的映射
        missing_prefixes = ["5525id", "5522id", "5521id", "5519id"]
        found_missing = [prefix for prefix in missing_prefixes if prefix in alternatives]
        
        print(f"   新发现的映射:")
        for prefix in missing_prefixes:
            status = "✅" if prefix in alternatives else "❌"
            print(f"      {status} {prefix}")
        
        if len(found_missing) == len(missing_prefixes):
            print(f"   🎉 所有缺失映射已成功添加！")
            return True
        else:
            print(f"   ⚠️ 部分映射可能未正确添加")
            return False
            
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")
        return False

def reload_system_cache():
    """重新加载系统缓存"""
    print("🔄 重新加载系统缓存...")
    
    try:
        from modules.search_detail import SearchDetailModule
        
        # 创建新的搜索引擎实例（强制重新加载）
        search_engine = SearchDetailModule()
        
        # 测试更新后的映射
        result = search_engine.search_dmm_enhanced("ID-021")
        
        if isinstance(result, dict) and 'results' in result:
            result_count = len(result['results'])
            print(f"   ✅ 系统重新加载成功")
            print(f"   📊 ID-021 现在返回: {result_count} 个映射")
            
            if result_count == 12:
                print(f"   🎉 完美！现在返回完整的12个映射！")
                return True
            elif result_count > 8:
                print(f"   ✅ 改进！映射数量从8个增加到{result_count}个")
                return True
            else:
                print(f"   ⚠️ 映射数量仍为{result_count}个，可能需要重启系统")
                return False
        else:
            print(f"   ⚠️ 搜索结果格式异常")
            return False
            
    except Exception as e:
        print(f"   ❌ 系统重新加载失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ 映射数据更新工具")
    print("=" * 50)
    
    print("💡 更新目标:")
    print("   - 将新爬虫发现的4个缺失映射添加到数据库")
    print("   - 5525id, 5522id, 5521id, 5519id")
    print("   - 使ID-021搜索返回完整的12个映射")
    
    user_input = input("\n是否继续更新映射数据？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("更新已取消")
        return
    
    # 执行更新流程
    steps = [
        ("备份当前映射", backup_current_mappings),
        ("加载当前映射", load_current_mappings),
    ]
    
    backup_file = None
    current_data = None
    
    # 1. 备份和加载
    backup_file = backup_current_mappings()
    current_data = load_current_mappings()
    
    if not current_data:
        print("❌ 无法加载当前映射数据，更新终止")
        return
    
    # 2. 更新映射
    if update_id_mappings(current_data):
        # 3. 保存更新
        if save_updated_mappings(current_data):
            # 4. 验证更新
            if verify_update():
                # 5. 重新加载系统
                reload_success = reload_system_cache()
                
                print("\n" + "=" * 50)
                print("📊 更新总结:")
                
                if reload_success:
                    print("🎉 映射更新完全成功！")
                    print("\n✅ 完成的工作:")
                    print("   - 备份了原始映射文件")
                    print("   - 添加了4个缺失的映射")
                    print("   - 更新了映射总数")
                    print("   - 重新加载了系统缓存")
                    print("   - ID-021现在返回完整的12个映射")
                    
                    print("\n🎯 现在可以:")
                    print("   - 重新运行搜索测试")
                    print("   - 验证12个映射全部可用")
                    print("   - 享受完整的搜索功能")
                else:
                    print("✅ 映射文件更新成功，但系统缓存可能需要重启")
                    print("   建议重启应用程序以加载新映射")
            else:
                print("❌ 更新验证失败")
        else:
            print("❌ 映射文件保存失败")
    else:
        print("💡 映射数据已是最新，无需更新")

if __name__ == "__main__":
    main()
