#!/usr/bin/env python3
"""
清理FastDMM缓存并重建索引
"""
import sqlite3
import os
import sys

def clear_fastdmm_cache():
    """清理FastDMM缓存"""
    print("🧹 清理FastDMM缓存...")
    
    db_file = "mmp/fast_dmm.db"
    
    if not os.path.exists(db_file):
        print(f"   ❌ 数据库文件不存在: {db_file}")
        return False
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 查看现有的表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"   发现数据库表: {', '.join(tables)}")
        
        # 清理可能的缓存表
        cache_tables = []
        for table in tables:
            if any(keyword in table.lower() for keyword in ['cache', 'index', 'search']):
                cache_tables.append(table)
        
        if cache_tables:
            print(f"   发现缓存相关表: {', '.join(cache_tables)}")
            
            for table in cache_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"     {table}: {count} 条记录")
                
                # 清空表
                cursor.execute(f"DELETE FROM {table}")
                print(f"     ✅ 已清空 {table}")
        
        # 检查是否有SSIS相关的缓存记录
        print("\n   🔍 检查SSIS相关缓存...")
        for table in tables:
            try:
                cursor.execute(f"SELECT * FROM {table} WHERE code LIKE '%SSIS%' OR dmm_cid LIKE '%ssis%' LIMIT 5")
                ssis_records = cursor.fetchall()
                if ssis_records:
                    print(f"     在 {table} 中找到 {len(ssis_records)} 条SSIS记录")
                    for record in ssis_records:
                        print(f"       {record}")
                    
                    # 删除SSIS相关记录
                    cursor.execute(f"DELETE FROM {table} WHERE code LIKE '%SSIS%' OR dmm_cid LIKE '%ssis%'")
                    deleted = cursor.rowcount
                    if deleted > 0:
                        print(f"     ✅ 删除了 {deleted} 条SSIS记录")
            except Exception as e:
                # 某些表可能没有这些列，跳过
                continue
        
        conn.commit()
        conn.close()
        
        print("   ✅ FastDMM缓存清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 缓存清理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def rebuild_mapping_manager():
    """重建映射管理器"""
    print("\n🔄 重建映射管理器...")
    
    try:
        sys.path.append('.')
        from modules.mapping_manager import MappingManager
        
        # 重新创建映射管理器，强制重新同步
        manager = MappingManager(
            config_file="studio_mappings_all.json",
            db_file="mmp/fast_dmm.db",
            enable_db_sync=True
        )
        
        # 验证关键映射
        test_studios = ['SSIS', 'GDRD', 'MIDV', 'CAWD']
        print("   🔍 验证关键映射:")
        
        for studio in test_studios:
            info = manager.get_mapping_info(studio)
            if info['has_mapping']:
                print(f"     {studio}: {info['primary']} ({info['type']})")
            else:
                print(f"     {studio}: ❌ 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 重建映射管理器失败: {e}")
        return False

def test_clean_search():
    """测试清理后的搜索"""
    print("\n🧪 测试清理后的搜索...")
    
    try:
        sys.path.append('.')
        from modules.fast_dmm_search import FastDMMSearch
        
        # 创建新的搜索引擎实例
        search_engine = FastDMMSearch(
            db_file="mmp/fast_dmm.db",
            config_file="mmp/dmm_studio_mappings.json"
        )
        
        # 测试用例
        test_cases = [
            ('SSIS-001', 'ssis00001'),
            ('GDRD-046', 'gdrd00046'),
            ('MIDV-123', 'midv00123')
        ]
        
        for code, expected_cid in test_cases:
            print(f"\n   测试: {code}")
            result = search_engine.intelligent_search(code)
            
            if result['success']:
                if result['is_multiple']:
                    print(f"     找到 {result['count']} 个映射:")
                    primary_result = None
                    for res in result['results']:
                        if hasattr(res, 'mapping_type') and res.mapping_type == 'primary':
                            primary_result = res
                            break
                    
                    if primary_result:
                        print(f"     主要结果: {primary_result.dmm_cid}")
                        if primary_result.dmm_cid == expected_cid:
                            print("     ✅ 映射正确！")
                        else:
                            print(f"     ⚠️ 期望: {expected_cid}")
                else:
                    if result['results']:
                        res = result['results'][0]
                        print(f"     结果: {res.dmm_cid}")
                        if res.dmm_cid == expected_cid:
                            print("     ✅ 映射正确！")
                        else:
                            print(f"     ⚠️ 期望: {expected_cid}")
            else:
                print(f"     ❌ 搜索失败: {result['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_missing_mappings_to_original_config():
    """添加缺失的映射到原配置"""
    print("\n🔧 添加缺失的映射到原配置...")
    
    try:
        import json
        
        # 加载原配置
        with open('mmp/dmm_studio_mappings.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        studio_mappings = config.get('studio_mappings', {})
        
        # 从新配置中获取缺失的映射
        with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
            new_config = json.load(f)
        
        new_mappings = new_config.get('mappings', {})
        
        # 添加缺失的重要映射
        important_studios = ['MOND', 'AVOP', 'HUNTB', 'JUL', 'ALDN', 'VRKM', 'SONE']
        added = 0
        
        for studio in important_studios:
            if studio not in studio_mappings and studio in new_mappings:
                primary_mapping = new_mappings[studio]['primary']
                studio_mappings[studio] = primary_mapping
                print(f"     添加: {studio} -> {primary_mapping}")
                added += 1
        
        if added > 0:
            # 保存更新后的配置
            config['studio_mappings'] = studio_mappings
            with open('mmp/dmm_studio_mappings.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 添加了 {added} 个映射到原配置")
            return True
        else:
            print("   ✅ 没有需要添加的映射")
            return False
            
    except Exception as e:
        print(f"❌ 添加映射失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ FastDMM缓存清理工具")
    print("=" * 50)
    
    # 步骤1: 清理缓存
    step1 = clear_fastdmm_cache()
    
    # 步骤2: 添加缺失的映射
    step2 = add_missing_mappings_to_original_config()
    
    # 步骤3: 重建映射管理器
    step3 = rebuild_mapping_manager()
    
    # 步骤4: 测试清理后的搜索
    if step1 and step3:
        step4 = test_clean_search()
    else:
        step4 = False
    
    print("\n" + "=" * 50)
    if step1 and step3 and step4:
        print("🎉 FastDMM缓存清理和重建完成！")
        print("\n📋 下一步:")
        print("1. 运行 python3 test_fast_dmm_updated.py 验证")
        print("2. 确认所有映射都正确")
        print("3. 继续系统集成")
    else:
        print("⚠️ 部分步骤失败，请检查错误信息")

if __name__ == "__main__":
    main()
