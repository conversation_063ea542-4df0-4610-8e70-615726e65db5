#!/usr/bin/env python3
"""
DMM原始工作版本 - 完全复制第一次成功的版本
"""
import sqlite3
import time
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

class DMMOriginalCrawler:
    """DMM原始爬虫 - 第一次成功的版本"""
    
    def __init__(self, db_file: str = "dmm_original_database.db"):
        self.db_file = db_file
        self.driver = None
        
        # 最简单的配置
        self.config = {
            'page_load_timeout': 30,
            'element_wait_timeout': 15,
        }
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        print("📊 初始化数据库...")
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dmm_works (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cid TEXT UNIQUE NOT NULL,
                    title TEXT,
                    number TEXT,
                    studio TEXT,
                    detail_url TEXT,
                    thumbnail_url TEXT,
                    page_number INTEGER,
                    crawl_time TEXT,
                    raw_html TEXT,
                    UNIQUE(cid)
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ 数据库初始化完成")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
    
    def init_driver(self):
        """初始化浏览器 - 最简单配置"""
        print("🚀 初始化浏览器...")
        
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            
            # 创建临时用户目录
            import tempfile
            temp_dir = tempfile.mkdtemp()
            chrome_options.add_argument(f'--user-data-dir={temp_dir}')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(self.config['page_load_timeout'])
            
            print("✅ 浏览器初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 浏览器初始化失败: {e}")
            return False
    
    def extract_work_info_from_element(self, element, page_number: int) -> Optional[Dict]:
        """从单个作品元素中提取信息 - 最简单版本"""
        try:
            work_info = {
                'page_number': page_number,
                'crawl_time': datetime.now().isoformat()
            }
            
            # 查找链接元素
            try:
                link_element = element.find_element(By.TAG_NAME, 'a')
                detail_url = link_element.get_attribute('href')
                
                if detail_url and 'cid=' in detail_url:
                    work_info['detail_url'] = detail_url
                    
                    # 从URL中提取CID
                    cid_match = re.search(r'cid=([^&/]+)', detail_url)
                    if cid_match:
                        work_info['cid'] = cid_match.group(1)
                    else:
                        return None
                else:
                    return None
                    
            except NoSuchElementException:
                return None
            
            # 提取标题
            try:
                title_element = element.find_element(By.CSS_SELECTOR, '[title], .title, .txt')
                title = title_element.get_attribute('title') or title_element.text
                if title:
                    work_info['title'] = title.strip()
            except NoSuchElementException:
                pass
            
            # 提取缩略图
            try:
                img_element = element.find_element(By.TAG_NAME, 'img')
                thumbnail_url = img_element.get_attribute('src')
                if thumbnail_url:
                    work_info['thumbnail_url'] = thumbnail_url
            except NoSuchElementException:
                pass
            
            # 从标题中提取番号
            title = work_info.get('title', '')
            if title:
                number_match = re.search(r'([A-Z]+[-_]?\d+)', title.upper())
                if number_match:
                    work_info['number'] = number_match.group(1)
            
            # 保存原始HTML
            work_info['raw_html'] = element.get_attribute('outerHTML')
            
            return work_info
            
        except Exception as e:
            return None
    
    def crawl_single_page(self, page_number: int) -> Tuple[bool, List[Dict]]:
        """爬取单页数据 - 最简单版本"""
        url = f"https://video.dmm.co.jp/av/list/?page={page_number}"
        
        try:
            print(f"   🔍 爬取第 {page_number} 页")
            
            # 访问页面
            self.driver.get(url)
            
            # 等待页面加载
            print(f"      ⏳ 等待页面加载...")
            time.sleep(10)  # 简单等待
            
            # 查找作品列表
            work_elements = self.driver.find_elements(By.CSS_SELECTOR, '[href*="cid="]')
            
            if work_elements:
                print(f"      ✅ 使用选择器 '[href*=\"cid=\"]' 找到 {len(work_elements)} 个元素")
                
                # 提取作品信息
                works_data = []
                
                for element in work_elements:
                    work_info = self.extract_work_info_from_element(element, page_number)
                    if work_info and work_info.get('cid'):
                        works_data.append(work_info)
                
                print(f"      ✅ 提取到 {len(works_data)} 个作品")
                return True, works_data
            else:
                print(f"      ❌ 未找到作品列表元素")
                return True, []
                
        except Exception as e:
            print(f"      ❌ 爬取异常: {e}")
            return False, []
    
    def save_works_to_db(self, works_data: List[Dict]):
        """保存作品数据到数据库"""
        if not works_data:
            return
        
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            for work in works_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO dmm_works 
                    (cid, title, number, studio, detail_url, thumbnail_url, 
                     page_number, crawl_time, raw_html)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    work.get('cid'),
                    work.get('title'),
                    work.get('number'),
                    work.get('studio'),
                    work.get('detail_url'),
                    work.get('thumbnail_url'),
                    work.get('page_number'),
                    work.get('crawl_time'),
                    work.get('raw_html')
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def crawl_first_page(self):
        """只爬取第一页进行测试"""
        print("🕷️ DMM原始爬虫测试")
        print("=" * 50)
        
        # 初始化浏览器
        if not self.init_driver():
            print("❌ 浏览器初始化失败")
            return
        
        try:
            # 爬取第1页
            print(f"\n📄 处理第 1 页")
            success, works_data = self.crawl_single_page(1)
            
            if success:
                # 保存数据
                self.save_works_to_db(works_data)
                print(f"   ✅ 第 1 页完成，保存 {len(works_data)} 个作品")
                
                # 显示样本
                if works_data:
                    print(f"\n📋 样本数据:")
                    for i, work in enumerate(works_data[:5], 1):
                        print(f"{i}. CID: {work.get('cid')} - {work.get('detail_url')}")
                
            else:
                print(f"   ❌ 第 1 页失败")
            
        finally:
            # 关闭浏览器
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")

def main():
    """主函数"""
    print("🧪 DMM原始工作版本测试")
    print("=" * 50)
    
    # 用户确认
    user_input = input("是否开始测试第一页爬取？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("测试已取消")
        return
    
    # 开始爬取
    crawler = DMMOriginalCrawler()
    crawler.crawl_first_page()

if __name__ == "__main__":
    main()
