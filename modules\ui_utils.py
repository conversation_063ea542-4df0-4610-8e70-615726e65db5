#!/usr/bin/env python3
"""
UI工具模块
提供Streamlit界面相关的工具函数
"""

import streamlit as st
from datetime import datetime


class UIUtils:
    """UI工具类"""
    
    @staticmethod
    def display_image_with_preview(image_bytes, title, image_type="poster", max_width=300):
        """
        显示带预览功能的图片 - 简化版本
        """
        if not image_bytes:
            return

        try:
            from modules.search_detail import SearchDetailModule
            search_module = SearchDetailModule()
            
            # 获取图片信息
            img_info = search_module.get_image_info(image_bytes)

            if img_info["success"]:
                # 显示图片信息
                st.caption(f"📏 {img_info['width']}×{img_info['height']} | 📦 {img_info['size_str']}")

            # 显示图片
            st.image(
                image_bytes,
                caption=title,
                width=max_width,
                use_container_width=False
            )

            # 下载按钮
            file_ext = "jpg" if image_type == "poster" else "jpg"
            filename = f"{title}_{image_type}.{file_ext}"
            
            st.download_button(
                label=f"💾 下载{title}",
                data=image_bytes,
                file_name=filename,
                mime="image/jpeg",
                use_container_width=True
            )

        except Exception as e:
            st.error(f"❌ 图片显示异常: {str(e)}")
    
    @staticmethod
    def add_event(event_type, status, detail="", progress=0, result=None):
        """添加事件到session state"""
        if "events" not in st.session_state:
            st.session_state.events = []
        
        st.session_state.events.append({
            "type": event_type,
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "status": status,  # 运行中、完成、失败
            "detail": detail,
            "progress": progress,
            "result": result
        })
        
        # 只保留最近100条
        if len(st.session_state.events) > 100:
            st.session_state.events = st.session_state.events[-100:]
    
    @staticmethod
    def update_last_event(status=None, progress=None, result=None):
        """更新最后一个事件"""
        if "events" not in st.session_state or not st.session_state.events:
            return
            
        if status is not None:
            st.session_state.events[-1]["status"] = status
        if progress is not None:
            st.session_state.events[-1]["progress"] = progress
        if result is not None:
            st.session_state.events[-1]["result"] = result
    
    @staticmethod
    def show_search_result_info(result):
        """显示搜索结果信息"""
        if not result or not result.get("success"):
            return
        
        # 基本信息
        info_data = {
            "番号": result["code"],
            "CID": result["cid"],
            "厂牌": result["label"],
            "详情页": result["url"]
        }

        for key, value in info_data.items():
            if value:
                if key == "详情页":
                    st.markdown(f"**{key}:** [点击访问]({value})")
                else:
                    st.markdown(f"**{key}:** {value}")

        # 显示保存状态
        if result.get("save_message"):
            st.info(result["save_message"])
    
    @staticmethod
    def show_poster_display(poster_data):
        """显示海报图片"""
        if not poster_data or not poster_data.get("success"):
            if poster_data:
                st.error(f"🖼️ 海报获取失败: {poster_data.get('message', '未知错误')}")
            return
        
        # 优先显示高清海报
        if poster_data.get("poster_bytes"):
            st.markdown("#### 🖼️ 高清海报")
            UIUtils.display_image_with_preview(
                poster_data["poster_bytes"],
                "高清海报",
                "poster",
                max_width=250
            )

        # 显示缩略图（如果有且没有高清海报）
        elif poster_data.get("thumb_bytes"):
            st.markdown("#### 🖼️ 缩略图")
            UIUtils.display_image_with_preview(
                poster_data["thumb_bytes"],
                "缩略图",
                "thumbnail",
                max_width=200
            )

        # 如果都有，显示缩略图选项
        if poster_data.get("poster_bytes") and poster_data.get("thumb_bytes"):
            st.markdown("---")
            st.markdown("#### 📱 缩略图版本")
            UIUtils.display_image_with_preview(
                poster_data["thumb_bytes"],
                "缩略图",
                "thumbnail_alt",
                max_width=180
            )

        if not poster_data.get("poster_bytes") and not poster_data.get("thumb_bytes"):
            st.warning("🖼️ 海报数据为空")
    
    @staticmethod
    def show_rename_section(search_result):
        """显示文件重命名区域"""
        if not search_result or not search_result.get("success"):
            return
        
        st.markdown("---")
        st.markdown("#### 📝 自动文件重命名")
        st.info("根据搜索结果自动重命名指定目录下的对应文件")

        col_dir, col_rename = st.columns([2, 1])

        with col_dir:
            target_dir = st.text_input(
                "📂 目标目录",
                value="/vol1/1000/CloudDrive/115open/R+18/待整理/刮削中/搬运测试",
                placeholder="输入包含视频文件的目录路径",
                key="rename_target_dir"
            )

        with col_rename:
            st.markdown("<br>", unsafe_allow_html=True)  # 对齐按钮
            if st.button("🚀 执行重命名", use_container_width=True):
                if target_dir.strip():
                    with st.spinner("🔄 正在重命名文件..."):
                        from modules.search_detail import SearchDetailModule
                        search_module = SearchDetailModule()
                        
                        rename_result = search_module.auto_rename_files_for_code(
                            search_result["code"],
                            target_dir.strip(),
                            search_result
                        )

                        if rename_result["success"]:
                            st.success(f"✅ {rename_result['message']}")
                            if rename_result.get("details"):
                                with st.expander("📋 详细重命名结果", expanded=True):
                                    for detail in rename_result["details"]:
                                        st.text(detail)
                        else:
                            st.error(f"❌ {rename_result['message']}")
                else:
                    st.warning("请输入目标目录！")
