#!/usr/bin/env python3
"""
快速测试应用 - 验证搜索详情页模块化功能
"""

import streamlit as st

# 配置页面
st.set_page_config(
    page_title="MMA Pro - 模块化测试",
    page_icon="🔍",
    layout="wide"
)

def main():
    """主函数"""
    st.title("🔍 搜索详情页模块化测试")

    # 使用session state管理状态
    if "use_modular" not in st.session_state:
        st.session_state.use_modular = True

    # 侧边栏控制
    with st.sidebar:
        st.markdown("### 🔧 版本控制")

        # 显示当前版本
        if st.session_state.use_modular:
            st.success("✅ 当前: 模块化版本")
        else:
            st.info("📜 当前: 原版功能")

        # 切换按钮
        if st.button("🔄 切换版本"):
            st.session_state.use_modular = not st.session_state.use_modular
            st.rerun()
    
    # 主内容区域
    st.markdown("---")
    
    if st.session_state.use_modular:
        render_modular_version()
    else:
        render_original_version()

def render_modular_version():
    """渲染模块化版本"""
    try:
        from modules.search_detail_ui import SearchDetailUI
        
        st.success("🔧 使用模块化版本")
        st.info("模块加载成功，功能完整可用")
        
        # 渲染模块化UI
        search_ui = SearchDetailUI()
        search_ui.render()
        
    except ImportError as e:
        st.error(f"❌ 模块导入失败: {e}")
        st.warning("自动切换到原版功能")
        render_original_version()
    except Exception as e:
        st.error(f"❌ 模块运行异常: {e}")
        st.warning("自动切换到原版功能")
        render_original_version()

def render_original_version():
    """渲染原版功能"""
    st.info("📜 使用原版功能")
    
    st.markdown("### 🔍 搜索详情页 (原版)")
    st.info("这是原版的搜索详情页功能演示")
    
    # 简化的原版功能演示
    col1, col2 = st.columns([3, 1])
    
    with col1:
        code = st.text_input("📝 请输入番号", placeholder="IPX-123")
    
    with col2:
        st.markdown("**功能选项：**")
        show_poster = st.checkbox("🖼️ 显示海报", value=True)
        auto_save = st.checkbox("💾 自动保存", value=True)
    
    if st.button("🔍 搜索 (原版演示)", type="primary"):
        if code.strip():
            st.success(f"✅ 原版搜索演示: {code}")
            st.info(f"海报显示: {'开启' if show_poster else '关闭'}")
            st.info(f"自动保存: {'开启' if auto_save else '关闭'}")
        else:
            st.warning("请输入番号！")

if __name__ == "__main__":
    main()
