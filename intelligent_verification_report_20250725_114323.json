{"summary": {"total_studios": 40, "total_prefixes": 43, "total_valid_prefixes": 40, "overall_success_rate": 0.9302325581395349, "verification_time": 114.62847900390625, "total_requests": 74, "avg_requests_per_minute": 38.733829835155504, "timestamp": "2025-07-25T11:43:23.412160"}, "status_distribution": {"excellent": 37, "good": 2, "poor": 0, "failed": 1, "error": 0, "no_mappings": 0}, "range_discoveries": {"0-99": ["GDRD:gdrd", "NSFS:nsfs", "GMA:gma", "GAJK:gajk", "NGHJ:nghj", "DNJR:dnjr", "HOWS:hows", "CHRV:chrv", "MOND:18mond", "ALDN:aldn", "DGCEMD:dgcemd", "CEMD:cemd", "EKDV:49ekdv", "CRNX:crnx", "MKON:mkon", "URKK:urkk", "MMUS:mmus", "KNIP:knip", "PFAS:pfas", "BASJ:basj", "BAGR:bagr", "BACJ:bacj", "AVSA:avsa", "SONE:sone", "TEK:tek", "ROE:roe", "JUR:jur", "ACHJ:achj", "IPZZ:ipzz", "SVCAO:1svcao", "START:1start", "SDMM:1sdmm", "SDHS:1sdhs", "SDAB:1sdab", "MDON:mdon", "HZGD:h_1100hzgd", "NACR:h_237nacr"], "200-299": ["MOND:mond", "MGT:h_1711mgt"], "100-199": ["SDJS:1sdjs"]}, "intelligent_config": {"min_delay": 0.1, "max_delay": 0.5, "max_requests_per_minute": 80, "batch_size": 25, "batch_delay": 15, "test_numbers": [1, 50, 62, 100, 200, 275, 500, 775, 1000, 1091, 1200], "max_tests_per_prefix": 6, "early_stop": true}, "detailed_results": {"GDRD": {"studio": "GDRD", "total_prefixes": 1, "valid_prefixes": ["gdrd"], "invalid_prefixes": [], "prefix_details": {"gdrd": {"prefix": "gdrd", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.665201425552368, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "NSFS": {"studio": "NSFS", "total_prefixes": 1, "valid_prefixes": ["nsfs"], "invalid_prefixes": [], "prefix_details": {"nsfs": {"prefix": "nsfs", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.463449001312256, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "GMA": {"studio": "GMA", "total_prefixes": 1, "valid_prefixes": ["gma"], "invalid_prefixes": [], "prefix_details": {"gma": {"prefix": "gma", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.3146119117736816, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "GAJK": {"studio": "GAJK", "total_prefixes": 1, "valid_prefixes": ["gajk"], "invalid_prefixes": [], "prefix_details": {"gajk": {"prefix": "gajk", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.5581779479980469, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "NGHJ": {"studio": "NGHJ", "total_prefixes": 1, "valid_prefixes": ["nghj"], "invalid_prefixes": [], "prefix_details": {"nghj": {"prefix": "nghj", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.9298889636993408, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "DNJR": {"studio": "DNJR", "total_prefixes": 1, "valid_prefixes": ["dnjr"], "invalid_prefixes": [], "prefix_details": {"dnjr": {"prefix": "dnjr", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.0536556243896484, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "HOWS": {"studio": "HOWS", "total_prefixes": 1, "valid_prefixes": ["hows"], "invalid_prefixes": [], "prefix_details": {"hows": {"prefix": "hows", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.554530382156372, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "CHRV": {"studio": "CHRV", "total_prefixes": 1, "valid_prefixes": ["chrv"], "invalid_prefixes": [], "prefix_details": {"chrv": {"prefix": "chrv", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.4445395469665527, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MOND": {"studio": "MOND", "total_prefixes": 2, "valid_prefixes": ["mond", "18mond"], "invalid_prefixes": [], "prefix_details": {"mond": {"prefix": "mond", "successful_numbers": [275], "failed_numbers": [1, 50, 62, 100, 200], "total_tests": 6, "success_count": 1, "is_valid": true, "confidence": 0.16666666666666666}, "18mond": {"prefix": "18mond", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 7.004512786865234, "status": "excellent", "total_requests": 7, "verification_method": "intelligent_range_probing"}, "ALDN": {"studio": "ALDN", "total_prefixes": 1, "valid_prefixes": ["aldn"], "invalid_prefixes": [], "prefix_details": {"aldn": {"prefix": "aldn", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.156116247177124, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "DGCEMD": {"studio": "DGCEMD", "total_prefixes": 1, "valid_prefixes": ["dgcemd"], "invalid_prefixes": [], "prefix_details": {"dgcemd": {"prefix": "dgcemd", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.1643741130828857, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "CEMD": {"studio": "CEMD", "total_prefixes": 1, "valid_prefixes": ["cemd"], "invalid_prefixes": [], "prefix_details": {"cemd": {"prefix": "cemd", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.477428913116455, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "EKDV": {"studio": "EKDV", "total_prefixes": 2, "valid_prefixes": ["49ekdv"], "invalid_prefixes": ["ekdv"], "prefix_details": {"ekdv": {"prefix": "ekdv", "successful_numbers": [], "failed_numbers": [1, 50, 62, 100, 200, 275], "total_tests": 6, "success_count": 0, "is_valid": false, "confidence": 0.0}, "49ekdv": {"prefix": "49ekdv", "successful_numbers": [50], "failed_numbers": [1], "total_tests": 2, "success_count": 1, "is_valid": true, "confidence": 0.5}}, "success_rate": 0.5, "verification_time": 7.053356885910034, "status": "good", "total_requests": 8, "verification_method": "intelligent_range_probing"}, "CRNX": {"studio": "CRNX", "total_prefixes": 1, "valid_prefixes": ["crnx"], "invalid_prefixes": [], "prefix_details": {"crnx": {"prefix": "crnx", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 0.9542465209960938, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MKON": {"studio": "MKON", "total_prefixes": 1, "valid_prefixes": ["mkon"], "invalid_prefixes": [], "prefix_details": {"mkon": {"prefix": "mkon", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.4896228313446045, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "URKK": {"studio": "URKK", "total_prefixes": 1, "valid_prefixes": ["urkk"], "invalid_prefixes": [], "prefix_details": {"urkk": {"prefix": "urkk", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.193094253540039, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MMUS": {"studio": "MMUS", "total_prefixes": 1, "valid_prefixes": ["mmus"], "invalid_prefixes": [], "prefix_details": {"mmus": {"prefix": "mmus", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.60410737991333, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "KNIP": {"studio": "KNIP", "total_prefixes": 1, "valid_prefixes": ["knip"], "invalid_prefixes": [], "prefix_details": {"knip": {"prefix": "knip", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.3821544647216797, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "PFAS": {"studio": "PFAS", "total_prefixes": 1, "valid_prefixes": ["pfas"], "invalid_prefixes": [], "prefix_details": {"pfas": {"prefix": "pfas", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.2772696018218994, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MGT": {"studio": "MGT", "total_prefixes": 2, "valid_prefixes": ["h_1711mgt"], "invalid_prefixes": ["118mgt"], "prefix_details": {"118mgt": {"prefix": "118mgt", "successful_numbers": [], "failed_numbers": [1, 50, 62, 100, 200, 275], "total_tests": 6, "success_count": 0, "is_valid": false, "confidence": 0.0}, "h_1711mgt": {"prefix": "h_1711mgt", "successful_numbers": [200], "failed_numbers": [1, 50, 62, 100], "total_tests": 5, "success_count": 1, "is_valid": true, "confidence": 0.2}}, "success_rate": 0.5, "verification_time": 8.885839700698853, "status": "good", "total_requests": 11, "verification_method": "intelligent_range_probing"}, "MAAN": {"studio": "MAAN", "total_prefixes": 1, "valid_prefixes": [], "invalid_prefixes": ["h_1711maan"], "prefix_details": {"h_1711maan": {"prefix": "h_1711maan", "successful_numbers": [], "failed_numbers": [1, 50, 62, 100, 200, 275], "total_tests": 6, "success_count": 0, "is_valid": false, "confidence": 0.0}}, "success_rate": 0.0, "verification_time": 3.863165855407715, "status": "failed", "total_requests": 6, "verification_method": "intelligent_range_probing"}, "BASJ": {"studio": "BASJ", "total_prefixes": 1, "valid_prefixes": ["basj"], "invalid_prefixes": [], "prefix_details": {"basj": {"prefix": "basj", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.1399590969085693, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "BAGR": {"studio": "BAGR", "total_prefixes": 1, "valid_prefixes": ["bagr"], "invalid_prefixes": [], "prefix_details": {"bagr": {"prefix": "bagr", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.331936836242676, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "BACJ": {"studio": "BACJ", "total_prefixes": 1, "valid_prefixes": ["bacj"], "invalid_prefixes": [], "prefix_details": {"bacj": {"prefix": "bacj", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.224679470062256, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "AVSA": {"studio": "AVSA", "total_prefixes": 1, "valid_prefixes": ["avsa"], "invalid_prefixes": [], "prefix_details": {"avsa": {"prefix": "avsa", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.1902251243591309, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "SONE": {"studio": "SONE", "total_prefixes": 1, "valid_prefixes": ["sone"], "invalid_prefixes": [], "prefix_details": {"sone": {"prefix": "sone", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.316352605819702, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "TEK": {"studio": "TEK", "total_prefixes": 1, "valid_prefixes": ["tek"], "invalid_prefixes": [], "prefix_details": {"tek": {"prefix": "tek", "successful_numbers": [50], "failed_numbers": [1], "total_tests": 2, "success_count": 1, "is_valid": true, "confidence": 0.5}}, "success_rate": 1.0, "verification_time": 3.6952340602874756, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "ROE": {"studio": "ROE", "total_prefixes": 1, "valid_prefixes": ["roe"], "invalid_prefixes": [], "prefix_details": {"roe": {"prefix": "roe", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.7648406028747559, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "JUR": {"studio": "JUR", "total_prefixes": 1, "valid_prefixes": ["jur"], "invalid_prefixes": [], "prefix_details": {"jur": {"prefix": "jur", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.334695339202881, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "ACHJ": {"studio": "ACHJ", "total_prefixes": 1, "valid_prefixes": ["achj"], "invalid_prefixes": [], "prefix_details": {"achj": {"prefix": "achj", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.4282913208007812, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "IPZZ": {"studio": "IPZZ", "total_prefixes": 1, "valid_prefixes": ["ipzz"], "invalid_prefixes": [], "prefix_details": {"ipzz": {"prefix": "ipzz", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.0528693199157715, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "SVCAO": {"studio": "SVCAO", "total_prefixes": 1, "valid_prefixes": ["1svcao"], "invalid_prefixes": [], "prefix_details": {"1svcao": {"prefix": "1svcao", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.1790189743041992, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "START": {"studio": "START", "total_prefixes": 1, "valid_prefixes": ["1start"], "invalid_prefixes": [], "prefix_details": {"1start": {"prefix": "1start", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 2.768829107284546, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "SDMM": {"studio": "SDMM", "total_prefixes": 1, "valid_prefixes": ["1sdmm"], "invalid_prefixes": [], "prefix_details": {"1sdmm": {"prefix": "1sdmm", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.569957971572876, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "SDJS": {"studio": "SDJS", "total_prefixes": 1, "valid_prefixes": ["1sdjs"], "invalid_prefixes": [], "prefix_details": {"1sdjs": {"prefix": "1sdjs", "successful_numbers": [100], "failed_numbers": [1, 50, 62], "total_tests": 4, "success_count": 1, "is_valid": true, "confidence": 0.25}}, "success_rate": 1.0, "verification_time": 4.662522792816162, "status": "excellent", "total_requests": 4, "verification_method": "intelligent_range_probing"}, "SDHS": {"studio": "SDHS", "total_prefixes": 1, "valid_prefixes": ["1sdhs"], "invalid_prefixes": [], "prefix_details": {"1sdhs": {"prefix": "1sdhs", "successful_numbers": [50], "failed_numbers": [1], "total_tests": 2, "success_count": 1, "is_valid": true, "confidence": 0.5}}, "success_rate": 1.0, "verification_time": 2.5396835803985596, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}, "SDAB": {"studio": "SDAB", "total_prefixes": 1, "valid_prefixes": ["1sdab"], "invalid_prefixes": [], "prefix_details": {"1sdab": {"prefix": "1sdab", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.2595477104187012, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "MDON": {"studio": "MDON", "total_prefixes": 1, "valid_prefixes": ["mdon"], "invalid_prefixes": [], "prefix_details": {"mdon": {"prefix": "mdon", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.21518874168396, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "HZGD": {"studio": "HZGD", "total_prefixes": 1, "valid_prefixes": ["h_1100hzgd"], "invalid_prefixes": [], "prefix_details": {"h_1100hzgd": {"prefix": "h_1100hzgd", "successful_numbers": [1], "failed_numbers": [], "total_tests": 1, "success_count": 1, "is_valid": true, "confidence": 1.0}}, "success_rate": 1.0, "verification_time": 1.924358606338501, "status": "excellent", "total_requests": 1, "verification_method": "intelligent_range_probing"}, "NACR": {"studio": "NACR", "total_prefixes": 1, "valid_prefixes": ["h_237nacr"], "invalid_prefixes": [], "prefix_details": {"h_237nacr": {"prefix": "h_237nacr", "successful_numbers": [50], "failed_numbers": [1], "total_tests": 2, "success_count": 1, "is_valid": true, "confidence": 0.5}}, "success_rate": 1.0, "verification_time": 2.5331296920776367, "status": "excellent", "total_requests": 2, "verification_method": "intelligent_range_probing"}}}