#!/usr/bin/env python3
"""
科学的数据库映射验证工具
使用多番号验证法，避免误判
"""
import sys
import json
import time
import random
from datetime import datetime
from typing import Dict, List, Tuple
sys.path.append('.')

class ScientificVerifier:
    """科学验证器"""
    
    def __init__(self):
        self.crawler = None
        self.verification_results = {}
        self.start_time = None
        self.request_count = 0
        self.last_request_time = 0
        
        # 科学验证配置
        self.config = {
            'min_delay': 3.0,      # 最小请求间隔（秒）
            'max_delay': 6.0,      # 最大请求间隔（秒）
            'batch_size': 15,      # 批次大小
            'batch_delay': 60,     # 批次间休息时间（秒）
            'max_requests_per_minute': 8,  # 每分钟最大请求数
            'error_threshold': 3,   # 连续错误阈值
            'cooldown_time': 300,   # 冷却时间（秒）
            
            # 科学验证参数
            'test_numbers': [1, 100, 21, 123, 2, 500, 200, 999, 50, 300],  # 测试番号列表
            'min_success_for_valid': 1,     # 至少成功1个就认为有效
            'max_tests_per_prefix': 5,      # 每个前缀最多测试5个番号
            'early_stop_on_success': True,  # 成功一个就停止测试该前缀
        }
        
        self.consecutive_errors = 0
        self.request_times = []
    
    def safe_delay(self):
        """安全延迟"""
        delay = random.uniform(self.config['min_delay'], self.config['max_delay'])
        print(f"         😴 等待 {delay:.1f} 秒...")
        time.sleep(delay)
        
        current_time = time.time()
        self.request_times.append(current_time)
        self.last_request_time = current_time
        self.request_count += 1
        
        # 清理1分钟前的记录
        one_minute_ago = current_time - 60
        self.request_times = [t for t in self.request_times if t > one_minute_ago]
    
    def check_rate_limit(self):
        """频率检查"""
        current_time = time.time()
        one_minute_ago = current_time - 60
        self.request_times = [t for t in self.request_times if t > one_minute_ago]
        
        recent_requests = len(self.request_times)
        
        if recent_requests >= self.config['max_requests_per_minute']:
            print(f"⚠️ 最近1分钟请求过多: {recent_requests}/{self.config['max_requests_per_minute']}")
            print(f"😴 等待60秒...")
            time.sleep(60)
            return True
        
        return True
    
    def initialize_crawler(self):
        """初始化DMM爬虫"""
        print("🔧 初始化DMM爬虫...")
        
        try:
            from modules.dmm_search_crawler import DMMSearchCrawler
            
            self.crawler = DMMSearchCrawler()
            
            if not self.crawler.age_verified:
                print("❌ 年龄验证失败")
                return False
            
            print("✅ DMM爬虫初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 爬虫初始化失败: {e}")
            return False
    
    def load_database_mappings(self):
        """加载数据库映射"""
        print("📖 加载数据库映射...")
        
        try:
            with open('studio_mappings_all.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'mappings' not in data:
                print("❌ 映射数据格式错误")
                return None
            
            mappings = data['mappings']
            print(f"✅ 加载了 {len(mappings)} 个厂商映射")
            
            return mappings
            
        except Exception as e:
            print(f"❌ 加载映射数据失败: {e}")
            return None
    
    def scientific_verify_prefix(self, prefix: str) -> Dict:
        """科学验证单个前缀"""
        result = {
            'prefix': prefix,
            'tested_numbers': [],
            'successful_numbers': [],
            'failed_numbers': [],
            'total_tests': 0,
            'success_count': 0,
            'is_valid': False,
            'confidence': 0.0
        }
        
        print(f"      🧪 科学验证前缀: {prefix}")
        
        test_numbers = self.config['test_numbers'][:self.config['max_tests_per_prefix']]
        
        for test_num in test_numbers:
            try:
                # 检查频率限制
                self.check_rate_limit()
                
                # 构建测试CID
                test_cid = f"{prefix}{test_num:05d}"
                detail_url = f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={test_cid}/"
                
                print(f"         🔍 测试: {test_cid}")
                
                # 安全延迟
                self.safe_delay()
                
                # 验证CID
                if self.crawler._verify_cid_exists(detail_url, test_cid):
                    result['successful_numbers'].append(test_num)
                    result['success_count'] += 1
                    print(f"         ✅ 成功: {test_cid}")
                    
                    # 早停策略：成功一个就够了
                    if self.config['early_stop_on_success']:
                        print(f"         🎯 早停：已验证前缀有效")
                        break
                else:
                    result['failed_numbers'].append(test_num)
                    print(f"         ❌ 失败: {test_cid}")
                
                result['tested_numbers'].append(test_num)
                result['total_tests'] += 1
                
            except Exception as e:
                print(f"         ⚠️ 测试异常: {e}")
                result['failed_numbers'].append(test_num)
                result['total_tests'] += 1
                continue
        
        # 判断前缀是否有效
        result['is_valid'] = result['success_count'] >= self.config['min_success_for_valid']
        
        # 计算置信度
        if result['total_tests'] > 0:
            result['confidence'] = result['success_count'] / result['total_tests']
        
        print(f"      📊 前缀验证结果: {prefix}")
        print(f"         测试数: {result['total_tests']}")
        print(f"         成功数: {result['success_count']}")
        print(f"         有效性: {'✅ 有效' if result['is_valid'] else '❌ 无效'}")
        print(f"         置信度: {result['confidence']:.2f}")
        
        return result
    
    def verify_studio_mapping(self, studio: str, mapping_data: Dict) -> Dict:
        """科学验证单个厂商的映射"""
        print(f"\n🔬 科学验证厂商: {studio}")
        
        result = {
            'studio': studio,
            'total_prefixes': 0,
            'valid_prefixes': [],
            'invalid_prefixes': [],
            'prefix_details': {},
            'success_rate': 0.0,
            'verification_time': 0,
            'status': 'unknown',
            'total_requests': 0
        }
        
        try:
            start_time = time.time()
            
            # 获取映射信息
            primary = mapping_data.get('primary', '')
            alternatives = mapping_data.get('alternatives', [])
            
            all_prefixes = [primary] + alternatives if primary else alternatives
            result['total_prefixes'] = len(all_prefixes)
            
            print(f"   总前缀数: {len(all_prefixes)}")
            print(f"   科学验证策略:")
            print(f"      测试番号: {self.config['test_numbers'][:self.config['max_tests_per_prefix']]}")
            print(f"      每前缀最多测试: {self.config['max_tests_per_prefix']} 个")
            print(f"      成功阈值: {self.config['min_success_for_valid']} 个")
            print(f"      早停策略: {'开启' if self.config['early_stop_on_success'] else '关闭'}")
            
            if not all_prefixes:
                result['status'] = 'no_mappings'
                return result
            
            request_count_start = self.request_count
            
            # 科学验证每个前缀
            for prefix in all_prefixes:
                prefix_result = self.scientific_verify_prefix(prefix)
                result['prefix_details'][prefix] = prefix_result
                
                if prefix_result['is_valid']:
                    result['valid_prefixes'].append(prefix)
                else:
                    result['invalid_prefixes'].append(prefix)
            
            result['total_requests'] = self.request_count - request_count_start
            
            # 计算成功率
            valid_count = len(result['valid_prefixes'])
            total_count = len(all_prefixes)
            result['success_rate'] = valid_count / total_count if total_count > 0 else 0
            
            # 确定状态
            if result['success_rate'] >= 0.8:
                result['status'] = 'excellent'
            elif result['success_rate'] >= 0.5:
                result['status'] = 'good'
            elif result['success_rate'] >= 0.2:
                result['status'] = 'poor'
            else:
                result['status'] = 'failed'
            
            result['verification_time'] = time.time() - start_time
            
            print(f"   📊 科学验证完成:")
            print(f"      有效前缀: {valid_count}/{total_count} ({result['success_rate']:.1%})")
            print(f"      总请求数: {result['total_requests']}")
            print(f"      验证状态: {result['status']}")
            
            return result
            
        except Exception as e:
            print(f"   ❌ 验证异常: {e}")
            result['status'] = 'error'
            result['error'] = str(e)
            return result
    
    def scientific_batch_verification(self, mappings: Dict, start_index: int = 0, limit: int = None) -> Dict:
        """科学批量验证"""
        print(f"\n🔬 开始科学批量验证...")
        
        self.start_time = time.time()
        studios = list(mappings.keys())[start_index:]
        
        if limit:
            studios = studios[:limit]
            print(f"⚠️ 限制验证数量: {limit} 个厂商")
        
        print(f"📋 计划验证 {len(studios)} 个厂商")
        print(f"🔬 科学验证配置:")
        print(f"   请求间隔: {self.config['min_delay']}-{self.config['max_delay']} 秒")
        print(f"   批次大小: {self.config['batch_size']}")
        print(f"   最大频率: {self.config['max_requests_per_minute']} 请求/分钟")
        print(f"   测试番号: {self.config['test_numbers']}")
        print(f"   验证策略: 多番号验证，避免误判")
        
        for i, studio in enumerate(studios, 1):
            print(f"\n{'='*60}")
            print(f"进度: {i}/{len(studios)} ({i/len(studios):.1%})")
            
            # 批次休息
            if i > 1 and (i - 1) % self.config['batch_size'] == 0:
                print(f"😴 批次休息 {self.config['batch_delay']} 秒...")
                time.sleep(self.config['batch_delay'])
            
            mapping_data = mappings[studio]
            result = self.verify_studio_mapping(studio, mapping_data)
            self.verification_results[studio] = result
            
            # 显示进度和统计
            elapsed = time.time() - self.start_time
            avg_time = elapsed / i
            remaining = (len(studios) - i) * avg_time
            
            print(f"⏱️ 已用时: {elapsed:.1f}s, 预计剩余: {remaining:.1f}s")
            print(f"📡 总请求数: {self.request_count}")
            print(f"📊 最近1分钟请求: {len(self.request_times)}")
            
            # 检查是否需要停止
            if self.consecutive_errors >= self.config['error_threshold']:
                print("🚨 错误过多，停止验证")
                break
        
        return self.verification_results
    
    def generate_scientific_report(self) -> Dict:
        """生成科学验证报告"""
        print(f"\n📊 生成科学验证报告...")
        
        if not self.verification_results:
            return {}
        
        total_studios = len(self.verification_results)
        total_time = time.time() - self.start_time if self.start_time else 0
        
        # 统计各种状态
        status_counts = {
            'excellent': 0,
            'good': 0,
            'poor': 0,
            'failed': 0,
            'error': 0,
            'no_mappings': 0
        }
        
        total_prefixes = 0
        total_valid_prefixes = 0
        total_requests = 0
        
        # 详细统计
        test_number_stats = {}
        for num in self.config['test_numbers']:
            test_number_stats[num] = {'success': 0, 'total': 0}
        
        for studio, result in self.verification_results.items():
            status = result.get('status', 'unknown')
            if status in status_counts:
                status_counts[status] += 1
            
            total_prefixes += result.get('total_prefixes', 0)
            total_valid_prefixes += len(result.get('valid_prefixes', []))
            total_requests += result.get('total_requests', 0)
            
            # 统计测试番号效果
            for prefix, details in result.get('prefix_details', {}).items():
                for test_num in details.get('tested_numbers', []):
                    if test_num in test_number_stats:
                        test_number_stats[test_num]['total'] += 1
                        if test_num in details.get('successful_numbers', []):
                            test_number_stats[test_num]['success'] += 1
        
        overall_success_rate = total_valid_prefixes / total_prefixes if total_prefixes > 0 else 0
        
        report = {
            'summary': {
                'total_studios': total_studios,
                'total_prefixes': total_prefixes,
                'total_valid_prefixes': total_valid_prefixes,
                'overall_success_rate': overall_success_rate,
                'verification_time': total_time,
                'total_requests': total_requests,
                'avg_requests_per_minute': (total_requests / (total_time / 60)) if total_time > 0 else 0,
                'timestamp': datetime.now().isoformat()
            },
            'status_distribution': status_counts,
            'test_number_effectiveness': test_number_stats,
            'scientific_config': self.config,
            'detailed_results': self.verification_results
        }
        
        return report
    
    def print_scientific_report(self, report: Dict):
        """打印科学验证报告"""
        if not report:
            print("❌ 无报告数据")
            return
        
        summary = report['summary']
        status_dist = report['status_distribution']
        test_stats = report['test_number_effectiveness']
        
        print(f"\n" + "="*70)
        print(f"📊 科学数据库映射验证报告")
        print(f"="*70)
        
        print(f"\n📈 总体统计:")
        print(f"   验证厂商: {summary['total_studios']} 个")
        print(f"   总前缀数: {summary['total_prefixes']} 个")
        print(f"   有效前缀: {summary['total_valid_prefixes']} 个")
        print(f"   总成功率: {summary['overall_success_rate']:.1%}")
        print(f"   验证耗时: {summary['verification_time']:.1f} 秒")
        print(f"   总请求数: {summary['total_requests']}")
        print(f"   平均频率: {summary['avg_requests_per_minute']:.1f} 请求/分钟")
        
        print(f"\n📋 厂商状态分布:")
        print(f"   🟢 优秀 (≥80%): {status_dist['excellent']} 个")
        print(f"   🟡 良好 (≥50%): {status_dist['good']} 个") 
        print(f"   🟠 较差 (≥20%): {status_dist['poor']} 个")
        print(f"   🔴 失败 (<20%): {status_dist['failed']} 个")
        print(f"   ❌ 异常: {status_dist['error']} 个")
        print(f"   ⚪ 无映射: {status_dist['no_mappings']} 个")
        
        print(f"\n🧪 测试番号效果分析:")
        for test_num, stats in sorted(test_stats.items()):
            if stats['total'] > 0:
                success_rate = stats['success'] / stats['total']
                print(f"   番号 {test_num:3d}: {stats['success']:2d}/{stats['total']:2d} ({success_rate:.1%}) {'🎯' if success_rate > 0.5 else '📊' if success_rate > 0.2 else '❌'}")
        
        print(f"\n💡 科学验证优势:")
        print(f"   ✅ 避免误判：使用多个测试番号")
        print(f"   ✅ 提高准确性：只要有一个番号成功就认为映射有效")
        print(f"   ✅ 效率优化：成功一个就停止测试该前缀")
        print(f"   ✅ 数据分析：统计哪些测试番号最有效")

def main():
    """主函数"""
    print("🔬 科学数据库映射验证工具")
    print("=" * 70)
    
    print("🧪 科学验证特性:")
    print("   - 多番号验证法：避免固定番号21的误判问题")
    print("   - 智能早停策略：成功一个就停止，提高效率")
    print("   - 效果分析：统计哪些测试番号最有效")
    print("   - 准确判断：只要有一个番号成功就认为映射有效")
    
    # 用户确认
    user_input = input("\n是否开始科学验证？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("验证已取消")
        return
    
    # 询问验证数量限制
    limit_input = input("验证数量限制 (建议10-20，回车=10): ").strip()
    limit = 10  # 默认10个
    
    if limit_input.isdigit():
        limit = int(limit_input)
    
    print(f"🎯 将科学验证 {limit} 个厂商")
    
    # 执行验证
    verifier = ScientificVerifier()
    
    # 1. 初始化爬虫
    if not verifier.initialize_crawler():
        print("❌ 爬虫初始化失败，验证终止")
        return
    
    # 2. 加载映射数据
    mappings = verifier.load_database_mappings()
    if not mappings:
        print("❌ 映射数据加载失败，验证终止")
        return
    
    # 3. 执行科学验证
    print(f"\n🔬 开始科学验证...")
    results = verifier.scientific_batch_verification(mappings, limit=limit)
    
    # 4. 生成报告
    report = verifier.generate_scientific_report()
    
    # 5. 显示报告
    verifier.print_scientific_report(report)
    
    # 6. 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"scientific_verification_report_{timestamp}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细报告已保存: {report_file}")
    except Exception as e:
        print(f"\n❌ 报告保存失败: {e}")

if __name__ == "__main__":
    main()
